{"name": "@apify/consts", "version": "2.41.0", "description": "Tools and constants shared across Apify projects.", "main": "./cjs/index.cjs", "module": "./esm/index.mjs", "typings": "./cjs/index.d.ts", "exports": {".": {"import": {"types": "./esm/index.d.mts", "default": "./esm/index.mjs"}, "require": {"types": "./cjs/index.d.ts", "default": "./cjs/index.cjs"}}}, "keywords": ["apify"], "author": {"name": "Apify", "email": "<EMAIL>", "url": "https://apify.com"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/apify/apify-shared-js"}, "bugs": {"url": "https://github.com/apify/apify-shared-js/issues"}, "homepage": "https://apify.com", "scripts": {"build": "npm run clean && npm run compile && npm run copy", "clean": "rimraf ./dist", "compile": "tsup", "copy": "ts-node -T ../../scripts/copy.ts"}, "publishConfig": {"access": "public"}, "gitHead": "e98bc7ac677b4f748930ffb38c587e2e59781a1b"}