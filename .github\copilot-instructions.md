# Copilot 自定义指令

<!-- Use this file to provide workspace-specific custom instructions to Copilot. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

所有对话中文回复
每一句代码都要有中文注释，包括逻辑，代码实现原理
项目开发为C#开发
灵活运用MCP工具
测试成功以后记得删除测试文件，并更新主文件
每一句代码都需要添加调试信息方便回溯问题
如果在原功能文件基础上创建了新的功能文件那么应该删除旧文件
修改或者更新完成以后检查整个项目目录是否有重复文件以及没用的文件，并清理整理，所有文件夹以及文件
当修改代码修复问题更新功能的时候应该要考虑到所有代码关联的地方
我已经安装.net sdk
我已经安装node.js
标记“&&”不是此版本中的有效语句分隔符。
输入终端命令以后查看终端是否已经执行 执行完了继续下一步
如果有错误请修复错误以后再继续下一步
时刻整理所有文件夹，文件
PowerShell不支持&&分隔符
编译位置为D:\IIIIII\BatchBrowserUI\bin\Debug\net9.0-windows