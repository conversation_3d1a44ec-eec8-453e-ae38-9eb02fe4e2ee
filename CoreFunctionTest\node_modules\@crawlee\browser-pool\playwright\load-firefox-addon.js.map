{"version": 3, "file": "load-firefox-addon.js", "sourceRoot": "", "sources": ["../../src/playwright/load-firefox-addon.ts"], "names": [], "mappings": ";;;;AAAA,6CAAqC;AACrC,gEAA2B;AAEpB,MAAM,gBAAgB,GAAG,KAAK,EAAE,IAAY,EAAE,IAAY,EAAE,SAAiB,EAAE,EAAE;IACpF,OAAO,IAAI,OAAO,CAAU,CAAC,OAAO,EAAE,EAAE;QACpC,MAAM,MAAM,GAAG,kBAAG,CAAC,OAAO,CAAC;YACvB,IAAI;YACJ,IAAI;SACP,CAAC,CAAC;QAEH,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAC/B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;YACtB,OAAO,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,GAAG,CAAC,IAA4B,EAAE,EAAE;YAC1C,MAAM,GAAG,GAAG,oBAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;YAE9C,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YAC9B,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC,CAAC;QAEF,IAAI,CAAC;YACD,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,SAAS;SAClB,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,CAAC,OAAY,EAAE,EAAE;YAC/B,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACtB,IAAI,CAAC;oBACD,EAAE,EAAE,OAAO,CAAC,WAAW;oBACvB,IAAI,EAAE,uBAAuB;oBAC7B,SAAS;iBACZ,CAAC,CAAC;YACP,CAAC;YAED,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBAChB,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM,CAAC,GAAG,EAAE,CAAC;YACjB,CAAC;YAED,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBAChB,MAAM,CAAC,GAAG,EAAE,CAAC;YACjB,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YACvB,OAAO,IAAI,EAAE,CAAC;gBACV,IAAI,cAAc,KAAK,CAAC,EAAE,CAAC;oBACvB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBAEhC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAEnB,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;wBACf,OAAO;oBACX,CAAC;oBAED,MAAM,MAAM,GAAG,oBAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACtC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBAExC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;oBACnB,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAEpE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;wBACnC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;oBACrC,CAAC;oBAED,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;gBAC5C,CAAC;gBAED,IAAI,IAAI,CAAC,MAAM,GAAG,cAAc,EAAE,CAAC;oBAC/B,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC;oBAC9B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACnB,MAAM;gBACV,CAAC;gBAED,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;gBAE/C,MAAM,MAAM,GAAG,oBAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACtC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;gBAEnB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC3C,cAAc,CAAC,GAAG,EAAE;oBAChB,SAAS,CAAC,IAAI,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC;gBAEH,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;gBAChD,cAAc,GAAG,CAAC,CAAC;gBAEnB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACzB,MAAM;gBACV,CAAC;gBAED,IAAI,GAAG,SAAS,CAAC;YACrB,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AApGW,QAAA,gBAAgB,oBAoG3B"}