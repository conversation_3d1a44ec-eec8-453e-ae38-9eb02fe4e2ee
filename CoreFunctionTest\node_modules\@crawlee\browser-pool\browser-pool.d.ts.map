{"version": 3, "file": "browser-pool.d.ts", "sourceRoot": "", "sources": ["../src/browser-pool.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,6BAA6B,EAAE,MAAM,uBAAuB,CAAC;AAC3E,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAC7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,sBAAsB,CAAC;AAI3D,OAAO,QAAQ,MAAM,WAAW,CAAC;AACjC,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAIlD,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,uCAAuC,CAAC;AAC/E,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,mCAAmC,CAAC;AACvE,OAAO,EAAE,mBAAmB,EAAE,MAAM,UAAU,CAAC;AAM/C,OAAO,KAAK,EAAE,2BAA2B,EAAE,MAAM,wBAAwB,CAAC;AAC1E,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAEtD,OAAO,KAAK,EAAE,uBAAuB,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAKtE,MAAM,WAAW,iBAAiB,CAAC,EAAE,SAAS,iBAAiB,EAAE,IAAI;IACjE,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACzE,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACxE,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,CAAC,iBAAiB,EAAE,EAAE,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACvF,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,EAAE,CAAC,iBAAiB,EAAE,EAAE,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CAC3F;AAED;;;;GAIG;AACH,MAAM,WAAW,kBAAkB;IAC/B;;OAEG;IACH,2BAA2B,CAAC,EAAE,2BAA2B,CAAC;IAC1D;;;;OAIG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B;;;;;OAKG;IACH,oBAAoB,CAAC,EAAE,MAAM,CAAC;CACjC;AAED,MAAM,WAAW,kBAAkB,CAAC,MAAM,SAAS,aAAa,GAAG,aAAa;IAC5E;;;;OAIG;IACH,cAAc,EAAE,SAAS,MAAM,EAAE,CAAC;IAClC;;;;;OAKG;IACH,sBAAsB,CAAC,EAAE,MAAM,CAAC;IAChC;;;;;;;;OAQG;IACH,2BAA2B,CAAC,EAAE,MAAM,CAAC;IACrC;;;;;;;OAOG;IACH,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B;;;;;;OAMG;IACH,6BAA6B,CAAC,EAAE,MAAM,CAAC;IACvC;;;;;;;;OAQG;IACH,8BAA8B,CAAC,EAAE,MAAM,CAAC;IACxC;;OAEG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,kBAAkB,CAAC,EAAE,kBAAkB,CAAC;CAC3C;AAED;;;;;GAKG;AACH,MAAM,MAAM,aAAa,CAAC,EAAE,SAAS,aAAa,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,EAAE,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAElH;;;;;;;;GAQG;AACH,MAAM,MAAM,cAAc,CAAC,EAAE,SAAS,iBAAiB,IAAI,CACvD,MAAM,EAAE,MAAM,EACd,iBAAiB,EAAE,EAAE,KACpB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAE1B;;;;;;;;GAQG;AACH,MAAM,MAAM,iBAAiB,CAAC,EAAE,SAAS,iBAAiB,EAAE,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAC7F,MAAM,EAAE,MAAM,EACd,iBAAiB,EAAE,EAAE,EACrB,WAAW,CAAC,EAAE,EAAE,KACf,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAE1B;;;;;;;GAOG;AACH,MAAM,MAAM,kBAAkB,CAAC,EAAE,SAAS,iBAAiB,EAAE,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAC5G,IAAI,EAAE,IAAI,EACV,iBAAiB,EAAE,EAAE,KACpB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAE1B;;;;;;GAMG;AACH,MAAM,MAAM,gBAAgB,CAAC,EAAE,SAAS,iBAAiB,EAAE,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAC1G,IAAI,EAAE,IAAI,EACV,iBAAiB,EAAE,EAAE,KACpB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAE1B;;;;GAIG;AACH,MAAM,MAAM,iBAAiB,CAAC,EAAE,SAAS,iBAAiB,IAAI,CAC1D,MAAM,EAAE,MAAM,EACd,iBAAiB,EAAE,EAAE,KACpB,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAE1B,MAAM,WAAW,gBAAgB,CAC7B,EAAE,SAAS,iBAAiB,EAC5B,EAAE,SAAS,aAAa,EACxB,EAAE,SAAS,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;IAE9F;;;;;OAKG;IACH,cAAc,CAAC,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC;IACrC;;;;;;;;OAQG;IACH,eAAe,CAAC,EAAE,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC;IACvC;;;;;;;;OAQG;IACH,kBAAkB,CAAC,EAAE,iBAAiB,CAAC,EAAE,CAAC,EAAE,CAAC;IAC7C;;;;;;;OAOG;IACH,mBAAmB,CAAC,EAAE,kBAAkB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;IACnD;;;;;;OAMG;IACH,iBAAiB,CAAC,EAAE,gBAAgB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;IAC/C;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,iBAAiB,CAAC,EAAE,CAAC,EAAE,CAAC;CAChD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8CG;AACH,qBAAa,WAAW,CACpB,OAAO,SAAS,kBAAkB,GAAG,kBAAkB,EACvD,cAAc,SAAS,aAAa,EAAE,GAAG,uBAAuB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,EAC3F,uBAAuB,SAAS,iBAAiB,GAAG,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,kBAAkB,CAAC,CAAC,EAC1G,mBAAmB,SAAS,aAAa,GAAG,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,qBAAqB,CAAC,CAAC,EACrG,WAAW,GAAG,UAAU,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/D,UAAU,SAAS,aAAa,CAAC,UAAU,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,aAAa,CAC5F,UAAU,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC,CACjD,CACH,SAAQ,YAAY,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;IAC1E,cAAc,EAAE,cAAc,CAAC;IAC/B,sBAAsB,EAAE,MAAM,CAAC;IAC/B,2BAA2B,EAAE,MAAM,CAAC;IACpC,sBAAsB,EAAE,MAAM,CAAC;IAC/B,+BAA+B,EAAE,MAAM,CAAC;IACxC,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,kBAAkB,EAAE,kBAAkB,CAAC;IACvC,cAAc,EAAE,aAAa,CAAC,mBAAmB,CAAC,EAAE,CAAC;IACrD,eAAe,EAAE,cAAc,CAAC,uBAAuB,CAAC,EAAE,CAAC;IAC3D,kBAAkB,EAAE,iBAAiB,CAAC,uBAAuB,EAAE,WAAW,CAAC,EAAE,CAAC;IAC9E,mBAAmB,EAAE,kBAAkB,CAAC,uBAAuB,EAAE,UAAU,CAAC,EAAE,CAAC;IAC/E,iBAAiB,EAAE,gBAAgB,CAAC,uBAAuB,EAAE,UAAU,CAAC,EAAE,CAAC;IAC3E,kBAAkB,EAAE,iBAAiB,CAAC,uBAAuB,CAAC,EAAE,CAAC;IACjE,WAAW,SAAK;IAChB,KAAK,0BAAiC;IACtC,OAAO,8BAAqC;IAC5C,wBAAwB,+BAAsC;IAC9D,yBAAyB,+BAAsC;IAC/D,uBAAuB,+CAAsD;IAC7E,mBAAmB,CAAC,EAAE,mBAAmB,CAAC;IAC1C,oBAAoB,CAAC,EAAE,oBAAoB,CAAC;IAC5C,gBAAgB,CAAC,EAAE,QAAQ,CAAC,MAAM,EAAE,6BAA6B,CAAC,CAAC;IAEnE,OAAO,CAAC,qBAAqB,CAAC,CAG5B;IAEF,OAAO,CAAC,qBAAqB,CAAC,CAAiB;IAE/C,OAAO,CAAC,OAAO,CAAa;gBAEhB,OAAO,EAAE,OAAO,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,mBAAmB,EAAE,UAAU,CAAC;IA8FzG;;;;OAIG;IACG,OAAO,CAAC,OAAO,GAAE,yBAAyB,CAAC,WAAW,EAAE,cAAc,CAAC,MAAM,CAAC,CAAM,GAAG,OAAO,CAAC,UAAU,CAAC;IAuBhH;;;;OAIG;IACG,mBAAmB,CACrB,OAAO,GAAE,qCAAqC,CAAC,WAAW,EAAE,cAAc,CAAC,MAAM,CAAC,CAAM,GACzF,OAAO,CAAC,UAAU,CAAC;IAYtB;;;;;;;;;;;;;;;;;;;OAmBG;IACG,qBAAqB,CACvB,WAAW,GAAE,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,eAAe,CAAC,EAAO,GAC1G,OAAO,CAAC,UAAU,EAAE,CAAC;IAWxB;;;;;;;;;;;;OAYG;IACH,0BAA0B,CAAC,IAAI,EAAE,UAAU,GAAG,uBAAuB,GAAG,SAAS;IAIjF;;;;;OAKG;IACH,OAAO,CAAC,EAAE,EAAE,MAAM,GAAG,UAAU,GAAG,SAAS;IAI3C;;;;;OAKG;IACH,SAAS,CAAC,IAAI,EAAE,UAAU,GAAG,MAAM,GAAG,SAAS;YAIjC,qBAAqB;IA2DnC;;;;OAIG;IACH,uBAAuB,CAAC,iBAAiB,EAAE,uBAAuB,GAAG,IAAI;IASzE;;;OAGG;IACH,mBAAmB,CAAC,IAAI,EAAE,UAAU,GAAG,IAAI;IAK3C;;;OAGG;IACH,iBAAiB,IAAI,IAAI;IAMzB;;;OAGG;IACG,gBAAgB,IAAI,OAAO,CAAC,IAAI,CAAC;IASvC;;OAEG;IACG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAW9B,OAAO,CAAC,SAAS;IAOjB,OAAO,CAAC,yBAAyB;YAInB,cAAc;IAmD5B;;;OAGG;IACH,OAAO,CAAC,kBAAkB;IAO1B,OAAO,CAAC,4BAA4B;YAkBtB,6BAA6B;IAyB3C,OAAO,CAAC,kBAAkB;YAqBZ,aAAa;IAM3B,OAAO,CAAC,+BAA+B;IAavC,OAAO,CAAC,yBAAyB;IAYjC,OAAO,CAAC,oBAAoB;CAU/B;AAED,MAAM,WAAW,yBAAyB,CAAC,WAAW,EAAE,EAAE,SAAS,aAAa;IAC5E;;;OAGG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ;;;OAGG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B;;;;;;;OAOG;IACH,aAAa,CAAC,EAAE,EAAE,CAAC;IACnB;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,qCAAqC,CAAC,WAAW,EAAE,EAAE,SAAS,aAAa;IACxF;;;OAGG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ;;;OAGG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAC1B;;;;;;;;;;;OAWG;IACH,aAAa,CAAC,EAAE,EAAE,CAAC;IACnB;;OAEG;IACH,aAAa,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC;CACvC"}