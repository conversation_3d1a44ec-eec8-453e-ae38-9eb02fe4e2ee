import mod from "./index.js";

export default mod;
export const BROWSER_CONTROLLER_EVENTS = mod.BROWSER_CONTROLLER_EVENTS;
export const BROWSER_POOL_EVENTS = mod.BROWSER_POOL_EVENTS;
export const BrowserController = mod.BrowserController;
export const BrowserLaunchError = mod.BrowserLaunchError;
export const BrowserName = mod.BrowserName;
export const BrowserPlugin = mod.BrowserPlugin;
export const BrowserPool = mod.BrowserPool;
export const DEFAULT_USER_AGENT = mod.DEFAULT_USER_AGENT;
export const DeviceCategory = mod.DeviceCategory;
export const LaunchContext = mod.LaunchContext;
export const OperatingSystemsName = mod.OperatingSystemsName;
export const PlaywrightBrowser = mod.PlaywrightBrowser;
export const PlaywrightController = mod.PlaywrightController;
export const PlaywrightPlugin = mod.PlaywrightPlugin;
export const PuppeteerController = mod.PuppeteerController;
export const PuppeteerPlugin = mod.PuppeteerPlugin;
