// Chrome配置管理器 - 负责Chrome浏览器的检测、配置和数据目录管理
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Microsoft.Win32;

namespace BatchBrowserUI.Models
{
    /// <summary>
    /// Chrome配置管理器
    /// 负责Chrome浏览器的自动检测、配置管理和隔离数据目录创建
    /// </summary>
    public class ChromeConfigManager
    {
        /// <summary>
        /// Chrome可能的安装路径列表
        /// </summary>
        private static readonly string[] ChromePaths = new[]
        {
            @"C:\Program Files\Google\Chrome\Application\chrome.exe",
            @"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            @"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe",
            @"C:\Program Files\Google\Chrome Beta\Application\chrome.exe",
            @"C:\Program Files (x86)\Google\Chrome Beta\Application\chrome.exe"
        };

        /// <summary>
        /// 检测到的Chrome路径
        /// </summary>
        public string? DetectedChromePath { get; private set; }

        /// <summary>
        /// 浏览器数据根目录
        /// </summary>
        public string BrowserDataRootPath { get; private set; }

        /// <summary>
        /// Chrome配置选项
        /// </summary>
        public ChromeConfig Config { get; set; }

        /// <summary>
        /// 初始化Chrome配置管理器
        /// </summary>
        public ChromeConfigManager()
        {
            BrowserDataRootPath = Path.Combine(Environment.CurrentDirectory, "browser_data");
            Config = new ChromeConfig();
            
            // 确保根数据目录存在
            if (!Directory.Exists(BrowserDataRootPath))
            {
                Directory.CreateDirectory(BrowserDataRootPath);
            }
        }

        /// <summary>
        /// 自动检测Chrome浏览器路径
        /// </summary>
        /// <returns>检测结果</returns>
        public ChromeDetectionResult DetectChrome()
        {
            try
            {
                Console.WriteLine("🔍 开始检测Chrome浏览器...");

                // 1. 尝试从注册表检测
                var registryPath = DetectChromeFromRegistry();
                if (!string.IsNullOrEmpty(registryPath) && File.Exists(registryPath))
                {
                    DetectedChromePath = registryPath;
                    Console.WriteLine($"✅ 从注册表检测到Chrome: {registryPath}");
                    return new ChromeDetectionResult
                    {
                        Success = true,
                        ChromePath = registryPath,
                        DetectionMethod = "注册表",
                        Version = GetChromeVersion(registryPath)
                    };
                }

                // 2. 尝试从常见路径检测
                foreach (var pathTemplate in ChromePaths)
                {
                    var path = pathTemplate.Contains("{0}") 
                        ? string.Format(pathTemplate, Environment.UserName) 
                        : pathTemplate;

                    if (File.Exists(path))
                    {
                        DetectedChromePath = path;
                        Console.WriteLine($"✅ 从路径检测到Chrome: {path}");
                        return new ChromeDetectionResult
                        {
                            Success = true,
                            ChromePath = path,
                            DetectionMethod = "文件路径",
                            Version = GetChromeVersion(path)
                        };
                    }
                }

                Console.WriteLine("❌ 未检测到Chrome浏览器");
                return new ChromeDetectionResult
                {
                    Success = false,
                    Error = "未找到Chrome浏览器，请确保已安装Chrome"
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Chrome检测失败: {ex.Message}");
                return new ChromeDetectionResult
                {
                    Success = false,
                    Error = $"检测失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 从注册表检测Chrome路径
        /// </summary>
        private string? DetectChromeFromRegistry()
        {
            try
            {
                // 检查HKEY_LOCAL_MACHINE
                using (var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"))
                {
                    if (key?.GetValue("") is string path && File.Exists(path))
                        return path;
                }

                // 检查HKEY_CURRENT_USER
                using (var key = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"))
                {
                    if (key?.GetValue("") is string path && File.Exists(path))
                        return path;
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ 注册表检测失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取Chrome版本信息
        /// </summary>
        private string GetChromeVersion(string chromePath)
        {
            try
            {
                var versionInfo = System.Diagnostics.FileVersionInfo.GetVersionInfo(chromePath);
                return versionInfo.FileVersion ?? "未知版本";
            }
            catch
            {
                return "未知版本";
            }
        }

        /// <summary>
        /// 为浏览器实例创建隔离数据目录
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <returns>数据目录路径</returns>
        public string CreateInstanceDataDirectory(string instanceId)
        {
            try
            {
                var instanceDataPath = Path.Combine(BrowserDataRootPath, instanceId);
                
                if (!Directory.Exists(instanceDataPath))
                {
                    Directory.CreateDirectory(instanceDataPath);
                    Console.WriteLine($"📁 创建实例数据目录: {instanceDataPath}");
                }

                return instanceDataPath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 创建数据目录失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 生成Chrome启动参数
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <param name="proxyServer">代理服务器</param>
        /// <returns>Chrome启动参数</returns>
        public List<string> GenerateChromeArgs(string instanceId, string? proxyServer = null)
        {
            var args = new List<string>();

            // 数据目录
            var dataDir = CreateInstanceDataDirectory(instanceId);
            args.Add($"--user-data-dir=\"{dataDir}\"");

            // 基本参数
            if (Config.UseHeadlessMode)
                args.Add("--headless");

            if (Config.DisableImages)
                args.Add("--blink-settings=imagesEnabled=false");

            // 防检测参数
            if (Config.EnableAntiDetection)
            {
                args.AddRange(GetAntiDetectionArgs());
            }

            // 代理设置
            if (!string.IsNullOrEmpty(proxyServer))
            {
                args.Add($"--proxy-server={proxyServer}");
            }

            // 其他安全和性能参数
            args.AddRange(new[]
            {
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-default-apps",
                "--disable-popup-blocking",
                "--disable-translate",
                "--disable-background-timer-throttling",
                "--disable-renderer-backgrounding",
                "--disable-backgrounding-occluded-windows",
                "--disable-ipc-flooding-protection"
            });

            return args;
        }

        /// <summary>
        /// 获取防检测参数
        /// </summary>
        private List<string> GetAntiDetectionArgs()
        {
            return new List<string>
            {
                "--disable-blink-features=AutomationControlled",
                "--disable-dev-shm-usage",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",
                "--disable-javascript",
                "--disable-gpu",
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor"
            };
        }
    }

    /// <summary>
    /// Chrome检测结果
    /// </summary>
    public class ChromeDetectionResult
    {
        public bool Success { get; set; }
        public string? ChromePath { get; set; }
        public string? DetectionMethod { get; set; }
        public string? Version { get; set; }
        public string? Error { get; set; }
    }

    /// <summary>
    /// Chrome配置选项
    /// </summary>
    public class ChromeConfig
    {
        public bool UseLocalChrome { get; set; } = true;
        public bool UseHeadlessMode { get; set; } = false;
        public bool EnableAntiDetection { get; set; } = true;
        public bool RandomFingerprint { get; set; } = true;
        public bool SpoofUserAgent { get; set; } = true;
        public bool SpoofViewport { get; set; } = true;
        public bool DisableImages { get; set; } = false;
        public int MaxConcurrency { get; set; } = 10;
        public int PageTimeout { get; set; } = 30;
    }
}
