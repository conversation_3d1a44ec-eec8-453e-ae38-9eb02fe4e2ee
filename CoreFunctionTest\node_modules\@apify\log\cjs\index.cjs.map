{"version": 3, "sources": ["../../src/index.ts", "../../src/log_consts.ts", "../../src/log_helpers.ts", "../../src/logger.ts", "../../src/logger_json.ts", "../../src/logger_text.ts", "../../src/node_internals.ts", "../../src/log.ts"], "sourcesContent": ["import { Log } from './log';\n\nexport * from './log';\nexport * from './log_consts';\nexport * from './log_helpers';\nexport * from './logger';\nexport * from './logger_json';\nexport * from './logger_text';\n\n// Default export is an initialized instance of logger.\nconst log = new Log();\n\n// eslint-disable-next-line import/no-default-export\nexport default log;\n", "export enum LogLevel {\n    // Turns off logging completely\n    OFF = 0,\n    // For unexpected errors in Apify system\n    ERROR = 1,\n    // For situations where error is caused by user (e.g. Meteor.Error), i.e. when the error is not\n    // caused by Apify system, avoid the word \"ERROR\" to simplify searching in log\n    SOFT_FAIL = 2,\n    WARNING = 3,\n    INFO = 4,\n    DEBUG = 5,\n    // for performance stats\n    PERF = 6,\n}\n\nexport enum LogFormat {\n    JSON = 'JSON',\n    TEXT = 'TEXT',\n}\n\nexport const PREFIX_DELIMITER = ':';\nexport const LEVELS = LogLevel;\n\n// Inverse of LOG_LEVELS = maps log level to string.\nexport const LEVEL_TO_STRING = Object.keys(LogLevel).filter((x) => Number.isNaN(+x));\n\n/**\n * A symbol used to mark a limited depth object as having come from an error\n * @internal\n */\nexport const IS_APIFY_LOGGER_EXCEPTION = Symbol('apify.processed_error');\n", "import { APIFY_ENV_VARS } from '@apify/consts';\n\nimport { IS_APIFY_LOGGER_EXCEPTION, LogFormat, LogLevel } from './log_consts';\n\n/**\n * Ensures a string is shorter than a specified number of character, and truncates it if not, appending a specific suffix to it.\n * (copied from utilities package so logger do not have to depend on all of its dependencies)\n */\nexport function truncate(str: string, maxLength: number, suffix = '...[truncated]'): string {\n    maxLength = Math.floor(maxLength);\n\n    // TODO: we should just ignore rest of the suffix...\n    if (suffix.length > maxLength) {\n        throw new Error('suffix string cannot be longer than maxLength');\n    }\n\n    if (typeof str === 'string' && str.length > maxLength) {\n        str = str.substr(0, maxLength - suffix.length) + suffix;\n    }\n\n    return str;\n}\n\n/**\n * Gets log level from env variable. Both integers and strings (WARNING) are supported.\n */\nexport function getLevelFromEnv(): number {\n    const envVar = process.env[APIFY_ENV_VARS.LOG_LEVEL] as keyof typeof LogLevel;\n\n    if (!envVar) return LogLevel.INFO;\n    if (Number.isFinite(+envVar)) return +envVar;\n    if (LogLevel[envVar]) return LogLevel[envVar];\n\n    return +envVar;\n}\n\n/**\n * Gets log format from env variable. Currently, values 'JSON' and 'TEXT' are supported.\n * Defaults to 'TEXT' if no value is specified.\n */\nexport function getFormatFromEnv(): LogFormat {\n    const envVar = process.env[APIFY_ENV_VARS.LOG_FORMAT] || LogFormat.TEXT;\n\n    switch (envVar.toLowerCase()) {\n        case LogFormat.JSON.toLowerCase():\n            return LogFormat.JSON;\n        case LogFormat.TEXT.toLowerCase():\n            return LogFormat.TEXT;\n        default:\n            // eslint-disable-next-line no-console\n            console.warn(`Unknown value for environment variable ${APIFY_ENV_VARS.LOG_FORMAT}: ${envVar}`);\n            return LogFormat.TEXT;\n    }\n}\n\n/**\n * Limits given object to given depth and escapes function with [function] string.\n *\n * ie. Replaces object's content by '[object]' and array's content\n * by '[array]' when the value is nested more than given limit.\n */\nexport function limitDepth<T>(record: T, depth: number, maxStringLength?: number): T | undefined {\n    // handle common cases quickly\n    if (typeof record === 'string') {\n        return maxStringLength && record.length > maxStringLength ? truncate(record, maxStringLength) as unknown as T : record;\n    }\n\n    if (['number', 'boolean', 'symbol', 'bigint'].includes(typeof record) || record == null || record instanceof Date) {\n        return record;\n    }\n\n    // WORKAROUND: Error's properties are not iterable, convert it to a simple object and preserve custom properties\n    // NOTE: _.isError() doesn't work on Match.Error\n    if (record instanceof Error) {\n        const { name, message, stack, cause, ...rest } = record;\n        record = { name, message, stack, cause, ...rest, [IS_APIFY_LOGGER_EXCEPTION]: true } as unknown as T;\n    }\n\n    const nextCall = (rec: T) => limitDepth(rec, depth - 1, maxStringLength);\n\n    if (Array.isArray(record)) {\n        return (depth ? record.map(nextCall) : '[array]') as unknown as T;\n    }\n\n    if (typeof record === 'object' && record !== null) {\n        const mapObject = <U extends Record<PropertyKey, any>> (obj: U) => {\n            const res = {} as U;\n            Reflect.ownKeys(obj).forEach((key: keyof U) => {\n                res[key as keyof U] = nextCall(obj[key]) as U[keyof U];\n            });\n            return res;\n        };\n\n        return depth ? mapObject(record) : '[object]' as unknown as T;\n    }\n\n    // Replaces all function with [function] string\n    if (typeof record === 'function') {\n        return '[function]' as unknown as T;\n    }\n\n    // this shouldn't happen\n    // eslint-disable-next-line no-console\n    console.log(`WARNING: Object cannot be logged: ${record}`);\n\n    return undefined;\n}\n\n// Like an error class, but turned into an object\nexport interface LimitedError {\n    // used to identify this object as an error\n    [IS_APIFY_LOGGER_EXCEPTION]: true;\n    name: string;\n    message: string;\n    stack?: string;\n    cause?: unknown;\n\n    // Custom properties\n    type?: string;\n    details?: Record<string, unknown>;\n    reason?: string;\n    [key: string]: unknown;\n}\n", "/* eslint-disable no-console */\nimport { EventEmitter } from 'node:events';\n\nimport { LogLevel } from './log_consts';\n\n/**\n * This is an abstract class that should\n * be extended by custom logger classes.\n *\n * this._log() method must be implemented by them.\n */\nexport class Logger extends EventEmitter {\n    constructor(protected options: Record<string, any>) {\n        super();\n    }\n\n    setOptions(options: Record<string, any>) {\n        this.options = { ...this.options, ...options };\n    }\n\n    getOptions() {\n        return this.options;\n    }\n\n    _outputWithConsole(level: LogLevel, line: string) {\n        switch (level) {\n            case LogLevel.ERROR:\n                console.error(line);\n                break;\n            case LogLevel.WARNING:\n                console.warn(line);\n                break;\n            case LogLevel.DEBUG:\n                console.debug(line);\n                break;\n            default:\n                console.log(line);\n        }\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _log(level: LogLevel, message: string, data?: any, exception?: unknown, opts: Record<string, any> = {}) {\n        throw new Error('log() method must be implemented!');\n    }\n\n    log(level: LogLevel, message: string, ...args: any[]) {\n        const line = this._log(level, message, ...args);\n        this.emit('line', line);\n    }\n}\n", "import { LogLevel, PREFIX_DELIMITER } from './log_consts';\nimport { Logger } from './logger';\n\nconst DEFAULT_OPTIONS = {\n    skipLevelInfo: false,\n    skipTime: false,\n};\n\nexport class <PERSON><PERSON><PERSON><PERSON> extends Logger {\n    constructor(options = {}) {\n        super({ ...DEFAULT_OPTIONS, ...options });\n    }\n\n    _log(level: LogLevel, message: string, data?: any, exception?: unknown, opts: Record<string, any> = {}) {\n        const { prefix, suffix } = opts;\n\n        if (exception) data = { ...data, exception };\n        if (prefix) message = `${prefix}${PREFIX_DELIMITER} ${message}`;\n        if (suffix) message = `${message} ${suffix}`;\n\n        // Use short names to save log space.\n        // In development mode show more concise log otherwise it's impossible to see anything in it.\n        // Message must be shown early for people to see!\n        // NOTE: not adding time and host on production, because LogDNA adds it by default and log space is expensive\n        const rec = {\n            time: !this.options.skipTime ? new Date() : undefined,\n            level: this.options.skipLevelInfo && level === LogLevel.INFO ? undefined : LogLevel[level],\n            msg: message,\n            ...data,\n        };\n\n        const line = JSON.stringify(rec);\n        this._outputWithConsole(level, line);\n\n        return line;\n    }\n}\n", "import c from 'ansi-colors';\n\nimport { IS_APIFY_LOGGER_EXCEPTION, LEVEL_TO_STRING, LogLevel, PREFIX_DELIMITER } from './log_consts';\nimport type { LimitedError } from './log_helpers';\nimport { Logger } from './logger';\nimport { getStackFrames } from './node_internals';\n\nconst SHORTEN_LEVELS = {\n    SOFT_FAIL: 'SFAIL',\n    WARNING: 'WARN',\n} as const;\n\nconst LEVEL_TO_COLOR = {\n    [LogLevel.ERROR]: 'red',\n    [LogLevel.SOFT_FAIL]: 'red',\n    [LogLevel.WARNING]: 'yellow',\n    [LogLevel.INFO]: 'green',\n    [LogLevel.DEBUG]: 'blue',\n    [LogLevel.PERF]: 'magenta',\n} as const;\n\nconst SHORTENED_LOG_LEVELS = LEVEL_TO_STRING.map((level) => SHORTEN_LEVELS[level as keyof typeof SHORTEN_LEVELS] || level);\nconst MAX_LEVEL_LENGTH_SPACES = Math.max(...SHORTENED_LOG_LEVELS.map((l) => l.length));\n\nconst getLevelIndent = (level: string) => {\n    let spaces = '';\n\n    for (let i = 0; i < MAX_LEVEL_LENGTH_SPACES - level.length; i++) spaces += ' ';\n\n    return spaces;\n};\n\nconst DEFAULT_OPTIONS = {\n    skipTime: true,\n};\n\nexport class LoggerText extends Logger {\n    constructor(options = {}) {\n        super({ ...DEFAULT_OPTIONS, ...options });\n    }\n\n    _log(level: LogLevel, message: string, data?: any, exception?: unknown, opts: Record<string, any> = {}) {\n        let { prefix, suffix } = opts;\n\n        let maybeDate = '';\n        if (!this.options.skipTime) {\n            maybeDate = `${(new Date()).toISOString().replace('Z', '').replace('T', ' ')} `;\n        }\n\n        const errStack = exception ? this._parseException(exception) : '';\n        const color = LEVEL_TO_COLOR[level as keyof typeof LEVEL_TO_COLOR];\n        const levelStr = SHORTENED_LOG_LEVELS[level];\n        const levelIndent = getLevelIndent(levelStr);\n        const dataStr = !data ? '' : ` ${JSON.stringify(data)}`;\n\n        prefix = prefix ? ` ${prefix}${PREFIX_DELIMITER}` : '';\n        suffix = suffix ? ` ${suffix}` : '';\n\n        const line = `${c.gray(maybeDate)}${c[color](levelStr)}${levelIndent}${c.yellow(prefix)} ${message || ''}${c.gray(dataStr)}${c.yellow(suffix)}${errStack}`;\n        this._outputWithConsole(level, line);\n\n        return line;\n    }\n\n    protected _parseException(exception: unknown, indentLevel = 1) {\n        if (['string', 'boolean', 'number', 'undefined', 'bigint'].includes(typeof exception)) {\n            return `\\n${exception}`;\n        }\n\n        if (exception === null) {\n            return '\\nnull';\n        }\n\n        // We need to manually call toString on symbols\n        if (typeof exception === 'symbol') {\n            return `\\n${exception.toString()}`;\n        }\n\n        if (typeof exception === 'object' && IS_APIFY_LOGGER_EXCEPTION in exception) {\n            return this._parseLoggerException(exception as LimitedError, indentLevel);\n        }\n\n        // Anything else we just stringify\n        return `\\n${JSON.stringify(exception, null, 2)}`;\n    }\n\n    private _parseLoggerException(exception: LimitedError, indentLevel = 1) {\n        const errDetails = [];\n\n        if (exception.type) {\n            errDetails.push(`type=${exception.type}`);\n        }\n\n        if (exception.details) {\n            Object.entries(exception.details).map(([key, val]) => errDetails.push(`${key}=${val}`));\n        }\n\n        // Parse the stack lines\n        const errorString = exception.stack || exception.reason || exception.message;\n        const isStack = errorString === exception.stack;\n        const errorLines = getStackFrames(exception, errorString);\n\n        if (isStack) {\n            // Remove the useless `Error` prefix from stack traces\n            errorLines[0] = exception.message || errorLines[0];\n        }\n\n        // Add details to the first line.\n        if (errDetails.length) {\n            errorLines[0] += c.gray(`(details: ${errDetails.join(', ')})`);\n        }\n\n        // Make stack lines gray\n        for (let i = 1; i < errorLines.length; i++) {\n            errorLines[i] = c.gray(errorLines[i]);\n        }\n\n        // Recursively parse the cause.\n        if (exception.cause) {\n            const causeString = this._parseException(exception.cause, indentLevel + 1);\n            const causeLines = causeString.trim().split('\\n');\n\n            errorLines.push(c.red(`  CAUSE: ${c.reset(causeLines[0])}`), ...causeLines.slice(1));\n        }\n\n        return `\\n${errorLines.map((line) => `${' '.repeat(indentLevel * 2)}${line}`).join('\\n')}`;\n    }\n}\n", "/*\nTHE FOLLOWING CODE IS LICENSED UNDER THE FOLLOWING LICENSE:\n\nCopyright Joyent, Inc. and other Node contributors. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n*/\n\n// We've adapted the following code to work with our \"error\" representations (which are just nested simple objects)\n\nimport c from 'ansi-colors';\n\nimport { IS_APIFY_LOGGER_EXCEPTION } from './log_consts';\n\nfunction identicalSequenceRange(a: any[], b: any[]) {\n    for (let i = 0; i < a.length - 3; i++) {\n        // Find the first entry of b that matches the current entry of a.\n        const pos = b.indexOf(a[i]);\n        if (pos !== -1) {\n            const rest = b.length - pos;\n            if (rest > 3) {\n                let len = 1;\n                const maxLen = Math.min(a.length - i, rest);\n                // Count the number of consecutive entries.\n                while (maxLen > len && a[i + len] === b[pos + len]) {\n                    len++;\n                }\n                if (len > 3) {\n                    return { len, offset: i };\n                }\n            }\n        }\n    }\n\n    return { len: 0, offset: 0 };\n}\n\nfunction getStackString(error: any) {\n    return error.stack ? String(error.stack) : Error.prototype.toString.call(error);\n}\n\nexport function getStackFrames(err: Error, stack: string) {\n    const frames = stack.split('\\n');\n\n    let cause;\n    try {\n        ({ cause } = err);\n    } catch {\n    // If 'cause' is a getter that throws, ignore it.\n    }\n\n    // Remove stack frames identical to frames in cause.\n    if (cause != null && typeof cause === 'object' && IS_APIFY_LOGGER_EXCEPTION in (cause as any)) {\n        const causeStack = getStackString(cause);\n        const causeStackStart = causeStack.indexOf('\\n    at');\n        if (causeStackStart !== -1) {\n            const causeFrames = causeStack.slice(causeStackStart + 1).split('\\n');\n            const { len, offset } = identicalSequenceRange(frames, causeFrames);\n            if (len > 0) {\n                const skipped = len - 2;\n                const msg = `    ... ${skipped} lines matching cause stack trace ...`;\n                frames.splice(offset + 1, skipped, c.grey(msg));\n            }\n        }\n    }\n    return frames;\n}\n", "import { LogFormat, LogLevel, PREFIX_DELIMITER } from './log_consts';\nimport { getFormatFromEnv, getLevelFromEnv, limitDepth } from './log_helpers';\nimport type { Logger } from './logger';\nimport { <PERSON><PERSON><PERSON><PERSON> } from './logger_json';\nimport { LoggerText } from './logger_text';\n\nexport interface LoggerOptions {\n    /**\n     * Sets the log level to the given value, preventing messages from less important log levels\n     * from being printed to the console. Use in conjunction with the `log.LEVELS` constants.\n     */\n    level?: number;\n    /** Max depth of data object that will be logged. Anything deeper than the limit will be stripped off. */\n    maxDepth?: number;\n    /** Max length of the string to be logged. Longer strings will be truncated. */\n    maxStringLength?: number;\n    /** Prefix to be prepended the each logged line. */\n    prefix?: string | null;\n    /** Suffix that will be appended the each logged line. */\n    suffix?: string | null;\n    /**\n     * Logger implementation to be used. Default one is log.LoggerText to log messages as easily readable\n     * strings. Optionally you can use `log.LoggerJson` that formats each log line as a JSON.\n     */\n    logger?: Logger;\n    /** Additional data to be added to each log line. */\n    data?: Record<string, unknown>,\n}\n\nconst getLoggerForFormat = (format: LogFormat): Logger => {\n    switch (format) {\n        case LogFormat.JSON:\n            return new LoggerJson();\n        case LogFormat.TEXT:\n        default:\n            return new LoggerText();\n    }\n};\n\nconst getDefaultOptions = () => ({\n    level: getLevelFromEnv(),\n    maxDepth: 4,\n    maxStringLength: 2000,\n    prefix: null,\n    suffix: null,\n    logger: getLoggerForFormat(getFormatFromEnv()),\n    data: {},\n});\n\ntype AdditionalData = Record<string, any> | null;\n\n/**\n * The log instance enables level aware logging of messages and we advise\n * to use it instead of `console.log()` and its aliases in most development\n * scenarios.\n *\n * A very useful use case for `log` is using `log.debug` liberally throughout\n * the codebase to get useful logging messages only when appropriate log level is set\n * and keeping the console tidy in production environments.\n *\n * The available logging levels are, in this order: `DEBUG`, `INFO`, `WARNING`, `ERROR`, `OFF`\n * and can be referenced from the `log.LEVELS` constant, such as `log.LEVELS.ERROR`.\n *\n * To log messages to the system console, use the `log.level(message)` invocation,\n * such as `log.debug('this is a debug message')`.\n *\n * To prevent writing of messages above a certain log level to the console, simply\n * set the appropriate level. The default log level is `INFO`, which means that\n * `DEBUG` messages will not be printed, unless enabled.\n *\n * **Example:**\n * ```js\n * import log from '@apify/log';\n *\n * // importing from the Apify SDK or Crawlee is also supported:\n * // import { log } from 'apify';\n * // import { log } from 'crawlee';\n *\n * log.info('Information message', { someData: 123 }); // prints message\n * log.debug('Debug message', { debugData: 'hello' }); // doesn't print anything\n *\n * log.setLevel(log.LEVELS.DEBUG);\n * log.debug('Debug message'); // prints message\n *\n * log.setLevel(log.LEVELS.ERROR);\n * log.debug('Debug message'); // doesn't print anything\n * log.info('Info message'); // doesn't print anything\n * log.error('Error message', { errorDetails: 'This is bad!' }); // prints message\n *\n * try {\n *   throw new Error('Not good!');\n * } catch (e) {\n *   log.exception(e, 'Exception occurred', { errorDetails: 'This is really bad!' }); // prints message\n * }\n *\n * log.setOptions({ prefix: 'My actor' });\n * log.info('I am running!'); // prints \"My actor: I am running\"\n *\n * const childLog = log.child({ prefix: 'Crawler' });\n * log.info('I am crawling!'); // prints \"My actor:Crawler: I am crawling\"\n * ```\n *\n * Another very useful way of setting the log level is by setting the `APIFY_LOG_LEVEL`\n * environment variable, such as `APIFY_LOG_LEVEL=DEBUG`. This way, no code changes\n * are necessary to turn on your debug messages and start debugging right away.\n *\n * To add timestamps to your logs, you can override the default logger settings:\n * ```js\n * log.setOptions({\n *     logger: new log.LoggerText({ skipTime: false }),\n * });\n * ```\n * You can customize your logging further by extending or replacing the default\n * logger instances with your own implementations.\n */\nexport class Log {\n    /**\n     * Map of available log levels that's useful for easy setting of appropriate log levels.\n     * Each log level is represented internally by a number. Eg. `log.LEVELS.DEBUG === 5`.\n     */\n    readonly LEVELS = LogLevel; // for BC\n\n    private options: Required<LoggerOptions>;\n\n    private readonly warningsOnceLogged: Set<string> = new Set();\n\n    constructor(options: Partial<LoggerOptions> = {}) {\n        this.options = { ...getDefaultOptions(), ...options };\n\n        if (!LogLevel[this.options.level]) throw new Error('Options \"level\" must be one of log.LEVELS enum!');\n        if (typeof this.options.maxDepth !== 'number') throw new Error('Options \"maxDepth\" must be a number!');\n        if (typeof this.options.maxStringLength !== 'number') throw new Error('Options \"maxStringLength\" must be a number!');\n        if (this.options.prefix && typeof this.options.prefix !== 'string') throw new Error('Options \"prefix\" must be a string!');\n        if (this.options.suffix && typeof this.options.suffix !== 'string') throw new Error('Options \"suffix\" must be a string!');\n        if (typeof this.options.logger !== 'object') throw new Error('Options \"logger\" must be an object!');\n        if (typeof this.options.data !== 'object') throw new Error('Options \"data\" must be an object!');\n    }\n\n    private _limitDepth(obj: any) {\n        return limitDepth(obj, this.options.maxDepth);\n    }\n\n    /**\n     * Returns the currently selected logging level. This is useful for checking whether a message\n     * will actually be printed to the console before one actually performs a resource intensive operation\n     * to construct the message, such as querying a DB for some metadata that need to be added. If the log\n     * level is not high enough at the moment, it doesn't make sense to execute the query.\n     */\n    getLevel() {\n        return this.options.level;\n    }\n\n    /**\n     * Sets the log level to the given value, preventing messages from less important log levels\n     * from being printed to the console. Use in conjunction with the `log.LEVELS` constants such as\n     *\n     * ```\n     * log.setLevel(log.LEVELS.DEBUG);\n     * ```\n     *\n     * Default log level is INFO.\n     */\n    setLevel(level: LogLevel) {\n        if (!LogLevel[level]) throw new Error('Options \"level\" must be one of log.LEVELS enum!');\n\n        this.options.level = level;\n    }\n\n    internal(level: LogLevel, message: string, data?: any, exception?: any) {\n        if (level > this.options.level) return;\n\n        data = { ...this.options.data, ...data };\n        data = Reflect.ownKeys(data).length > 0 ? this._limitDepth(data) : undefined;\n        exception = this._limitDepth(exception);\n\n        this.options.logger.log(level, message, data, exception, {\n            prefix: this.options.prefix,\n            suffix: this.options.suffix,\n        });\n    }\n\n    /**\n     * Configures logger.\n     */\n    setOptions(options: Partial<LoggerOptions>) {\n        this.options = { ...this.options, ...options };\n    }\n\n    /**\n     * Returns the logger configuration.\n     */\n    getOptions(): Required<LoggerOptions> {\n        return { ...this.options };\n    }\n\n    /**\n     * Creates a new instance of logger that inherits settings from a parent logger.\n     */\n    child(options: Partial<LoggerOptions>) {\n        let { prefix } = this.options;\n\n        if (options.prefix) {\n            prefix = prefix\n                ? `${prefix}${PREFIX_DELIMITER}${options.prefix}`\n                : options.prefix;\n        }\n\n        const data = options.data\n            ? { ...this.options.data, ...options.data }\n            : this.options.data;\n\n        const newOptions = {\n            ...this.options,\n            ...options,\n            prefix,\n            data,\n        };\n\n        return new Log(newOptions);\n    }\n\n    /**\n     * Logs an `ERROR` message. Use this method to log error messages that are not directly connected\n     * to an exception. For logging exceptions, use the `log.exception` method.\n     */\n    error(message: string, data?: AdditionalData) {\n        this.internal(LogLevel.ERROR, message, data);\n    }\n\n    /**\n     * Logs an `ERROR` level message with a nicely formatted exception. Note that the exception is the first parameter\n     * here and an additional message is only optional.\n     */\n    exception(exception: Error, message: string, data?: AdditionalData) {\n        this.internal(LogLevel.ERROR, message, data, exception);\n    }\n\n    softFail(message: string, data?: AdditionalData) {\n        this.internal(LogLevel.SOFT_FAIL, message, data);\n    }\n\n    /**\n     * Logs a `WARNING` level message. Data are stringified and appended to the message.\n     */\n    warning(message: string, data?: AdditionalData) {\n        this.internal(LogLevel.WARNING, message, data);\n    }\n\n    /**\n     * Logs an `INFO` message. `INFO` is the default log level so info messages will be always logged,\n     * unless the log level is changed. Data are stringified and appended to the message.\n     */\n    info(message: string, data?: AdditionalData) {\n        this.internal(LogLevel.INFO, message, data);\n    }\n\n    /**\n     * Logs a `DEBUG` message. By default, it will not be written to the console. To see `DEBUG`\n     * messages in the console, set the log level to `DEBUG` either using the `log.setLevel(log.LEVELS.DEBUG)`\n     * method or using the environment variable `APIFY_LOG_LEVEL=DEBUG`. Data are stringified and appended\n     * to the message.\n     */\n    debug(message: string, data?: AdditionalData) {\n        this.internal(LogLevel.DEBUG, message, data);\n    }\n\n    perf(message: string, data?: AdditionalData) {\n        this.internal(LogLevel.PERF, message, data);\n    }\n\n    /**\n     * Logs a `WARNING` level message only once.\n     */\n    warningOnce(message: string) {\n        if (this.warningsOnceLogged.has(message)) return;\n\n        this.warningsOnceLogged.add(message);\n        this.warning(message);\n    }\n\n    /**\n     * Logs given message only once as WARNING. It's used to warn user that some feature he is using has been deprecated.\n     */\n    deprecated(message: string) {\n        this.warningOnce(message);\n    }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAO,IAAK,WAAL,kBAAKA,cAAL;AAEH,EAAAA,oBAAA,SAAM,KAAN;AAEA,EAAAA,oBAAA,WAAQ,KAAR;AAGA,EAAAA,oBAAA,eAAY,KAAZ;AACA,EAAAA,oBAAA,aAAU,KAAV;AACA,EAAAA,oBAAA,UAAO,KAAP;AACA,EAAAA,oBAAA,WAAQ,KAAR;AAEA,EAAAA,oBAAA,UAAO,KAAP;AAZQ,SAAAA;AAAA,GAAA;AAeL,IAAK,YAAL,kBAAKC,eAAL;AACH,EAAAA,WAAA,UAAO;AACP,EAAAA,WAAA,UAAO;AAFC,SAAAA;AAAA,GAAA;AAKL,IAAM,mBAAmB;AACzB,IAAM,SAAS;AAGf,IAAM,kBAAkB,OAAO,KAAK,QAAQ,EAAE,OAAO,CAAC,MAAM,OAAO,MAAM,CAAC,CAAC,CAAC;AAM5E,IAAM,4BAA4B,OAAO,uBAAuB;;;AC9BvE,oBAA+B;AAQxB,SAAS,SAAS,KAAa,WAAmB,SAAS,kBAA0B;AACxF,cAAY,KAAK,MAAM,SAAS;AAGhC,MAAI,OAAO,SAAS,WAAW;AAC3B,UAAM,IAAI,MAAM,+CAA+C;AAAA,EACnE;AAEA,MAAI,OAAO,QAAQ,YAAY,IAAI,SAAS,WAAW;AACnD,UAAM,IAAI,OAAO,GAAG,YAAY,OAAO,MAAM,IAAI;AAAA,EACrD;AAEA,SAAO;AACX;AAbgB;AAkBT,SAAS,kBAA0B;AACtC,QAAM,SAAS,QAAQ,IAAI,6BAAe,SAAS;AAEnD,MAAI,CAAC,OAAQ;AACb,MAAI,OAAO,SAAS,CAAC,MAAM,EAAG,QAAO,CAAC;AACtC,MAAI,SAAS,MAAM,EAAG,QAAO,SAAS,MAAM;AAE5C,SAAO,CAAC;AACZ;AARgB;AAcT,SAAS,mBAA8B;AAC1C,QAAM,SAAS,QAAQ,IAAI,6BAAe,UAAU;AAEpD,UAAQ,OAAO,YAAY,GAAG;AAAA,IAC1B,uBAAoB,YAAY;AAC5B;AAAA,IACJ,uBAAoB,YAAY;AAC5B;AAAA,IACJ;AAEI,cAAQ,KAAK,0CAA0C,6BAAe,UAAU,KAAK,MAAM,EAAE;AAC7F;AAAA,EACR;AACJ;AAbgB;AAqBT,SAAS,WAAc,QAAW,OAAe,iBAAyC;AAE7F,MAAI,OAAO,WAAW,UAAU;AAC5B,WAAO,mBAAmB,OAAO,SAAS,kBAAkB,SAAS,QAAQ,eAAe,IAAoB;AAAA,EACpH;AAEA,MAAI,CAAC,UAAU,WAAW,UAAU,QAAQ,EAAE,SAAS,OAAO,MAAM,KAAK,UAAU,QAAQ,kBAAkB,MAAM;AAC/G,WAAO;AAAA,EACX;AAIA,MAAI,kBAAkB,OAAO;AACzB,UAAM,EAAE,MAAM,SAAS,OAAO,OAAO,GAAG,KAAK,IAAI;AACjD,aAAS,EAAE,MAAM,SAAS,OAAO,OAAO,GAAG,MAAM,CAAC,yBAAyB,GAAG,KAAK;AAAA,EACvF;AAEA,QAAM,WAAW,wBAAC,QAAW,WAAW,KAAK,QAAQ,GAAG,eAAe,GAAtD;AAEjB,MAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,WAAQ,QAAQ,OAAO,IAAI,QAAQ,IAAI;AAAA,EAC3C;AAEA,MAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AAC/C,UAAM,YAAY,wBAAsC,QAAW;AAC/D,YAAM,MAAM,CAAC;AACb,cAAQ,QAAQ,GAAG,EAAE,QAAQ,CAAC,QAAiB;AAC3C,YAAI,GAAc,IAAI,SAAS,IAAI,GAAG,CAAC;AAAA,MAC3C,CAAC;AACD,aAAO;AAAA,IACX,GANkB;AAQlB,WAAO,QAAQ,UAAU,MAAM,IAAI;AAAA,EACvC;AAGA,MAAI,OAAO,WAAW,YAAY;AAC9B,WAAO;AAAA,EACX;AAIA,UAAQ,IAAI,qCAAqC,MAAM,EAAE;AAEzD,SAAO;AACX;AA7CgB;;;AC5DhB,yBAA6B;AAUtB,IAAM,UAAN,MAAM,gBAAe,gCAAa;AAAA,EACrC,YAAsB,SAA8B;AAChD,UAAM;AADY;AAAA,EAEtB;AAAA,EAEA,WAAW,SAA8B;AACrC,SAAK,UAAU,EAAE,GAAG,KAAK,SAAS,GAAG,QAAQ;AAAA,EACjD;AAAA,EAEA,aAAa;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,mBAAmB,OAAiB,MAAc;AAC9C,YAAQ,OAAO;AAAA,MACX;AACI,gBAAQ,MAAM,IAAI;AAClB;AAAA,MACJ;AACI,gBAAQ,KAAK,IAAI;AACjB;AAAA,MACJ;AACI,gBAAQ,MAAM,IAAI;AAClB;AAAA,MACJ;AACI,gBAAQ,IAAI,IAAI;AAAA,IACxB;AAAA,EACJ;AAAA;AAAA,EAGA,KAAK,OAAiB,SAAiB,MAAY,WAAqB,OAA4B,CAAC,GAAG;AACpG,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACvD;AAAA,EAEA,IAAI,OAAiB,YAAoB,MAAa;AAClD,UAAM,OAAO,KAAK,KAAK,OAAO,SAAS,GAAG,IAAI;AAC9C,SAAK,KAAK,QAAQ,IAAI;AAAA,EAC1B;AACJ;AAtCyC;AAAlC,IAAM,SAAN;;;ACRP,IAAM,kBAAkB;AAAA,EACpB,eAAe;AAAA,EACf,UAAU;AACd;AAEO,IAAM,cAAN,MAAM,oBAAmB,OAAO;AAAA,EACnC,YAAY,UAAU,CAAC,GAAG;AACtB,UAAM,EAAE,GAAG,iBAAiB,GAAG,QAAQ,CAAC;AAAA,EAC5C;AAAA,EAEA,KAAK,OAAiB,SAAiB,MAAY,WAAqB,OAA4B,CAAC,GAAG;AACpG,UAAM,EAAE,QAAQ,OAAO,IAAI;AAE3B,QAAI,UAAW,QAAO,EAAE,GAAG,MAAM,UAAU;AAC3C,QAAI,OAAQ,WAAU,GAAG,MAAM,GAAG,gBAAgB,IAAI,OAAO;AAC7D,QAAI,OAAQ,WAAU,GAAG,OAAO,IAAI,MAAM;AAM1C,UAAM,MAAM;AAAA,MACR,MAAM,CAAC,KAAK,QAAQ,WAAW,oBAAI,KAAK,IAAI;AAAA,MAC5C,OAAO,KAAK,QAAQ,iBAAiB,yBAA0B,SAAY,SAAS,KAAK;AAAA,MACzF,KAAK;AAAA,MACL,GAAG;AAAA,IACP;AAEA,UAAM,OAAO,KAAK,UAAU,GAAG;AAC/B,SAAK,mBAAmB,OAAO,IAAI;AAEnC,WAAO;AAAA,EACX;AACJ;AA5BuC;AAAhC,IAAM,aAAN;;;ACRP,IAAAC,sBAAc;;;ACyBd,yBAAc;AAId,SAAS,uBAAuB,GAAU,GAAU;AAChD,WAAS,IAAI,GAAG,IAAI,EAAE,SAAS,GAAG,KAAK;AAEnC,UAAM,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC1B,QAAI,QAAQ,IAAI;AACZ,YAAM,OAAO,EAAE,SAAS;AACxB,UAAI,OAAO,GAAG;AACV,YAAI,MAAM;AACV,cAAM,SAAS,KAAK,IAAI,EAAE,SAAS,GAAG,IAAI;AAE1C,eAAO,SAAS,OAAO,EAAE,IAAI,GAAG,MAAM,EAAE,MAAM,GAAG,GAAG;AAChD;AAAA,QACJ;AACA,YAAI,MAAM,GAAG;AACT,iBAAO,EAAE,KAAK,QAAQ,EAAE;AAAA,QAC5B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO,EAAE,KAAK,GAAG,QAAQ,EAAE;AAC/B;AArBS;AAuBT,SAAS,eAAe,OAAY;AAChC,SAAO,MAAM,QAAQ,OAAO,MAAM,KAAK,IAAI,MAAM,UAAU,SAAS,KAAK,KAAK;AAClF;AAFS;AAIF,SAAS,eAAe,KAAY,OAAe;AACtD,QAAM,SAAS,MAAM,MAAM,IAAI;AAE/B,MAAI;AACJ,MAAI;AACA,KAAC,EAAE,MAAM,IAAI;AAAA,EACjB,QAAQ;AAAA,EAER;AAGA,MAAI,SAAS,QAAQ,OAAO,UAAU,YAAY,6BAA8B,OAAe;AAC3F,UAAM,aAAa,eAAe,KAAK;AACvC,UAAM,kBAAkB,WAAW,QAAQ,UAAU;AACrD,QAAI,oBAAoB,IAAI;AACxB,YAAM,cAAc,WAAW,MAAM,kBAAkB,CAAC,EAAE,MAAM,IAAI;AACpE,YAAM,EAAE,KAAK,OAAO,IAAI,uBAAuB,QAAQ,WAAW;AAClE,UAAI,MAAM,GAAG;AACT,cAAM,UAAU,MAAM;AACtB,cAAM,MAAM,WAAW,OAAO;AAC9B,eAAO,OAAO,SAAS,GAAG,SAAS,mBAAAC,QAAE,KAAK,GAAG,CAAC;AAAA,MAClD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAzBgB;;;ADjDhB,IAAM,iBAAiB;AAAA,EACnB,WAAW;AAAA,EACX,SAAS;AACb;AAEA,IAAM,iBAAiB;AAAA,EACnB,cAAe,GAAG;AAAA,EAClB,kBAAmB,GAAG;AAAA,EACtB,gBAAiB,GAAG;AAAA,EACpB,aAAc,GAAG;AAAA,EACjB,cAAe,GAAG;AAAA,EAClB,aAAc,GAAG;AACrB;AAEA,IAAM,uBAAuB,gBAAgB,IAAI,CAAC,UAAU,eAAe,KAAoC,KAAK,KAAK;AACzH,IAAM,0BAA0B,KAAK,IAAI,GAAG,qBAAqB,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;AAErF,IAAM,iBAAiB,wBAAC,UAAkB;AACtC,MAAI,SAAS;AAEb,WAAS,IAAI,GAAG,IAAI,0BAA0B,MAAM,QAAQ,IAAK,WAAU;AAE3E,SAAO;AACX,GANuB;AAQvB,IAAMC,mBAAkB;AAAA,EACpB,UAAU;AACd;AAEO,IAAM,cAAN,MAAM,oBAAmB,OAAO;AAAA,EACnC,YAAY,UAAU,CAAC,GAAG;AACtB,UAAM,EAAE,GAAGA,kBAAiB,GAAG,QAAQ,CAAC;AAAA,EAC5C;AAAA,EAEA,KAAK,OAAiB,SAAiB,MAAY,WAAqB,OAA4B,CAAC,GAAG;AACpG,QAAI,EAAE,QAAQ,OAAO,IAAI;AAEzB,QAAI,YAAY;AAChB,QAAI,CAAC,KAAK,QAAQ,UAAU;AACxB,kBAAY,IAAI,oBAAI,KAAK,GAAG,YAAY,EAAE,QAAQ,KAAK,EAAE,EAAE,QAAQ,KAAK,GAAG,CAAC;AAAA,IAChF;AAEA,UAAM,WAAW,YAAY,KAAK,gBAAgB,SAAS,IAAI;AAC/D,UAAM,QAAQ,eAAe,KAAoC;AACjE,UAAM,WAAW,qBAAqB,KAAK;AAC3C,UAAM,cAAc,eAAe,QAAQ;AAC3C,UAAM,UAAU,CAAC,OAAO,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC;AAErD,aAAS,SAAS,IAAI,MAAM,GAAG,gBAAgB,KAAK;AACpD,aAAS,SAAS,IAAI,MAAM,KAAK;AAEjC,UAAM,OAAO,GAAG,oBAAAC,QAAE,KAAK,SAAS,CAAC,GAAG,oBAAAA,QAAE,KAAK,EAAE,QAAQ,CAAC,GAAG,WAAW,GAAG,oBAAAA,QAAE,OAAO,MAAM,CAAC,IAAI,WAAW,EAAE,GAAG,oBAAAA,QAAE,KAAK,OAAO,CAAC,GAAG,oBAAAA,QAAE,OAAO,MAAM,CAAC,GAAG,QAAQ;AACxJ,SAAK,mBAAmB,OAAO,IAAI;AAEnC,WAAO;AAAA,EACX;AAAA,EAEU,gBAAgB,WAAoB,cAAc,GAAG;AAC3D,QAAI,CAAC,UAAU,WAAW,UAAU,aAAa,QAAQ,EAAE,SAAS,OAAO,SAAS,GAAG;AACnF,aAAO;AAAA,EAAK,SAAS;AAAA,IACzB;AAEA,QAAI,cAAc,MAAM;AACpB,aAAO;AAAA,IACX;AAGA,QAAI,OAAO,cAAc,UAAU;AAC/B,aAAO;AAAA,EAAK,UAAU,SAAS,CAAC;AAAA,IACpC;AAEA,QAAI,OAAO,cAAc,YAAY,6BAA6B,WAAW;AACzE,aAAO,KAAK,sBAAsB,WAA2B,WAAW;AAAA,IAC5E;AAGA,WAAO;AAAA,EAAK,KAAK,UAAU,WAAW,MAAM,CAAC,CAAC;AAAA,EAClD;AAAA,EAEQ,sBAAsB,WAAyB,cAAc,GAAG;AACpE,UAAM,aAAa,CAAC;AAEpB,QAAI,UAAU,MAAM;AAChB,iBAAW,KAAK,QAAQ,UAAU,IAAI,EAAE;AAAA,IAC5C;AAEA,QAAI,UAAU,SAAS;AACnB,aAAO,QAAQ,UAAU,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,GAAG,MAAM,WAAW,KAAK,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;AAAA,IAC1F;AAGA,UAAM,cAAc,UAAU,SAAS,UAAU,UAAU,UAAU;AACrE,UAAM,UAAU,gBAAgB,UAAU;AAC1C,UAAM,aAAa,eAAe,WAAW,WAAW;AAExD,QAAI,SAAS;AAET,iBAAW,CAAC,IAAI,UAAU,WAAW,WAAW,CAAC;AAAA,IACrD;AAGA,QAAI,WAAW,QAAQ;AACnB,iBAAW,CAAC,KAAK,oBAAAA,QAAE,KAAK,aAAa,WAAW,KAAK,IAAI,CAAC,GAAG;AAAA,IACjE;AAGA,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,iBAAW,CAAC,IAAI,oBAAAA,QAAE,KAAK,WAAW,CAAC,CAAC;AAAA,IACxC;AAGA,QAAI,UAAU,OAAO;AACjB,YAAM,cAAc,KAAK,gBAAgB,UAAU,OAAO,cAAc,CAAC;AACzE,YAAM,aAAa,YAAY,KAAK,EAAE,MAAM,IAAI;AAEhD,iBAAW,KAAK,oBAAAA,QAAE,IAAI,YAAY,oBAAAA,QAAE,MAAM,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,WAAW,MAAM,CAAC,CAAC;AAAA,IACvF;AAEA,WAAO;AAAA,EAAK,WAAW,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,cAAc,CAAC,CAAC,GAAG,IAAI,EAAE,EAAE,KAAK,IAAI,CAAC;AAAA,EAC5F;AACJ;AA3FuC;AAAhC,IAAM,aAAN;;;AEPP,IAAM,qBAAqB,wBAAC,WAA8B;AACtD,UAAQ,QAAQ;AAAA,IACZ;AACI,aAAO,IAAI,WAAW;AAAA,IAC1B;AAAA,IACA;AACI,aAAO,IAAI,WAAW;AAAA,EAC9B;AACJ,GAR2B;AAU3B,IAAM,oBAAoB,8BAAO;AAAA,EAC7B,OAAO,gBAAgB;AAAA,EACvB,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ,mBAAmB,iBAAiB,CAAC;AAAA,EAC7C,MAAM,CAAC;AACX,IAR0B;AA4EnB,IAAM,OAAN,MAAM,KAAI;AAAA,EAWb,YAAY,UAAkC,CAAC,GAAG;AANlD;AAAA;AAAA;AAAA;AAAA,wBAAS,UAAS;AAElB;AAAA,wBAAQ;AAER,wBAAiB,sBAAkC,oBAAI,IAAI;AAGvD,SAAK,UAAU,EAAE,GAAG,kBAAkB,GAAG,GAAG,QAAQ;AAEpD,QAAI,CAAC,SAAS,KAAK,QAAQ,KAAK,EAAG,OAAM,IAAI,MAAM,iDAAiD;AACpG,QAAI,OAAO,KAAK,QAAQ,aAAa,SAAU,OAAM,IAAI,MAAM,sCAAsC;AACrG,QAAI,OAAO,KAAK,QAAQ,oBAAoB,SAAU,OAAM,IAAI,MAAM,6CAA6C;AACnH,QAAI,KAAK,QAAQ,UAAU,OAAO,KAAK,QAAQ,WAAW,SAAU,OAAM,IAAI,MAAM,oCAAoC;AACxH,QAAI,KAAK,QAAQ,UAAU,OAAO,KAAK,QAAQ,WAAW,SAAU,OAAM,IAAI,MAAM,oCAAoC;AACxH,QAAI,OAAO,KAAK,QAAQ,WAAW,SAAU,OAAM,IAAI,MAAM,qCAAqC;AAClG,QAAI,OAAO,KAAK,QAAQ,SAAS,SAAU,OAAM,IAAI,MAAM,mCAAmC;AAAA,EAClG;AAAA,EAEQ,YAAY,KAAU;AAC1B,WAAO,WAAW,KAAK,KAAK,QAAQ,QAAQ;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW;AACP,WAAO,KAAK,QAAQ;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,SAAS,OAAiB;AACtB,QAAI,CAAC,SAAS,KAAK,EAAG,OAAM,IAAI,MAAM,iDAAiD;AAEvF,SAAK,QAAQ,QAAQ;AAAA,EACzB;AAAA,EAEA,SAAS,OAAiB,SAAiB,MAAY,WAAiB;AACpE,QAAI,QAAQ,KAAK,QAAQ,MAAO;AAEhC,WAAO,EAAE,GAAG,KAAK,QAAQ,MAAM,GAAG,KAAK;AACvC,WAAO,QAAQ,QAAQ,IAAI,EAAE,SAAS,IAAI,KAAK,YAAY,IAAI,IAAI;AACnE,gBAAY,KAAK,YAAY,SAAS;AAEtC,SAAK,QAAQ,OAAO,IAAI,OAAO,SAAS,MAAM,WAAW;AAAA,MACrD,QAAQ,KAAK,QAAQ;AAAA,MACrB,QAAQ,KAAK,QAAQ;AAAA,IACzB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,SAAiC;AACxC,SAAK,UAAU,EAAE,GAAG,KAAK,SAAS,GAAG,QAAQ;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,aAAsC;AAClC,WAAO,EAAE,GAAG,KAAK,QAAQ;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SAAiC;AACnC,QAAI,EAAE,OAAO,IAAI,KAAK;AAEtB,QAAI,QAAQ,QAAQ;AAChB,eAAS,SACH,GAAG,MAAM,GAAG,gBAAgB,GAAG,QAAQ,MAAM,KAC7C,QAAQ;AAAA,IAClB;AAEA,UAAM,OAAO,QAAQ,OACf,EAAE,GAAG,KAAK,QAAQ,MAAM,GAAG,QAAQ,KAAK,IACxC,KAAK,QAAQ;AAEnB,UAAM,aAAa;AAAA,MACf,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAEA,WAAO,IAAI,KAAI,UAAU;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,SAAiB,MAAuB;AAC1C,SAAK,wBAAyB,SAAS,IAAI;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,WAAkB,SAAiB,MAAuB;AAChE,SAAK,wBAAyB,SAAS,MAAM,SAAS;AAAA,EAC1D;AAAA,EAEA,SAAS,SAAiB,MAAuB;AAC7C,SAAK,4BAA6B,SAAS,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,SAAiB,MAAuB;AAC5C,SAAK,0BAA2B,SAAS,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,SAAiB,MAAuB;AACzC,SAAK,uBAAwB,SAAS,IAAI;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,SAAiB,MAAuB;AAC1C,SAAK,wBAAyB,SAAS,IAAI;AAAA,EAC/C;AAAA,EAEA,KAAK,SAAiB,MAAuB;AACzC,SAAK,uBAAwB,SAAS,IAAI;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,SAAiB;AACzB,QAAI,KAAK,mBAAmB,IAAI,OAAO,EAAG;AAE1C,SAAK,mBAAmB,IAAI,OAAO;AACnC,SAAK,QAAQ,OAAO;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,SAAiB;AACxB,SAAK,YAAY,OAAO;AAAA,EAC5B;AACJ;AA3KiB;AAAV,IAAM,MAAN;;;APzGP,IAAM,MAAM,IAAI,IAAI;AAGpB,IAAO,gBAAQ;", "names": ["LogLevel", "LogFormat", "import_ansi_colors", "c", "DEFAULT_OPTIONS", "c"]}