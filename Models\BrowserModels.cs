using BatchBrowserEngine.Services;

namespace BatchBrowserEngine.Models
{
    /// <summary>
    /// 浏览器实例信息模型
    /// </summary>
    public class BrowserInstanceInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string ProxyServer { get; set; } = string.Empty;
        public string CurrentUrl { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public FingerprintInfo? Fingerprint { get; set; }
        public string DataDirectory { get; set; } = string.Empty;
        public string? LastError { get; set; }
        public DateTime? LastActivity { get; set; }
    }

    /// <summary>
    /// 创建实例请求模型
    /// </summary>
    public class CreateInstancesRequest
    {
        public int Count { get; set; } = 1;
        public string? TargetUrl { get; set; }
        public bool UseProxy { get; set; } = true;
        public bool UseRandomFingerprint { get; set; } = true;
        public string? ProxyType { get; set; }
    }

    /// <summary>
    /// 创建实例结果模型
    /// </summary>
    public class CreateInstancesResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<BrowserInstanceInfo> Instances { get; set; } = new();
        public int CreatedCount => Instances.Count;
    }

    /// <summary>
    /// 批量操作请求模型
    /// </summary>
    public class BatchOperationRequest
    {
        public string Operation { get; set; } = string.Empty;
        public string? TargetUrl { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
        public List<string>? InstanceIds { get; set; }
        public int? MaxConcurrency { get; set; }
    }

    /// <summary>
    /// 批量操作结果模型
    /// </summary>
    public class BatchOperationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<InstanceOperationResult> Results { get; set; } = new();
        public int SuccessCount => Results.Count(r => r.Success);
        public int FailureCount => Results.Count(r => !r.Success);
    }

    /// <summary>
    /// 单个实例操作结果
    /// </summary>
    public class InstanceOperationResult
    {
        public string InstanceId { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public object? Data { get; set; }
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// 系统状态模型
    /// </summary>
    public class SystemStatus
    {
        public bool IsRunning { get; set; }
        public int ActiveInstances { get; set; }
        public int MaxConcurrency { get; set; }
        public ProxyPoolStats ProxyStats { get; set; } = new();
        public SystemResources Resources { get; set; } = new();
        public DateTime StartTime { get; set; }
        public TimeSpan Uptime => DateTime.Now - StartTime;
    }

    /// <summary>
    /// 系统资源使用情况
    /// </summary>
    public class SystemResources
    {
        public double CpuUsage { get; set; }
        public long MemoryUsage { get; set; }
        public long MemoryTotal { get; set; }
        public double MemoryUsagePercent => MemoryTotal > 0 ? (double)MemoryUsage / MemoryTotal * 100 : 0;
        public int ThreadCount { get; set; }
        public int HandleCount { get; set; }
    }

    /// <summary>
    /// API响应基类
    /// </summary>
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public string? ErrorCode { get; set; }

        public static ApiResponse<T> SuccessResult(T data, string message = "操作成功")
        {
            return new ApiResponse<T>
            {
                Success = true,
                Message = message,
                Data = data
            };
        }

        public static ApiResponse<T> ErrorResult(string message, string? errorCode = null)
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                ErrorCode = errorCode
            };
        }
    }

    /// <summary>
    /// 分页请求模型
    /// </summary>
    public class PagedRequest
    {
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SortBy { get; set; }
        public string? SortOrder { get; set; } = "asc";
        public string? Filter { get; set; }
    }

    /// <summary>
    /// 分页响应模型
    /// </summary>
    public class PagedResponse<T>
    {
        public List<T> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasNextPage => Page < TotalPages;
        public bool HasPreviousPage => Page > 1;
    }

    /// <summary>
    /// 实例过滤条件
    /// </summary>
    public class InstanceFilter
    {
        public string? Status { get; set; }
        public string? ProxyServer { get; set; }
        public DateTime? CreatedAfter { get; set; }
        public DateTime? CreatedBefore { get; set; }
        public bool? HasError { get; set; }
    }

    /// <summary>
    /// 导航请求模型
    /// </summary>
    public class NavigateRequest
    {
        public string Url { get; set; } = string.Empty;
        public int? Timeout { get; set; }
        public bool WaitForLoad { get; set; } = true;
        public string? WaitForSelector { get; set; }
    }

    /// <summary>
    /// 脚本执行请求模型
    /// </summary>
    public class ExecuteScriptRequest
    {
        public string Script { get; set; } = string.Empty;
        public Dictionary<string, object>? Arguments { get; set; }
        public int? Timeout { get; set; }
    }

    /// <summary>
    /// 截图请求模型
    /// </summary>
    public class ScreenshotRequest
    {
        public string? Selector { get; set; }
        public bool FullPage { get; set; } = false;
        public string Format { get; set; } = "png";
        public int? Quality { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
    }
}
