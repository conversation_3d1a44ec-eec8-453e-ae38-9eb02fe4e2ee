using System;
using System.IO;
using System.Text.Json;
using System.Collections.Generic;

// 简单测试JSON反序列化
public class TestJsonDeserialize
{
    public static void Main()
    {
        try
        {
            var jsonPath = @"D:\IIIIII\data\browser_instances.json";
            
            if (!File.Exists(jsonPath))
            {
                Console.WriteLine("文件不存在");
                return;
            }
            
            var jsonString = File.ReadAllText(jsonPath);
            Console.WriteLine($"JSON内容长度: {jsonString.Length}");
            Console.WriteLine($"JSON前200字符: {jsonString.Substring(0, Math.Min(200, jsonString.Length))}");
            
            // 测试反序列化
            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };
            
            var data = JsonSerializer.Deserialize<BrowserInstancesPersistenceData>(jsonString, jsonOptions);
            
            Console.WriteLine($"反序列化结果:");
            Console.WriteLine($"  SaveTime: {data?.SaveTime}");
            Console.WriteLine($"  Version: {data?.Version}");
            Console.WriteLine($"  Instances Count: {data?.Instances?.Count ?? -1}");
            
            if (data?.Instances != null)
            {
                foreach (var instance in data.Instances)
                {
                    Console.WriteLine($"    Instance: {instance.Id} - {instance.Status}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"错误: {ex.Message}");
            Console.WriteLine($"详细: {ex}");
        }
    }
}

public class BrowserInstancesPersistenceData
{
    public DateTime SaveTime { get; set; }
    public string Version { get; set; } = "1.0";
    public List<PersistentBrowserInstance> Instances { get; set; } = new();
}

public class PersistentBrowserInstance
{
    public string? Id { get; set; }
    public string? Status { get; set; }
    public string? ProxyServer { get; set; }
    public string? Url { get; set; }
    public DateTime CreatedTime { get; set; }
    public DateTime LastActiveTime { get; set; }
    public long MemoryUsage { get; set; }
    public string? Fingerprint { get; set; }
    public bool IsActive { get; set; }
    public int RequestCount { get; set; }
}
