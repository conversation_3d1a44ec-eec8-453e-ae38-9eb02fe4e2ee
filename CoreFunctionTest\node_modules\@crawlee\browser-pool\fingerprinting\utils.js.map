{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/fingerprinting/utils.ts"], "names": [], "mappings": ";;;AAEA,uEAAmE;AACnE,oEAAgE;AAEhE,mCAA4E;AAErE,MAAM,0BAA0B,GAAG,CAAC,aAA4B,EAA+B,EAAE;IACpG,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,aAAa,CAAC;IAEvD,MAAM,OAAO,GAAG;QACZ,OAAO,EAAE,wCAAwB;QACjC,OAAO,EAAE,CAAC,OAAO,CAAC;QAClB,QAAQ,EAAE,CAAC,cAAc,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;QACxD,gBAAgB,EAAE,CAAC,kBAAkB,EAAE,CAAC;KAC3C,CAAC;IAEF,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAXW,QAAA,0BAA0B,8BAWrC;AAEF,MAAM,cAAc,GAAG,CAAC,aAA4B,EAAE,aAAkB,EAAe,EAAE;IACrF,MAAM,EAAE,OAAO,EAAE,GAAG,aAAa,CAAC;IAClC,IAAI,WAAW,CAAC;IAEhB,IAAI,aAAa,YAAY,oCAAgB,EAAE,CAAC;QAC5C,WAAW,GAAG,OAAO,CAAC,IAAK,EAAE,CAAC;IAClC,CAAC;IACD,IAAI,aAAa,YAAY,kCAAe,EAAE,CAAC;QAC3C,WAAW,GAAG,aAAa,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;IAC3D,CAAC;IAED,QAAQ,WAAW,EAAE,CAAC;QAClB,KAAK,QAAQ;YACT,OAAO,mBAAW,CAAC,MAAM,CAAC;QAC9B,KAAK,SAAS;YACV,OAAO,mBAAW,CAAC,OAAO,CAAC;QAC/B;YACI,OAAO,mBAAW,CAAC,MAAM,CAAC;IAClC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,kBAAkB,GAAG,GAAyB,EAAE;IAClD,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;IAE7B,QAAQ,QAAQ,EAAE,CAAC;QACf,KAAK,OAAO;YACR,oCAAoC;YACpC,oDAAoC;QACxC,KAAK,QAAQ;YACT,gDAAkC;QACtC;YACI,mCAAmC;YACnC,gDAAkC;IAC1C,CAAC;AACL,CAAC,CAAC"}