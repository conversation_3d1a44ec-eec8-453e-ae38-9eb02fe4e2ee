// WPF主窗口后端代码 - 批量浏览器管理UI
// 功能说明：集成现有C#和Node.js后端组件，提供完整的UI操作功能

using System.Collections.ObjectModel;      // 用于数据绑定的集合
using System.ComponentModel;               // 用于属性变更通知
using System.Diagnostics;                  // 用于性能监控
using System.IO;                           // 用于文件和目录操作
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;            // 用于UI线程定时器
using BatchBrowserUI.Models;               // 导入UI数据模型
using BatchBrowserUI.Views;                // 引用UI视图组件

namespace BatchBrowserUI;

/// <summary>
/// 批量浏览器管理主窗口
/// 功能：集成现有后端组件，提供完整的批量浏览器管理界面
/// </summary>
public partial class MainWindow : Window, INotifyPropertyChanged
{    // 后端核心组件
    private BatchBrowserUI.Models.SimplifiedBatchBrowserController _batchController;   // 批量浏览器控制器
    private ChromeConfigManager _chromeConfigManager;                                   // Chrome配置管理器
    private DataPersistenceManager _dataPersistenceManager;                             // 数据持久化管理器
    
    // UI数据绑定集合
    private ObservableCollection<BrowserInstanceModel> _browserInstances;   // 浏览器实例列表
    private ObservableCollection<ProxyModel> _proxyList;                    // 代理服务器列表
    private ObservableCollection<string> _systemLogs;                       // 系统日志列表
    
    // 系统监控数据
    private SystemMonitorModel _systemMonitor;           // 系统性能监控数据
    private PerformanceCounter _cpuCounter;              // CPU性能计数器
    private PerformanceCounter _memoryCounter;           // 内存性能计数器
    
    // UI状态属性
    private bool _isEngineRunning;                       // 引擎运行状态
    private string _engineStatus;                        // 引擎状态文本
    private string _chromeStatus;                        // Chrome集成状态
    private DateTime _systemStartTime;                   // 系统启动时间
    
    // 定时器
    private DispatcherTimer _monitoringTimer;            // 性能监控定时器
    private DispatcherTimer _statusUpdateTimer;          // 状态更新定时器    /// <summary>
    /// 主窗口构造函数 - 初始化所有组件
    /// </summary>
    public MainWindow()
    {
        try
        {
            Console.WriteLine("🏗️ 开始初始化MainWindow...");
            
            // 首先初始化UI组件
            Console.WriteLine("📋 正在初始化UI组件...");
            InitializeComponent();
            Console.WriteLine("✅ UI组件初始化完成");
            
            // 初始化UI数据模型（必须在其他组件之前）
            Console.WriteLine("📊 正在初始化数据模型...");
            InitializeDataModels();
            Console.WriteLine("✅ 数据模型初始化完成");
            
            // 设置数据绑定上下文
            Console.WriteLine("🔗 设置数据绑定上下文...");
            DataContext = this;
            Console.WriteLine("✅ 数据绑定设置完成");
            
            // 记录系统启动时间
            _systemStartTime = DateTime.Now;
            
            // 现在可以安全地添加日志
            AddSystemLog("系统UI组件初始化完成...");
            
            // 初始化后端组件
            Console.WriteLine("⚙️ 正在初始化后端组件...");
            InitializeBackendComponents();
            Console.WriteLine("✅ 后端组件初始化完成");
            
            // 初始化性能监控
            Console.WriteLine("📈 正在初始化性能监控...");
            InitializePerformanceMonitoring();
            Console.WriteLine("✅ 性能监控初始化完成");
            
            // 初始化定时器
            Console.WriteLine("⏰ 正在初始化定时器...");
            InitializeTimers();
            Console.WriteLine("✅ 定时器初始化完成");
            
            // 最终日志
            AddSystemLog("系统初始化完成，准备启动批量浏览器管理器...");

            // 设置初始按钮状态
            UpdateButtonStates();

            Console.WriteLine("🎉 WPF主界面初始化完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ MainWindow初始化失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
            MessageBox.Show($"主窗口初始化失败: {ex.Message}\n\n详细信息: {ex.ToString()}", "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
            throw; // 重新抛出异常以确保应用程序不会继续
        }
    }

    /// <summary>
    /// 窗口关闭事件处理
    /// </summary>
    protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
    {
        try
        {
            AddSystemLog("正在保存数据并关闭应用程序...");

            // 保存浏览器实例数据
            SaveBrowserInstances();

            // 保存Chrome配置
            if (_chromeConfigManager != null && _dataPersistenceManager != null)
            {
                _dataPersistenceManager.SaveAppConfig(_chromeConfigManager.Config);
            }

            AddSystemLog("✅ 数据保存完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 关闭时保存数据失败: {ex.Message}");
        }

        base.OnClosing(e);
    }

    #region 公共属性 - 用于数据绑定

    /// <summary>
    /// 浏览器实例列表 - 绑定到DataGrid
    /// </summary>
    public ObservableCollection<BrowserInstanceModel> BrowserInstances
    {
        get => _browserInstances;
        set
        {
            Console.WriteLine($"🔍 BrowserInstances被设置: 旧值Count={_browserInstances?.Count ?? -1}, 新值Count={value?.Count ?? -1}");
            Console.WriteLine($"🔍 调用堆栈: {Environment.StackTrace}");
            _browserInstances = value;
            OnPropertyChanged(nameof(BrowserInstances));
        }
    }

    /// <summary>
    /// 代理服务器列表 - 绑定到DataGrid
    /// </summary>
    public ObservableCollection<ProxyModel> ProxyList
    {
        get => _proxyList;
        set
        {
            _proxyList = value;
            OnPropertyChanged(nameof(ProxyList));
        }
    }

    /// <summary>
    /// 系统日志列表 - 绑定到ListBox
    /// </summary>
    public ObservableCollection<string> SystemLogs
    {
        get => _systemLogs;
        set
        {
            _systemLogs = value;
            OnPropertyChanged(nameof(SystemLogs));
        }
    }

    /// <summary>
    /// 系统性能监控数据 - 绑定到监控面板
    /// </summary>
    public SystemMonitorModel SystemMonitor
    {
        get => _systemMonitor;
        set
        {
            _systemMonitor = value;
            OnPropertyChanged(nameof(SystemMonitor));
        }
    }

    /// <summary>
    /// 引擎运行状态
    /// </summary>
    public bool IsEngineRunning
    {
        get => _isEngineRunning;
        set
        {
            _isEngineRunning = value;
            OnPropertyChanged(nameof(IsEngineRunning));
        }
    }

    /// <summary>
    /// 引擎状态文本
    /// </summary>
    public string EngineStatus
    {
        get => _engineStatus;
        set
        {
            _engineStatus = value;
            OnPropertyChanged(nameof(EngineStatus));
        }
    }

    /// <summary>
    /// Chrome集成状态
    /// </summary>
    public string ChromeStatus
    {
        get => _chromeStatus;
        set
        {
            _chromeStatus = value;
            OnPropertyChanged(nameof(ChromeStatus));
        }
    }

    #endregion

    #region 初始化方法

    /// <summary>
    /// 初始化后端组件
    /// </summary>
    private void InitializeBackendComponents()
    {
        try
        {
            // 初始化批量浏览器控制器
            _batchController = new BatchBrowserUI.Models.SimplifiedBatchBrowserController();

            // 初始化数据持久化管理器
            _dataPersistenceManager = new DataPersistenceManager();

            // 初始化Chrome配置管理器
            _chromeConfigManager = new ChromeConfigManager();

            // 加载保存的Chrome配置
            _chromeConfigManager.Config = _dataPersistenceManager.LoadAppConfig();

            // 将Chrome配置管理器传递给批量浏览器控制器
            _batchController.SetChromeConfigManager(_chromeConfigManager);

            // 设置初始状态
            IsEngineRunning = false;
            EngineStatus = "已停止";
            ChromeStatus = "未连接";

            // 自动检测Chrome
            AutoDetectChrome();

            // 现在可以安全地加载保存的浏览器实例
            LoadSavedBrowserInstances();

            // 显示加载结果
            MessageBox.Show($"数据加载完成！\n浏览器实例数量: {BrowserInstances?.Count ?? 0}\n活跃实例: {BrowserInstances?.Count(b => b.IsActive) ?? 0}",
                "调试信息", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            AddSystemLog($"后端组件初始化失败: {ex.Message}");
            MessageBox.Show($"后端组件初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 初始化UI数据模型
    /// </summary>
    private void InitializeDataModels()
    {
        // 初始化集合
        BrowserInstances = new ObservableCollection<BrowserInstanceModel>();
        ProxyList = new ObservableCollection<ProxyModel>();
        SystemLogs = new ObservableCollection<string>();

        // 注意：这里不加载保存的实例，因为数据持久化管理器还没有初始化
        // 保存的实例将在后端组件初始化后加载
        
        // 初始化系统监控数据
        SystemMonitor = new SystemMonitorModel
        {
            EngineStatus = "已停止",
            ActiveInstances = 0,
            TotalRequests = 0,
            SuccessfulRequests = 0,
            ErrorCount = 0,
            TotalMemory = 8192, // 设置默认总内存8GB
            MemoryUsage = 0,
            CpuUsage = 0,
            TotalBrowsers = 0,
            ActiveBrowsers = 0,
            ProxyCount = 0,
            LastUpdated = DateTime.Now,
            IsEngineRunning = false
        };
        
        Console.WriteLine("UI数据模型初始化完成");
    }

    /// <summary>
    /// 加载保存的浏览器实例
    /// </summary>
    private void LoadSavedBrowserInstances()
    {
        try
        {
            Console.WriteLine("🔧 LoadSavedBrowserInstances 开始执行...");

            if (_dataPersistenceManager == null)
            {
                Console.WriteLine("❌ 数据持久化管理器未初始化");
                AddSystemLog("❌ 数据持久化管理器未初始化");
                return;
            }

            Console.WriteLine("📂 正在调用LoadBrowserInstances...");
            AddSystemLog("📂 正在加载保存的浏览器实例...");
            var savedInstances = _dataPersistenceManager.LoadBrowserInstances();

            Console.WriteLine($"📋 从文件加载了 {savedInstances.Count} 个实例");
            AddSystemLog($"📋 从文件加载了 {savedInstances.Count} 个实例");

            Console.WriteLine($"📋 当前BrowserInstances集合状态: Count={BrowserInstances?.Count ?? -1}");

            foreach (var instance in savedInstances)
            {
                Console.WriteLine($"   + 正在添加实例: {instance.Id}");
                BrowserInstances.Add(instance);
                Console.WriteLine($"   + 实例已添加，当前总数: {BrowserInstances.Count}");
                AddSystemLog($"   + 添加实例: {instance.Id} (状态: {instance.Status}, 活跃: {instance.IsActive})");
            }

            if (savedInstances.Count > 0)
            {
                Console.WriteLine($"✅ 已恢复 {savedInstances.Count} 个浏览器实例");
                AddSystemLog($"✅ 已恢复 {savedInstances.Count} 个浏览器实例");

                // 更新统计信息
                SystemMonitor.TotalBrowsers = BrowserInstances.Count;
                SystemMonitor.ActiveBrowsers = BrowserInstances.Count(b => b.IsActive);
                SystemMonitor.ActiveInstances = BrowserInstances.Count(b => b.IsActive);

                Console.WriteLine($"📊 统计更新: 总数={SystemMonitor.TotalBrowsers}, 活跃={SystemMonitor.ActiveBrowsers}");
                AddSystemLog($"📊 统计更新: 总数={SystemMonitor.TotalBrowsers}, 活跃={SystemMonitor.ActiveBrowsers}");
            }
            else
            {
                Console.WriteLine("📋 没有找到保存的浏览器实例");
                AddSystemLog("📋 没有找到保存的浏览器实例");
            }

            Console.WriteLine("🔧 LoadSavedBrowserInstances 执行完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 加载保存的浏览器实例失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
            AddSystemLog($"❌ 加载保存的浏览器实例失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 保存浏览器实例数据
    /// </summary>
    private void SaveBrowserInstances()
    {
        try
        {
            if (_dataPersistenceManager == null) return;

            var instancesList = BrowserInstances.ToList();
            var success = _dataPersistenceManager.SaveBrowserInstances(instancesList);

            if (success)
            {
                AddSystemLog($"💾 已保存 {instancesList.Count} 个浏览器实例");
            }
            else
            {
                AddSystemLog("❌ 保存浏览器实例失败");
            }
        }
        catch (Exception ex)
        {
            AddSystemLog($"❌ 保存浏览器实例异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 初始化性能监控组件
    /// </summary>
    private void InitializePerformanceMonitoring()
    {
        try
        {
            // 尝试安全初始化CPU性能计数器
            try
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _cpuCounter.NextValue(); // 第一次调用返回0，需要先调用一次
                Console.WriteLine("✅ CPU性能计数器初始化成功");
            }
            catch (Exception cpuEx)
            {
                Console.WriteLine($"⚠️ CPU性能计数器初始化失败: {cpuEx.Message}");
                _cpuCounter = null; // 设置为null以避免后续错误
            }
            
            // 尝试安全初始化内存性能计数器
            try
            {
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
                Console.WriteLine("✅ 内存性能计数器初始化成功");
            }
            catch (Exception memEx)
            {
                Console.WriteLine($"⚠️ 内存性能计数器初始化失败: {memEx.Message}");
                _memoryCounter = null; // 设置为null以避免后续错误
            }
            
            Console.WriteLine("🔧 性能监控组件初始化完成（部分功能可能不可用）");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 性能监控初始化失败: {ex.Message}");
            // 继续运行，但不使用性能监控
        }
    }

    /// <summary>
    /// 初始化定时器
    /// </summary>
    private void InitializeTimers()
    {
        // 性能监控定时器 - 每2秒更新一次
        _monitoringTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(2)
        };
        _monitoringTimer.Tick += MonitoringTimer_Tick;
        
        // 状态更新定时器 - 每5秒更新一次
        _statusUpdateTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(5)
        };
        _statusUpdateTimer.Tick += StatusUpdateTimer_Tick;
        
        // 启动定时器
        _monitoringTimer.Start();
        _statusUpdateTimer.Start();
        
        Console.WriteLine("定时器初始化完成");
    }

    /// <summary>
    /// 自动检测Chrome浏览器
    /// </summary>
    private void AutoDetectChrome()
    {
        try
        {
            var result = _chromeConfigManager.DetectChrome();

            if (result.Success)
            {
                ChromeStatus = $"已检测到 Chrome {result.Version}";

                // 更新UI中的Chrome路径显示
                if (ChromePathTextBox != null)
                {
                    ChromePathTextBox.Text = result.ChromePath;
                }

                AddSystemLog($"✅ Chrome检测成功: {result.ChromePath}");
                AddSystemLog($"📋 检测方式: {result.DetectionMethod}, 版本: {result.Version}");
            }
            else
            {
                ChromeStatus = "未检测到";
                AddSystemLog($"❌ Chrome检测失败: {result.Error}");
            }
        }
        catch (Exception ex)
        {
            ChromeStatus = "检测失败";
            AddSystemLog($"❌ Chrome自动检测异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新按钮状态
    /// </summary>
    private void UpdateButtonStates()
    {
        try
        {
            // 确保在UI线程上执行
            if (!Dispatcher.CheckAccess())
            {
                Dispatcher.Invoke(UpdateButtonStates);
                return;
            }

            // 启动引擎按钮 - 当引擎未运行时可用
            if (StartEngineButton != null)
                StartEngineButton.IsEnabled = !IsEngineRunning;

            // 停止引擎按钮 - 当引擎运行时可用
            if (StopEngineButton != null)
                StopEngineButton.IsEnabled = IsEngineRunning;

            // 创建实例按钮 - 当引擎运行时可用
            if (CreateInstancesButton != null)
                CreateInstancesButton.IsEnabled = IsEngineRunning;

            Console.WriteLine($"🔄 按钮状态已更新 - 引擎运行状态: {IsEngineRunning}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 更新按钮状态失败: {ex.Message}");
        }
    }

    #endregion

    #region 事件处理方法

    /// <summary>
    /// 启动引擎按钮点击事件
    /// </summary>
    private async void StartEngine_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            AddSystemLog("正在启动Node.js批量引擎...");
            
            // 禁用按钮防止重复点击
            var button = sender as Button;
            if (button != null) button.IsEnabled = false;
            
            // 启动批量浏览器系统
            var success = await _batchController.StartSystemAsync();
            
            if (success)
            {
                IsEngineRunning = true;
                EngineStatus = "运行中";
                ChromeStatus = "已连接";
                SystemMonitor.EngineStatus = "运行中";

                // 更新按钮状态
                UpdateButtonStates();

                AddSystemLog("✅ 批量引擎启动成功！");
                MessageBox.Show("批量引擎启动成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                AddSystemLog("❌ 批量引擎启动失败");
                MessageBox.Show("批量引擎启动失败，请检查Node.js环境和脚本文件", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            AddSystemLog($"启动引擎时发生异常: {ex.Message}");
            MessageBox.Show($"启动引擎时发生异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // 重新启用按钮并更新状态
            UpdateButtonStates();
        }
    }

    /// <summary>
    /// 停止引擎按钮点击事件
    /// </summary>
    private async void StopEngine_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            AddSystemLog("正在停止Node.js批量引擎...");
            
            // 禁用按钮防止重复点击
            var button = sender as Button;
            if (button != null) button.IsEnabled = false;
            
            // 停止系统
            await _batchController.StopSystemAsync();
            
            IsEngineRunning = false;
            EngineStatus = "已停止";
            ChromeStatus = "未连接";
            SystemMonitor.EngineStatus = "已停止";

            // 更新按钮状态
            UpdateButtonStates();

            // 注意：停止引擎时不清空浏览器实例列表，保留历史数据
            // 只更新活跃实例统计为0（因为引擎停止了，所有实例都不再活跃）
            foreach (var instance in BrowserInstances)
            {
                instance.IsActive = false;
                instance.Status = "已停止";
            }
            SystemMonitor.ActiveInstances = 0;
            SystemMonitor.ActiveBrowsers = 0;

            AddSystemLog("✅ 批量引擎已停止");
            MessageBox.Show("批量引擎已停止", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            AddSystemLog($"停止引擎时发生异常: {ex.Message}");
            MessageBox.Show($"停止引擎时发生异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // 重新启用按钮并更新状态
            UpdateButtonStates();
        }
    }

    /// <summary>
    /// 创建浏览器实例按钮点击事件
    /// </summary>
    private async void CreateInstances_Click(object sender, RoutedEventArgs e)
    {
        if (!IsEngineRunning)
        {
            MessageBox.Show("请先启动引擎再创建浏览器实例", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // 弹出对话框让用户输入创建数量
        var result = InputDialog.ShowDialog(
            this,
            "请输入要创建的浏览器实例数量（建议1-20个）：", 
            "创建浏览器实例", 
            "5");
        
        if (string.IsNullOrEmpty(result)) return;
        
        if (!int.TryParse(result, out int count) || count <= 0 || count > 50)
        {
            MessageBox.Show("请输入有效的数量（1-50）", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        try
        {
            AddSystemLog($"开始创建 {count} 个浏览器实例...");
            
            // 禁用按钮防止重复点击
            var button = sender as Button;
            if (button != null) button.IsEnabled = false;

            // 调用后端创建实例
            var creationResult = await _batchController.CreateBatchInstancesAsync(count);
            
            // 检查创建结果
            if (!creationResult.Success)
            {
                AddSystemLog($"❌ 创建实例失败: {creationResult.Error}");
                MessageBox.Show($"创建实例失败: {creationResult.Error}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // 更新UI显示 - 修复属性名称从Instances改为Data
            foreach (var instance in creationResult.Data)
            {
                // 使用Chrome配置管理器为每个实例创建隔离数据目录
                try
                {
                    var dataDir = _chromeConfigManager.CreateInstanceDataDirectory(instance.Id);
                    AddSystemLog($"创建隔离数据目录: {dataDir}");
                }
                catch (Exception dirEx)
                {
                    AddSystemLog($"⚠️ 创建数据目录失败: {dirEx.Message}");
                }

                var uiInstance = new BrowserInstanceModel
                {
                    Id = instance.Id, // 直接使用字符串ID，无需转换
                    Status = instance.Status, // 使用实例的实际状态
                    Proxy = instance.ProxyServer ?? "无代理", // 使用ProxyServer属性
                    Url = instance.Url,
                    Created = instance.CreatedTime, // 使用CreatedTime属性
                    Fingerprint = instance.Fingerprint, // 直接使用指纹字符串
                    Memory = (int)instance.MemoryUsage, // 转换为整数
                    RequestCount = 0,
                    // 设置其他必要的属性
                    IsActive = instance.IsActive,
                    ProxyServer = instance.ProxyServer ?? "无代理",
                    CreatedTime = instance.CreatedTime,
                    LastActiveTime = instance.LastActiveTime,
                    MemoryUsage = instance.MemoryUsage
                };

                BrowserInstances.Add(uiInstance);
            }
            
            // 更新统计信息
            SystemMonitor.ActiveInstances = BrowserInstances.Count;
            SystemMonitor.TotalBrowsers = BrowserInstances.Count;
            SystemMonitor.ActiveBrowsers = BrowserInstances.Count(b => b.IsActive);

            // 自动保存浏览器实例数据
            SaveBrowserInstances();

            AddSystemLog($"✅ 成功创建 {creationResult.Data.Count} 个浏览器实例");
            MessageBox.Show($"成功创建 {creationResult.Data.Count} 个浏览器实例", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            AddSystemLog($"创建浏览器实例失败: {ex.Message}");
            MessageBox.Show($"创建浏览器实例失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // 重新启用按钮
            var button = sender as Button;
            if (button != null) button.IsEnabled = true;
        }
    }

    /// <summary>
    /// 管理代理按钮点击事件
    /// </summary>
    private void ManageProxies_Click(object sender, RoutedEventArgs e)
    {
        // 切换到代理管理标签页
        if (MainTabControl.Items.Count > 1)
        {
            MainTabControl.SelectedIndex = 1; // 切换到代理管理标签
        }
        
        AddSystemLog("切换到代理管理界面");
    }

    /// <summary>
    /// 添加代理按钮点击事件
    /// </summary>
    private void AddProxy_Click(object sender, RoutedEventArgs e)
    {
        // 这里可以弹出添加代理的对话框
        // 暂时添加一个示例代理
        var newProxy = new ProxyModel
        {
            Address = "127.0.0.1",
            Port = 8080,
            Type = "HTTP",
            Status = "活跃",
            Username = "",
            Password = "",
            ResponseTime = Random.Shared.Next(50, 500),
            LastCheck = DateTime.Now,
            UsageCount = 0,
            IsActive = true
        };
        
        ProxyList.Add(newProxy);
        AddSystemLog($"添加新代理: {newProxy.FullAddress}");
    }

    /// <summary>
    /// 启动浏览器按钮点击事件
    /// </summary>
    private async void StartBrowserButton_Click(object sender, RoutedEventArgs e)
    {
        var button = sender as Button;
        var instance = button?.DataContext as BrowserInstanceModel;

        if (instance == null)
        {
            MessageBox.Show("无法获取实例信息", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        try
        {
            AddSystemLog($"正在启动浏览器实例: {instance.Id}");

            // 禁用按钮防止重复点击
            button.IsEnabled = false;

            // 使用Chrome配置管理器创建隔离数据目录
            var dataDir = _chromeConfigManager.CreateInstanceDataDirectory(instance.Id);
            AddSystemLog($"使用数据目录: {dataDir}");

            // 生成Chrome启动参数
            var chromeArgs = _chromeConfigManager.GenerateChromeArgs(instance.Id, instance.ProxyServer);
            AddSystemLog($"Chrome启动参数: {string.Join(" ", chromeArgs.Take(3))}... (共{chromeArgs.Count}个参数)");

            // 调用后端启动浏览器
            var result = await _batchController.StartBrowserInstanceAsync(instance.Id);

            if (result.Success)
            {
                instance.Status = "运行中";
                instance.IsActive = true;
                instance.LastActiveTime = DateTime.Now;

                AddSystemLog($"✅ 浏览器实例 {instance.Id} 启动成功");
                MessageBox.Show($"浏览器实例 {instance.Id} 启动成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                AddSystemLog($"❌ 启动浏览器实例失败: {result.Error}");
                MessageBox.Show($"启动浏览器实例失败: {result.Error}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            AddSystemLog($"启动浏览器实例时发生异常: {ex.Message}");
            MessageBox.Show($"启动浏览器实例时发生异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // 重新启用按钮
            if (button != null) button.IsEnabled = true;
        }
    }

    /// <summary>
    /// 打开数据目录按钮点击事件
    /// </summary>
    private void OpenDataDirButton_Click(object sender, RoutedEventArgs e)
    {
        var button = sender as Button;
        var instance = button?.DataContext as BrowserInstanceModel;

        if (instance == null)
        {
            MessageBox.Show("无法获取实例信息", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        try
        {
            // 使用Chrome配置管理器获取数据目录
            var dataDir = _chromeConfigManager.CreateInstanceDataDirectory(instance.Id);

            // 打开文件夹
            Process.Start("explorer.exe", dataDir);
            AddSystemLog($"打开数据目录: {dataDir}");
        }
        catch (Exception ex)
        {
            AddSystemLog($"打开数据目录失败: {ex.Message}");
            MessageBox.Show($"打开数据目录失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 删除选中的浏览器实例
    /// </summary>
    private async void DeleteInstance_Click(object sender, RoutedEventArgs e)
    {
        var selectedInstance = BrowserInstancesDataGrid.SelectedItem as BrowserInstanceModel;
        if (selectedInstance == null)
        {
            MessageBox.Show("请先选择要删除的浏览器实例", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            // 调用后端删除实例
            await _batchController.CloseInstanceAsync(selectedInstance.Id);
            
            // 从UI列表中移除
            BrowserInstances.Remove(selectedInstance);

            // 更新统计信息
            SystemMonitor.ActiveInstances = BrowserInstances.Count;

            // 保存更新后的实例列表
            SaveBrowserInstances();

            AddSystemLog($"删除浏览器实例: {selectedInstance.Id}");
        }
        catch (Exception ex)
        {
            AddSystemLog($"删除实例失败: {ex.Message}");
            MessageBox.Show($"删除实例失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// 刷新浏览器实例列表
    /// </summary>
    private async void RefreshInstancesButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            AddSystemLog("正在刷新浏览器实例列表...");
            
            // 禁用按钮防止重复点击
            var button = sender as Button;
            if (button != null) button.IsEnabled = false;
            
            // 重新从持久化文件加载实例信息
            var savedInstances = _dataPersistenceManager?.LoadBrowserInstances() ?? new List<BrowserInstanceModel>();

            // 清空当前列表
            BrowserInstances.Clear();

            // 重新添加保存的实例信息
            foreach (var instance in savedInstances)
            {
                BrowserInstances.Add(instance);
            }

            // 更新统计信息
            SystemMonitor.TotalBrowsers = BrowserInstances.Count;
            SystemMonitor.ActiveBrowsers = BrowserInstances.Count(b => b.IsActive);
            SystemMonitor.ActiveInstances = BrowserInstances.Count(b => b.IsActive);
            
            AddSystemLog($"✅ 实例列表已刷新，共 {savedInstances.Count} 个实例");
        }
        catch (Exception ex)
        {
            AddSystemLog($"刷新实例列表失败: {ex.Message}");
            MessageBox.Show($"刷新实例列表失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // 重新启用按钮
            var button = sender as Button;
            if (button != null) button.IsEnabled = true;
        }
    }

    /// <summary>
    /// 关闭所有浏览器实例
    /// </summary>
    private async void CloseAllInstancesButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (BrowserInstances.Count == 0)
            {
                MessageBox.Show("没有运行中的浏览器实例", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }
            
            var result = MessageBox.Show(
                $"确定要关闭所有 {BrowserInstances.Count} 个浏览器实例吗？", 
                "确认关闭", 
                MessageBoxButton.YesNo, 
                MessageBoxImage.Question);
                
            if (result != MessageBoxResult.Yes) return;
            
            AddSystemLog($"正在关闭 {BrowserInstances.Count} 个浏览器实例...");
            
            // 禁用按钮防止重复点击
            var button = sender as Button;
            if (button != null) button.IsEnabled = false;
            
            // 记录要关闭的实例数量
            int totalInstances = BrowserInstances.Count;
            int successCount = 0;
            int failCount = 0;
            
            // 逐个关闭实例
            var instancesToClose = BrowserInstances.ToList(); // 创建副本避免在遍历时修改集合
            
            foreach (var instance in instancesToClose)
            {
                try
                {
                    var success = await _batchController.CloseInstanceAsync(instance.Id);
                    if (success)
                    {
                        BrowserInstances.Remove(instance);
                        successCount++;
                        AddSystemLog($"✅ 已关闭实例: {instance.Id}");
                    }
                    else
                    {
                        failCount++;
                        AddSystemLog($"❌ 关闭实例失败: {instance.Id}");
                    }
                }
                catch (Exception ex)
                {
                    failCount++;
                    AddSystemLog($"❌ 关闭实例 {instance.Id} 时发生错误: {ex.Message}");
                }
            }
            
            // 更新统计信息
            SystemMonitor.ActiveInstances = BrowserInstances.Count;
            
            // 显示结果总结
            string resultMessage = $"批量关闭完成：\n成功: {successCount} 个\n失败: {failCount} 个";
            AddSystemLog($"📊 {resultMessage.Replace('\n', ' ')}");
            
            if (failCount > 0)
            {
                MessageBox.Show(resultMessage, "批量关闭结果", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            else
            {
                MessageBox.Show($"成功关闭所有 {successCount} 个浏览器实例", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            AddSystemLog($"批量关闭实例失败: {ex.Message}");
            MessageBox.Show($"批量关闭实例失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // 重新启用按钮
            var button = sender as Button;
            if (button != null) button.IsEnabled = true;
        }
    }

    /// <summary>
    /// 清除所有日志
    /// </summary>
    private void ClearLogs_Click(object sender, RoutedEventArgs e)
    {
        SystemLogs.Clear();
        AddSystemLog("日志已清除");
    }

    #endregion

    #region 事件处理程序 - 补充缺失的方法

    /// <summary>
    /// 启动引擎按钮点击事件 - 直接调用主要的启动方法
    /// </summary>
    private void StartEngineButton_Click(object sender, RoutedEventArgs e)
    {
        StartEngine_Click(sender, e);
    }

    /// <summary>
    /// 停止引擎按钮点击事件 - 直接调用主要的停止方法
    /// </summary>
    private void StopEngineButton_Click(object sender, RoutedEventArgs e)
    {
        StopEngine_Click(sender, e);
    }

    /// <summary>
    /// 创建实例按钮点击事件 - 直接调用主要的创建方法
    /// </summary>
    private void CreateInstancesButton_Click(object sender, RoutedEventArgs e)
    {
        CreateInstances_Click(sender, e);
    }

    /// <summary>
    /// 管理代理按钮点击事件 - 直接调用主要的管理方法
    /// </summary>
    private void ManageProxiesButton_Click(object sender, RoutedEventArgs e)
    {
        ManageProxies_Click(sender, e);
    }

    /// <summary>
    /// 批量导航按钮点击事件
    /// </summary>
    private async void BatchNavigateButton_Click(object sender, RoutedEventArgs e)
    {
        var dialog = new InputDialog("请输入要导航的URL:", "https://www.example.com");
        if (dialog.ShowDialog() == true)
        {
            string url = dialog.InputValue;
            if (!string.IsNullOrWhiteSpace(url))
            {
                AddLog($"🔄 批量导航到: {url}");
                
                try
                {
                    var success = await _batchController.BatchNavigateAsync(url);
                    if (success)
                    {
                        RefreshBrowserInstances();
                        AddLog("✅ 批量导航完成");
                    }
                    else
                    {
                        AddLog("❌ 批量导航失败");
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"❌ 批量导航时发生错误: {ex.Message}");
                }
            }
            else
            {
                AddLog("❌ 请输入有效的URL");
            }
        }
    }

    /// <summary>
    /// 批量执行按钮点击事件
    /// </summary>
    private async void BatchExecuteButton_Click(object sender, RoutedEventArgs e)
    {
        AddLog("⚡ 开始批量执行任务...");
        
        try
        {
            await Task.Delay(1000); // 模拟批量执行
            AddLog("✅ 批量执行任务完成");
        }
        catch (Exception ex)
        {
            AddLog($"❌ 批量执行时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 批量截图按钮点击事件
    /// </summary>
    private async void BatchScreenshotButton_Click(object sender, RoutedEventArgs e)
    {
        AddLog("📸 开始批量截图...");
        
        try
        {
            await Task.Delay(1000); // 模拟批量截图
            AddLog("✅ 批量截图完成");
        }
        catch (Exception ex)
        {
            AddLog($"❌ 批量截图时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 查看实例按钮点击事件
    /// </summary>
    private void ViewInstanceButton_Click(object sender, RoutedEventArgs e)
    {
        // 获取选中的实例
        if (BrowserInstances.Count > 0)
        {
            var firstInstance = BrowserInstances[0];
            AddLog($"👁️ 查看实例 {firstInstance.Id}");
        }
        else
        {
            AddLog("❌ 没有可查看的实例");
        }
    }

    /// <summary>
    /// 关闭实例按钮点击事件
    /// </summary>
    private async void CloseInstanceButton_Click(object sender, RoutedEventArgs e)
    {
        // 获取选中的实例
        if (BrowserInstances.Count > 0)
        {
            var firstInstance = BrowserInstances[0];
            AddLog($"🔄 正在关闭实例 {firstInstance.Id}...");
            
            try
            {
                var success = await _batchController.CloseInstanceAsync(firstInstance.Id);
                if (success)
                {
                    RefreshBrowserInstances();
                    AddLog($"✅ 实例 {firstInstance.Id} 已关闭");
                }
                else
                {
                    AddLog($"❌ 关闭实例 {firstInstance.Id} 失败");
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 关闭实例时发生错误: {ex.Message}");
            }
        }
        else
        {
            AddLog("❌ 没有可关闭的实例");
        }
    }

    /// <summary>
    /// 代理健康检查按钮点击事件
    /// </summary>
    private async void ProxyHealthCheckButton_Click(object sender, RoutedEventArgs e)
    {
        AddLog("🔍 开始代理健康检查...");
        
        try
        {
            await Task.Delay(1000); // 模拟健康检查
            RefreshProxyList();
            AddLog("✅ 代理健康检查完成");
        }
        catch (Exception ex)
        {
            AddLog($"❌ 代理健康检查时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 添加代理按钮点击事件
    /// </summary>
    private void AddProxyButton_Click(object sender, RoutedEventArgs e)
    {
        var dialog = new InputDialog("请输入代理地址 (host:port):", "127.0.0.1:8080");
        if (dialog.ShowDialog() == true)
        {
            string proxyAddress = dialog.InputValue;
            if (!string.IsNullOrWhiteSpace(proxyAddress))
            {
                // 解析代理地址
                var parts = proxyAddress.Split(':');
                if (parts.Length == 2 && int.TryParse(parts[1], out int port))
                {
                    var proxy = new ProxyModel
                    {
                        Address = proxyAddress,
                        Port = port,
                        Type = "HTTP",
                        Status = "未测试",
                        Username = "",
                        Password = ""
                    };
                    
                    ProxyList.Add(proxy);
                    AddLog($"✅ 已添加代理: {proxyAddress}");
                }
                else
                {
                    AddLog("❌ 代理地址格式错误，请使用 host:port 格式");
                }
            }
            else
            {
                AddLog("❌ 请输入有效的代理地址");
            }
        }
    }

    /// <summary>
    /// 测试代理按钮点击事件
    /// </summary>
    private async void TestProxyButton_Click(object sender, RoutedEventArgs e)
    {
        AddLog("🧪 开始测试选中的代理...");
        
        try
        {
            await Task.Delay(1000); // 模拟代理测试
            AddLog("✅ 代理测试完成");
        }
        catch (Exception ex)
        {
            AddLog($"❌ 代理测试时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 移除代理按钮点击事件
    /// </summary>
    private void RemoveProxyButton_Click(object sender, RoutedEventArgs e)
    {
        if (ProxyList.Count > 0)
        {
            var firstProxy = ProxyList[0];
            ProxyList.Remove(firstProxy);
            AddLog($"✅ 已移除代理: {firstProxy.Address}");
        }
        else
        {
            AddLog("❌ 没有可移除的代理");
        }
    }

    /// <summary>
    /// 清除日志按钮点击事件
    /// </summary>
    private void ClearLogButton_Click(object sender, RoutedEventArgs e)
    {
        SystemLogs.Clear();
        AddLog("📝 系统日志已清空");
    }

    /// <summary>
    /// 检测Chrome按钮点击事件
    /// </summary>
    private async void DetectChromeButton_Click(object sender, RoutedEventArgs e)
    {
        AddLog("🔍 正在重新检测Chrome浏览器...");

        try
        {
            // 禁用按钮防止重复点击
            var button = sender as Button;
            if (button != null) button.IsEnabled = false;

            // 模拟检测延时
            await Task.Delay(500);

            // 执行检测
            var result = _chromeConfigManager.DetectChrome();

            if (result.Success)
            {
                ChromeStatus = $"已检测到 Chrome {result.Version}";

                // 更新UI中的Chrome路径显示
                if (ChromePathTextBox != null)
                {
                    ChromePathTextBox.Text = result.ChromePath;
                }

                AddLog($"✅ Chrome检测成功: {result.ChromePath}");
                AddLog($"📋 检测方式: {result.DetectionMethod}, 版本: {result.Version}");

                MessageBox.Show($"Chrome检测成功！\n\n路径: {result.ChromePath}\n版本: {result.Version}\n检测方式: {result.DetectionMethod}",
                    "检测成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                ChromeStatus = "未检测到";
                AddLog($"❌ Chrome检测失败: {result.Error}");

                MessageBox.Show($"Chrome检测失败！\n\n错误信息: {result.Error}\n\n请确保已安装Chrome浏览器。",
                    "检测失败", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            ChromeStatus = "检测失败";
            AddLog($"❌ Chrome检测时发生错误: {ex.Message}");
            MessageBox.Show($"Chrome检测时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // 重新启用按钮
            var button = sender as Button;
            if (button != null) button.IsEnabled = true;
        }
    }

    /// <summary>
    /// 保存设置按钮点击事件
    /// </summary>
    private async void SaveSettingsButton_Click(object sender, RoutedEventArgs e)
    {
        AddLog("💾 正在保存Chrome设置...");

        try
        {
            // 禁用按钮防止重复点击
            var button = sender as Button;
            if (button != null) button.IsEnabled = false;

            // 从UI控件读取设置
            if (_chromeConfigManager != null)
            {
                _chromeConfigManager.Config.UseLocalChrome = UseLocalChromeCheckBox?.IsChecked ?? true;
                _chromeConfigManager.Config.UseHeadlessMode = HeadlessCheckBox?.IsChecked ?? false;
                _chromeConfigManager.Config.EnableAntiDetection = EnableAntiDetectionCheckBox?.IsChecked ?? true;
                _chromeConfigManager.Config.RandomFingerprint = RandomFingerprintCheckBox?.IsChecked ?? true;
                _chromeConfigManager.Config.SpoofUserAgent = SpoofUserAgentCheckBox?.IsChecked ?? true;
                _chromeConfigManager.Config.SpoofViewport = SpoofViewportCheckBox?.IsChecked ?? true;
                _chromeConfigManager.Config.DisableImages = !(EnableImagesCheckBox?.IsChecked ?? false);
                _chromeConfigManager.Config.MaxConcurrency = (int)(MaxConcurrencySlider?.Value ?? 10);
                _chromeConfigManager.Config.PageTimeout = (int)(PageTimeoutSlider?.Value ?? 30);

                AddLog("📋 Chrome配置已更新:");
                AddLog($"   - 使用本地Chrome: {_chromeConfigManager.Config.UseLocalChrome}");
                AddLog($"   - 无头模式: {_chromeConfigManager.Config.UseHeadlessMode}");
                AddLog($"   - 防检测: {_chromeConfigManager.Config.EnableAntiDetection}");
                AddLog($"   - 最大并发: {_chromeConfigManager.Config.MaxConcurrency}");
                AddLog($"   - 页面超时: {_chromeConfigManager.Config.PageTimeout}秒");
            }

            // 模拟保存延时
            await Task.Delay(500);

            // 保存配置到文件
            _dataPersistenceManager.SaveAppConfig(_chromeConfigManager.Config);

            AddLog("✅ Chrome设置保存成功");
            MessageBox.Show("Chrome设置已保存！\n\n新设置将在下次创建浏览器实例时生效。",
                "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            AddLog($"❌ 保存设置时发生错误: {ex.Message}");
            MessageBox.Show($"保存设置时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            // 重新启用按钮
            var button = sender as Button;
            if (button != null) button.IsEnabled = true;
        }
    }

    /// <summary>
    /// 重置设置按钮点击事件
    /// </summary>
    private async void ResetSettingsButton_Click(object sender, RoutedEventArgs e)
    {
        AddLog("🔄 正在重置设置...");
        
        try
        {
            await Task.Delay(500); // 模拟重置过程
            AddLog("✅ 设置重置成功");
        }
        catch (Exception ex)
        {
            AddLog($"❌ 重置设置时发生错误: {ex.Message}");
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 添加系统日志的简化方法
    /// </summary>
    private void AddLog(string message)
    {
        AddSystemLog(message);
    }

    /// <summary>
    /// 添加系统日志
    /// </summary>
    private void AddSystemLog(string message)
    {
        string timestamp = DateTime.Now.ToString("HH:mm:ss");
        string logMessage = $"[{timestamp}] {message}";
        
        // 在UI线程上添加日志
        if (Dispatcher.CheckAccess())
        {
            SystemLogs.Add(logMessage);
            
            // 保持日志数量在合理范围内
            if (SystemLogs.Count > 1000)
            {
                for (int i = 0; i < 100; i++)
                {
                    SystemLogs.RemoveAt(0);
                }
            }
            
            // 滚动到最新日志
            ScrollToLatestLog();
        }
        else
        {
            Dispatcher.BeginInvoke(() => AddSystemLog(message));
        }
    }

    /// <summary>
    /// 滚动到最新日志
    /// </summary>
    private void ScrollToLatestLog()
    {
        try
        {
            // 查找日志列表控件并滚动到底部
            if (SystemLogs.Count > 0)
            {
                // 使用Dispatcher确保在UI线程上执行
                Dispatcher.BeginInvoke(() =>
                {
                    try
                    {
                        // 滚动到最新的日志项
                        var listBox = FindName("LogListBox") as ListBox;
                        if (listBox != null && SystemLogs.Count > 0)
                        {
                            listBox.ScrollIntoView(SystemLogs.Last());
                        }
                    }
                    catch (Exception)
                    {
                        // 忽略滚动错误
                    }
                });
            }
        }
        catch (Exception)
        {
            // 忽略滚动错误
        }
    }

    /// <summary>
    /// 刷新浏览器实例列表
    /// </summary>
    private void RefreshBrowserInstances()
    {
        try
        {
            var instances = _batchController.GetAllInstances();
            
            // 清空当前列表
            BrowserInstances.Clear();
            
            // 重新添加实例
            foreach (var instance in instances)
            {
                var uiInstance = new BrowserInstanceModel
                {
                    Id = instance.Id.ToString(),
                    Status = instance.IsActive ? "运行中" : "已停止",                    Proxy = instance.ProxyAddress,
                    Url = instance.CurrentUrl,
                    Created = instance.CreatedAt,
                    Fingerprint = !string.IsNullOrEmpty(instance.Fingerprint) ? 
                        (instance.Fingerprint.Length > 50 ? instance.Fingerprint.Substring(0, 50) + "..." : instance.Fingerprint) : "无",
                    Memory = Random.Shared.Next(50, 200),
                    RequestCount = instance.UrlsProcessed
                };
                
                BrowserInstances.Add(uiInstance);
            }
            
            // 更新统计
            SystemMonitor.ActiveInstances = instances.Count(i => i.IsActive);
        }
        catch (Exception ex)
        {
            AddLog($"刷新实例列表失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 刷新代理列表
    /// </summary>
    private void RefreshProxyList()
    {
        try
        {
            // 这里可以从后端获取代理列表
            // 暂时只更新现有代理的状态
            foreach (var proxy in ProxyList)
            {
                proxy.LastCheck = DateTime.Now;
                proxy.ResponseTime = Random.Shared.Next(50, 500);
            }
        }
        catch (Exception ex)
        {
            AddLog($"刷新代理列表失败: {ex.Message}");
        }    }

    #endregion

    #region 定时器事件处理器
      /// <summary>
    /// 性能监控定时器事件处理器 - 更新性能数据
    /// </summary>
    private void MonitoringTimer_Tick(object sender, EventArgs e)
    {
        try
        {
            // 安全更新CPU使用率
            float cpuUsage = 0f;
            try
            {
                cpuUsage = _cpuCounter?.NextValue() ?? 0f;
            }
            catch (Exception cpuEx)
            {
                Console.WriteLine($"⚠️ CPU使用率获取失败: {cpuEx.Message}");
                cpuUsage = 0f; // 使用默认值
            }
            SystemMonitor.CpuUsage = (double)cpuUsage;
            
            // 安全更新内存使用率  
            float memoryUsage = 0f;
            try
            {
                memoryUsage = _memoryCounter?.NextValue() ?? 0f;
            }
            catch (Exception memEx)
            {
                Console.WriteLine($"⚠️ 内存使用率获取失败: {memEx.Message}");
                memoryUsage = 0f; // 使用默认值
            }
            SystemMonitor.MemoryUsage = (long)memoryUsage;
              
            // 更新运行时间
            TimeSpan uptime = DateTime.Now - _systemStartTime;
            SystemMonitor.Uptime = uptime;
            
            // 触发属性更新
            OnPropertyChanged(nameof(SystemMonitor));
            
            // 调试输出
            Console.WriteLine($"性能监控更新 - CPU: {cpuUsage:F1}%, 内存: {memoryUsage:F1}MB");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"性能监控更新失败: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 状态更新定时器事件处理器 - 更新系统状态
    /// </summary>
    private void StatusUpdateTimer_Tick(object sender, EventArgs e)
    {
        try
        {
            // 正确计算活跃实例数 - 基于IsActive属性
            var activeCount = BrowserInstances.Count(b => b.IsActive);
            var totalCount = BrowserInstances.Count;

            SystemMonitor.ActiveInstances = activeCount;
            SystemMonitor.TotalBrowsers = totalCount;
            SystemMonitor.ActiveBrowsers = activeCount;

            // 更新引擎状态
            SystemMonitor.EngineStatus = IsEngineRunning ? "运行中" : "已停止";

            // 更新最后更新时间
            OnPropertyChanged(nameof(SystemMonitor));

            // 调试输出
            Console.WriteLine($"状态更新 - 活跃实例: {activeCount}/{totalCount}, 引擎状态: {SystemMonitor.EngineStatus}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"状态更新失败: {ex.Message}");
        }
    }
    
    #endregion

    /// <summary>
    /// 属性变更通知事件
    /// </summary>
    public event PropertyChangedEventHandler? PropertyChanged;

    /// <summary>
    /// 触发属性变更通知
    /// </summary>
    /// <param name="propertyName">变更的属性名称</param>
    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}