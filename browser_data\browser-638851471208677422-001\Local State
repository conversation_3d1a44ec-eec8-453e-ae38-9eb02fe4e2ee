{"accessibility": {"captions": {"soda_registered_language_packs": ["cmn-Hans-CN"]}}, "autofill": {"ablation_seed": "0LvT67FVfog="}, "breadcrumbs": {"enabled": false, "enabled_time": "13393995123883722"}, "browser": {"whats_new": {"enabled_order": ["PdfSearchify"]}}, "hardware_acceleration_mode_previous": true, "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "management": {"platform": {"azure_active_directory": 0, "enterprise_mdm_win": 0}}, "optimization_guide": {"model_execution": {"last_usage_by_feature": {}}, "model_store_metadata": {}, "on_device": {"last_version": "137.0.7151.69", "model_crash_count": 0}}, "os_crypt": {"audit_enabled": true, "encrypted_key": "RFBBUEkBAAAA0Iyd3wEV0RGMegDAT8KX6wEAAAC936311rTrRbVOfQJeKCbSEAAAABwAAABHAG8AbwBnAGwAZQAgAEMAaAByAG8AbQBlAAAAEGYAAAABAAAgAAAAcwU6rmNhcA946X6cY4qeLm5B+66tMxaF1w32opotXnQAAAAADoAAAAACAAAgAAAAASO9bJ9ErdSXJPQjwI9eAiCPUbrrNsENemC2RNuLaeAwAAAAEYdb0/bDre7Z4nFtXdiMrXNtyUeKlxyCRdGhEZv6R11q4e5/DhFXtx80X2u2Y4IMQAAAAF57cIBHh3b8WmcgRawpN95Iqb8PBnj0jHgNUBhusa8LLt5IOaibuN09vyasli8GrbA5YbOXu0MJGWsAuz60aCw="}, "os_update_handler_enabled": true, "performance_intervention": {"last_daily_sample": "*****************"}, "policy": {"last_statistics_update": "*****************"}, "privacy_budget": {"meta_experiment_activation_salt": 0.*****************}, "profile": {"info_cache": {"Default": {"active_time": **********.004352, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_26", "background_apps": false, "default_avatar_fill_color": -2890755, "default_avatar_stroke_color": -********, "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": true, "is_using_default_name": true, "managed_user_id": "", "metrics_bucket_index": 1, "name": "您的 Chrome", "profile_color_seed": -********, "profile_highlight_color": -2890755, "signin.with_credential_provider": false, "user_name": ""}}, "last_active_profiles": [], "metrics": {"next_bucket_index": 2}, "profile_counts_reported": "*****************", "profiles_order": ["<PERSON><PERSON><PERSON>"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "session_id_generator_last_value": "*********", "signin": {"active_accounts_last_emitted": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": 0, "content": "", "format": 0}}, "tab_stats": {"discards_external": 0, "discards_frozen": 0, "discards_proactive": 0, "discards_suggested": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 1, "reloads_external": 0, "reloads_frozen": 0, "reloads_proactive": 0, "reloads_suggested": 0, "reloads_urgent": 0, "total_tab_count_max": 1, "window_count_max": 1}, "ukm": {"persisted_logs": []}, "uninstall_metrics": {"installation_date2": "**********"}, "user_experience_metrics": {"client_id2": "524ae025-e1bc-4d69-9d94-b6549c61aeec", "client_id_timestamp": "**********", "last_seen": {"CrashpadMetrics": "0"}, "limited_entropy_randomization_source": "E2BDFF455B92705ACBBAFB7F396E8FF7", "log_record_id": 1, "low_entropy_source3": 4203, "pseudo_low_entropy_source": 4092, "session_id": 0, "stability": {"browser_last_live_timestamp": "*****************", "exited_cleanly": true, "saved_system_profile": "CMKZ6MEGEhAxMzcuMC43MTUxLjY5LTY0GJDkmMIGIgV6aC1DTioYCgpXaW5kb3dzIE5UEgoxMC4wLjE5MDQ1MmQKBng4Nl82NBDNfhiAgLj2nf8fIgAoATCADzi4CEIKCAAQABoAMgA6AE3rE7lCVfJZuUJlAACAP2oYCgxHZW51aW5lSW50ZWwQ6Y0kGAQgACgAggEAigEAqgEGeDg2XzY0sAEBSgoNbSM6XhVkxDtgSgoNQZDythUMl5IASgoNkrdXsxUwrvLcSgoNBQ7w9BWAjX3KUARaAggAaggIABAAOABAAIABkOSYwgaYAQD4AesggAL///////////8BiAIBkgIkNTI0YWUwMjUtZTFiYy00ZDY5LTlkOTQtYjY1NDljNjFhZWVjqAL8H7ICWMWIACp+yLBs25szqe5qLX8qfizFGQzbCUy0VBMQADyTEfngPsplfhtv3FcQ2/tTLmN3Hn0eDEaNuJo+x7JyInbf76xnG/UF8TkL3aInRINJz2gzf0zzDV3xAs2AZsyEM53b", "saved_system_profile_hash": "70F6E57A015B1A5EAE15E6F65AD0DA44105C56F8", "stats_buildtime": "1748634818", "stats_version": "137.0.7151.69-64", "system_crash_count": 0}}, "variations_failed_to_fetch_seed_streak": 1, "variations_google_groups": {"Default": []}, "variations_limited_entropy_synthetic_trial_seed_v2": "13", "was": {"restarted": false}}