{"version": 3, "file": "browser-controller.js", "sourceRoot": "", "sources": ["../../src/abstract-classes/browser-controller.ts"], "names": [], "mappings": ";;;AACA,mCAAgC;AAChC,2DAAkD;AAElD,4CAA2C;AAI3C,sCAAgC;AAIhC,MAAM,2BAA2B,GAAG,IAAI,CAAC;AAczC;;;;;;;GAOG;AACH,MAAsB,iBAMpB,SAAQ,iCAA2G;IAkDjH,YAAY,aAAkG;QAC1G,KAAK,EAAE,CAAC;QAlDZ;;;;mBAAK,IAAA,eAAM,GAAE;WAAC;QAEd;;WAEG;QACH;;;;;WAAmG;QAEnG;;WAEG;QACH;;;;mBAAwB,SAAU;WAAC;QAEnC;;WAEG;QACH;;;;mBAAqG,SAAU;WAAC;QAEhH;;;WAGG;QACH;;;;;WAAmB;QAEnB;;;WAGG;QACH;;;;;WAAkB;QAElB;;;;mBAAW,KAAK;WAAC;QAEjB;;;;mBAAc,CAAC;WAAC;QAEhB;;;;mBAAa,CAAC;WAAC;QAEf;;;;mBAAmB,IAAI,CAAC,GAAG,EAAE;WAAC;QAEtB;;;;;WAAuB;QAEvB;;;;mBAAkB,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;gBACpD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;YAC7B,CAAC,CAAC;WAAC;QAEK;;;;;WAA2B;QAE3B;;;;mBAAoB,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;gBACtD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;YACjC,CAAC,CAAC;WAAC;QAIC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACvC,CAAC;IAED;;;;;OAKG;IACH,QAAQ;QACJ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;QACtF,CAAC;QACD,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,aAAa,CACT,OAAqB,EACrB,aAAkG;QAElG,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;QAClF,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,KAAK;QACP,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAE7B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,gDAAgD;YAChD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,YAAG,CAAC,KAAK,CAAC,oCAAqC,KAAe,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/F,CAAC;QAED,IAAI,CAAC,IAAI,iEAA2C,IAAI,CAAC,CAAC;QAE1D,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;gBACvB,YAAG,CAAC,KAAK,CAAC,mCAAmC,GAAG,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC;QACP,CAAC,EAAE,2BAA2B,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,IAAI;QACN,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAC7B,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,IAAI,iEAA2C,IAAI,CAAC,CAAC;IAC9D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO,CAAC,WAA4B;QACtC,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,MAAM,IAAI,CAAC,eAAe,CAAC;QAC3B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC9C,IAAA,mBAAS,GAAE,CAAC;QACZ,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAmB,EAAE,OAAiB;QACnD,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAmB;QAChC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;CA6BJ;AAjLD,8CAiLC"}