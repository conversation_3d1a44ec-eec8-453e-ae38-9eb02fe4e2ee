# Apify shared library

[![Build Status](https://github.com/apify/apify-shared-js/actions/workflows/test_and_release.yaml/badge.svg?branch=master)](https://github.com/apify/apify-shared-js/actions/workflows/test_and_release.yaml)

package | version
--------|--------
`@apify/consts` | [![NPM version](https://img.shields.io/npm/v/@apify/consts.svg)](https://www.npmjs.com/package/@apify/consts)
`@apify/datastructures` | [![NPM version](https://img.shields.io/npm/v/@apify/datastructures.svg)](https://www.npmjs.com/package/@apify/datastructures)
`@apify/git` | [![NPM version](https://img.shields.io/npm/v/@apify/git.svg)](https://www.npmjs.com/package/@apify/git)
`@apify/hubspot_client` | [![NPM version](https://img.shields.io/npm/v/@apify/hubspot_client.svg)](https://www.npmjs.com/package/@apify/hubspot_client)
`@apify/input_schema` | [![NPM version](https://img.shields.io/npm/v/@apify/input_schema.svg)](https://www.npmjs.com/package/@apify/input_schema)
`@apify/input_secrets` | [![NPM version](https://img.shields.io/npm/v/@apify/input_secrets.svg)](https://www.npmjs.com/package/@apify/input_secrets)
`@apify/image_proxy_client` | [![NPM version](https://img.shields.io/npm/v/@apify/image_proxy_client.svg)](https://www.npmjs.com/package/@apify/image_proxy_client)
`@apify/log` | [![NPM version](https://img.shields.io/npm/v/@apify/log.svg)](https://www.npmjs.com/package/@apify/log)
`@apify/markdown` | [![NPM version](https://img.shields.io/npm/v/@apify/markdown.svg)](https://www.npmjs.com/package/@apify/markdown)
`@apify/payment_qr_codes` | [![NPM version](https://img.shields.io/npm/v/@apify/payment_qr_codes.svg)](https://www.npmjs.com/package/@apify/payment_qr_codes)
`@apify/pseudo_url` | [![NPM version](https://img.shields.io/npm/v/@apify/pseudo_url.svg)](https://www.npmjs.com/package/@apify/pseudo_url)
`@apify/utilities` | [![NPM version](https://img.shields.io/npm/v/@apify/utilities.svg)](https://www.npmjs.com/package/@apify/utilities)
`@apify/timeout` | [![NPM version](https://img.shields.io/npm/v/@apify/timeout.svg)](https://www.npmjs.com/package/@apify/timeout)

Internal utilities and constants shared across <a href="https://www.apify.com">Apify</a> projects.
Unless you work at Apify ([interested?](https://apify.com/jobs)), you shouldn't use this package directly,
as things might break for you any time without deprecation warning.
