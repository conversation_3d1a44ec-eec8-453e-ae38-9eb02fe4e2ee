// src/regexs.ts
var namePartSubRegexStr = "[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+";
var nameSubRegexStr = `${namePartSubRegexStr}(?:\\.${namePartSubRegexStr})*`;
var domainPartSubRegexStr = "[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?";
var domainSubRegexStr = `${domainPartSubRegexStr}(?:\\.${domainPartSubRegexStr})+`;
var EMAIL_REGEX_STR = `${nameSubRegexStr}@${domainSubRegexStr}`;
var EMAIL_REGEX = new RegExp(`^${EMAIL_REGEX_STR}$`);
var COMMA_SEPARATED_EMAILS_REGEX_STR = `(${EMAIL_REGEX_STR})( *, *${EMAIL_REGEX_STR})*`;
var COMMA_SEPARATED_EMAILS_REGEX = new RegExp(`^${COMMA_SEPARATED_EMAILS_REGEX_STR}$`);
var GIT_REPO_REGEX = /^(?:git|ssh|https?|git@[-\w.]+):(\/\/)?(.*?)(\/?|#[-\d\w._:/]+?)$/;
var DNS_SAFE_NAME_REGEX = /^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9])$/;
var APIFY_PROXY_VALUE_REGEX = /^[\w._~]+$/;
var PROXY_URL_REGEX = /^(socks(4|4a|5|5h)?|https?):\/\/(([^:]+:)?[^@]*@)?[^.:@]+\.[^:]+:[\d]+?$/;
var KEY_VALUE_STORE_KEY_REGEX = /^([a-zA-Z0-9!\-_.'()]{1,256})$/;
var GITHUB_REGEX_STR = "[a-z\\d](?:[a-z\\d]|-(?=[a-z\\d])){0,38}";
var TWITTER_REGEX = /^@[a-z0-9_]{1,15}$/i;
var GITHUB_REGEX = new RegExp(`^${GITHUB_REGEX_STR}$`, "i");
var LINKEDIN_PROFILE_REGEX = /^(https?:\/\/)?(www\.)?([a-z]{2}\.)?linkedin.com\/(in|company)\/([A-Za-z0-9_-]+)\/?$/;
var URL_REGEX = /^https?:\/\//i;
var HTTP_URL_REGEX = new RegExp(
  "^(?:(?:(?:https?):)?\\/\\/)(?:\\S+(?::\\S*)?@)?(?:(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z0-9\\u00a1-\\uffff][a-z0-9\\u00a1-\\uffff_-]{0,62})?[a-z0-9\\u00a1-\\uffff]\\.)+(?:[a-z\\u00a1-\\uffff]{2,}\\.?|xn--[a-z0-9]+))(?::\\d{2,5})?(?:[/?#]\\S*)?$",
  "i"
);
var GITHUB_GIST_URL_REGEX = new RegExp(`^https:\\/\\/gist\\.github\\.com\\/${GITHUB_REGEX_STR}\\/[0-9a-f]{32}$`, "i");
var SPLIT_PATH_REGEX = /[^/]+/g;
var RELATIVE_URL_REGEX = /^(?!www\.|(?:http|ftp)s?:\/\/|[A-Za-z]:\|\/\/).*/i;
var CONTACT_LINK_REGEX = /^(mailto|tel|sms):.*$/i;
var APIFY_ID_REGEX = /[a-zA-Z0-9]{17}/;

// src/consts.ts
var FREE_SUBSCRIPTION_PLAN_CODE = "DEV";
var ACTOR_JOB_TYPES = {
  BUILD: "BUILD",
  RUN: "RUN"
};
var ACTOR_SOURCE_TYPES = {
  SOURCE_CODE: "SOURCE_CODE",
  SOURCE_FILES: "SOURCE_FILES",
  GIT_REPO: "GIT_REPO",
  TARBALL: "TARBALL",
  GITHUB_GIST: "GITHUB_GIST"
};
var ACTOR_EVENT_NAMES = {
  CPU_INFO: "cpuInfo",
  SYSTEM_INFO: "systemInfo",
  MIGRATING: "migrating",
  PERSIST_STATE: "persistState",
  ABORTING: "aborting"
};
var ACTOR_JOB_STATUSES = {
  READY: "READY",
  // started but not allocated to any worker yet
  RUNNING: "RUNNING",
  // running on worker
  SUCCEEDED: "SUCCEEDED",
  // finished and all good
  FAILED: "FAILED",
  // run or build failed
  TIMING_OUT: "TIMING-OUT",
  // timing out now
  TIMED_OUT: "TIMED-OUT",
  // timed out
  ABORTING: "ABORTING",
  // being aborted by user
  ABORTED: "ABORTED"
  // aborted by user
};
var WEBHOOK_DISPATCH_STATUSES = {
  ACTIVE: "ACTIVE",
  // Attempting to deliver the webhook
  SUCCEEDED: "SUCCEEDED",
  // Webhook was delivered
  FAILED: "FAILED"
  // All calls to webhook target URL failed
};
var ACTOR_JOB_TERMINAL_STATUSES = [
  ACTOR_JOB_STATUSES.SUCCEEDED,
  ACTOR_JOB_STATUSES.FAILED,
  ACTOR_JOB_STATUSES.TIMED_OUT,
  ACTOR_JOB_STATUSES.ABORTED
];
var WORKER_SERVICE_TYPES = {
  CRAWLING: "crawling",
  ACTOR: "actor"
};
var META_ORIGINS = {
  DEVELOPMENT: "DEVELOPMENT",
  // Job started from Developer console in Source section of actor
  WEB: "WEB",
  // Job started from other place on the website (either console or task detail page)
  API: "API",
  // Job started through API
  SCHEDULER: "SCHEDULER",
  // Job started through Scheduler
  TEST: "TEST",
  // Job started through test actor page
  WEBHOOK: "WEBHOOK",
  // Job started by the webhook
  ACTOR: "ACTOR",
  // Job started by another actor run
  CLI: "CLI",
  // Job started by apify CLI
  STANDBY: "STANDBY"
  // Job started by Actor Standby
};
var DOCKER_LABELS = {
  ACTOR_BUILD_ID: "com.apify.actBuildId",
  ACTOR_RUN_ID: "com.apify.actRunId",
  // Kept for backwards compatibility, will be removed soon (TODO: remove old usages!)
  /** @deprecated Use ACTOR_BUILD_ID instead! */
  ACT_BUILD_ID: "com.apify.actBuildId",
  /** @deprecated Use ACTOR_RUN_ID instead! */
  ACT_RUN_ID: "com.apify.actRunId"
};
var ACTOR_TYPES = {
  ACT: "acts",
  CRAWLER: "crawlers"
};
var ME_USER_NAME_PLACEHOLDER = "me";
var ANONYMOUS_USERNAME = "anonymous";
var USERNAME = {
  MIN_LENGTH: 3,
  MAX_LENGTH: 30,
  // Regex matching a potentially allowed username. The numbers must match MIN and MAX!
  // Note that username must also pass isForbiddenUser() test to be allowed!
  REGEX: /^[a-zA-Z0-9_.-]{3,30}$/
};
var EMAIL = {
  MAX_LENGTH: 254,
  // see https://www.rfc-editor.org/errata_search.php?rfc=3696&eid=1690
  REGEX: EMAIL_REGEX
};
var PROFILE_NAME = {
  MAX_LENGTH: 50,
  REGEX: /^(?!.*:\/\/)[^@><]*$/
  // Prohibits usage of @, <, > and ://
};
var DNS_SAFE_NAME_MAX_LENGTH = 63;
var ACTOR_NAME = {
  MIN_LENGTH: 3,
  MAX_LENGTH: DNS_SAFE_NAME_MAX_LENGTH,
  // DNS-safe string length
  REGEX: DNS_SAFE_NAME_REGEX
};
var SHORT_CRAWLER_ID_LENGTH = 5;
var BUILD_TAG_LATEST = "latest";
var ACTOR_RESTART_ON_ERROR = {
  MAX_RESTARTS: 3,
  // This needs to be low enough so that it only covers restart loops, rather than e.g.
  // errors during crawling of large lists of URLs
  INTERVAL_MILLIS: 1 * 60 * 1e3
};
var ACT_RESTART_ON_ERROR = ACTOR_RESTART_ON_ERROR;
var ACT_JOB_TYPES = ACTOR_JOB_TYPES;
var ACT_SOURCE_TYPES = ACTOR_SOURCE_TYPES;
var ACT_JOB_STATUSES = ACTOR_JOB_STATUSES;
var ACT_JOB_TERMINAL_STATUSES = ACTOR_JOB_TERMINAL_STATUSES;
var ACT_TYPES = ACTOR_TYPES;
var COMPUTE_UNIT_MB = 1024;
var COMPUTE_UNIT_MILLIS = 60 * 60 * 1e3;
var ACTOR_LIMITS = {
  // The actualy used limit is taken from private package @apify-packages/consts
  BUILD_DEFAULT_MEMORY_MBYTES: 4096,
  // Maximum duration of build in seconds.
  BUILD_TIMEOUT_SECS: 1800,
  // For each build or run container, set disk quota based on memory size
  RUN_DISK_TO_MEMORY_SIZE_COEFF: 2,
  // For each build or run container, set CPU cores based on memory size
  RUN_MEMORY_MBYTES_PER_CPU_CORE: 4096,
  // The default limit of memory for all running Actor jobs for free accounts.
  FREE_ACCOUNT_MAX_MEMORY_MBYTES: 8192,
  // The default limit of memory for all running Actor jobs for paid accounts.
  PAID_ACCOUNT_MAX_MEMORY_MBYTES: 65536,
  // Minimum and maximum memory for a single act run.
  MIN_RUN_MEMORY_MBYTES: 128,
  MAX_RUN_MEMORY_MBYTES: 32768,
  // Maximum size of actor input schema.
  INPUT_SCHEMA_MAX_BYTES: 500 * 1024,
  // Max length of run/build log in number of characters
  LOG_MAX_CHARS: 10 * 1024 * 1024
};
var DEFAULT_PLATFORM_LIMITS = {
  // Maximum number of actors per user
  MAX_ACTORS_PER_USER: 500,
  // Maximum number of tasks per user
  MAX_TASKS_PER_USER: 5e3,
  // Maximum number of schedules per user
  MAX_SCHEDULES_PER_USER: 100,
  // Maximum number of webhooks per user
  MAX_WEBHOOKS_PER_USER: 100,
  // Maximum number of concurrent actor runs per user for free accounts.
  FREE_ACCOUNT_MAX_CONCURRENT_ACTOR_RUNS_PER_USER: 25,
  // Maximum number of concurrent actor runs per user for paid accounts.
  PAID_ACCOUNT_MAX_CONCURRENT_ACTOR_RUNS_PER_USER: 250,
  // Maximum number of actors per scheduler
  MAX_ACTORS_PER_SCHEDULER: 10,
  // Maximum number of tasks per scheduler
  MAX_TASKS_PER_SCHEDULER: 10
};
var REQUEST_QUEUE_HEAD_MAX_LIMIT = 1e3;
var APIFY_ENV_VARS = {
  API_BASE_URL: "APIFY_API_BASE_URL",
  API_PUBLIC_BASE_URL: "APIFY_API_PUBLIC_BASE_URL",
  CHROME_EXECUTABLE_PATH: "APIFY_CHROME_EXECUTABLE_PATH",
  DEDICATED_CPUS: "APIFY_DEDICATED_CPUS",
  DISABLE_OUTDATED_WARNING: "APIFY_DISABLE_OUTDATED_WARNING",
  FACT: "APIFY_FACT",
  HEADLESS: "APIFY_HEADLESS",
  INPUT_SECRETS_PRIVATE_KEY_FILE: "APIFY_INPUT_SECRETS_PRIVATE_KEY_FILE",
  INPUT_SECRETS_PRIVATE_KEY_PASSPHRASE: "APIFY_INPUT_SECRETS_PRIVATE_KEY_PASSPHRASE",
  IS_AT_HOME: "APIFY_IS_AT_HOME",
  LOCAL_STORAGE_DIR: "APIFY_LOCAL_STORAGE_DIR",
  LOG_FORMAT: "APIFY_LOG_FORMAT",
  LOG_LEVEL: "APIFY_LOG_LEVEL",
  METAMORPH_AFTER_SLEEP_MILLIS: "APIFY_METAMORPH_AFTER_SLEEP_MILLIS",
  META_ORIGIN: "APIFY_META_ORIGIN",
  PERSIST_STATE_INTERVAL_MILLIS: "APIFY_PERSIST_STATE_INTERVAL_MILLIS",
  PROXY_HOSTNAME: "APIFY_PROXY_HOSTNAME",
  PROXY_PASSWORD: "APIFY_PROXY_PASSWORD",
  PROXY_PORT: "APIFY_PROXY_PORT",
  PROXY_STATUS_URL: "APIFY_PROXY_STATUS_URL",
  PURGE_ON_START: "APIFY_PURGE_ON_START",
  SDK_LATEST_VERSION: "APIFY_SDK_LATEST_VERSION",
  SYSTEM_INFO_INTERVAL_MILLIS: "APIFY_SYSTEM_INFO_INTERVAL_MILLIS",
  TOKEN: "APIFY_TOKEN",
  USER_ID: "APIFY_USER_ID",
  USER_IS_PAYING: "APIFY_USER_IS_PAYING",
  USER_PRICING_TIER: "APIFY_USER_PRICING_TIER",
  WORKFLOW_KEY: "APIFY_WORKFLOW_KEY",
  XVFB: "APIFY_XVFB",
  // Replaced by ACTOR_ENV_VARS, kept for backward compatibility:
  ACTOR_BUILD_ID: "APIFY_ACTOR_BUILD_ID",
  ACTOR_BUILD_NUMBER: "APIFY_ACTOR_BUILD_NUMBER",
  ACTOR_EVENTS_WS_URL: "APIFY_ACTOR_EVENTS_WS_URL",
  ACTOR_ID: "APIFY_ACTOR_ID",
  ACTOR_MAX_PAID_DATASET_ITEMS: "ACTOR_MAX_PAID_DATASET_ITEMS",
  ACTOR_RUN_ID: "APIFY_ACTOR_RUN_ID",
  ACTOR_TASK_ID: "APIFY_ACTOR_TASK_ID",
  CONTAINER_PORT: "APIFY_CONTAINER_PORT",
  CONTAINER_URL: "APIFY_CONTAINER_URL",
  DEFAULT_DATASET_ID: "APIFY_DEFAULT_DATASET_ID",
  DEFAULT_KEY_VALUE_STORE_ID: "APIFY_DEFAULT_KEY_VALUE_STORE_ID",
  DEFAULT_REQUEST_QUEUE_ID: "APIFY_DEFAULT_REQUEST_QUEUE_ID",
  INPUT_KEY: "APIFY_INPUT_KEY",
  MEMORY_MBYTES: "APIFY_MEMORY_MBYTES",
  STARTED_AT: "APIFY_STARTED_AT",
  TIMEOUT_AT: "APIFY_TIMEOUT_AT",
  // Deprecated, keep them for backward compatibility:
  ACT_ID: "APIFY_ACT_ID",
  ACT_RUN_ID: "APIFY_ACT_RUN_ID"
};
var ENV_VARS = APIFY_ENV_VARS;
var ACTOR_ENV_VARS = {
  BUILD_ID: "ACTOR_BUILD_ID",
  BUILD_NUMBER: "ACTOR_BUILD_NUMBER",
  BUILD_TAGS: "ACTOR_BUILD_TAGS",
  DEFAULT_DATASET_ID: "ACTOR_DEFAULT_DATASET_ID",
  DEFAULT_KEY_VALUE_STORE_ID: "ACTOR_DEFAULT_KEY_VALUE_STORE_ID",
  DEFAULT_REQUEST_QUEUE_ID: "ACTOR_DEFAULT_REQUEST_QUEUE_ID",
  EVENTS_WEBSOCKET_URL: "ACTOR_EVENTS_WEBSOCKET_URL",
  FULL_NAME: "ACTOR_FULL_NAME",
  ID: "ACTOR_ID",
  INPUT_KEY: "ACTOR_INPUT_KEY",
  MAX_PAID_DATASET_ITEMS: "ACTOR_MAX_PAID_DATASET_ITEMS",
  MAX_TOTAL_CHARGE_USD: "ACTOR_MAX_TOTAL_CHARGE_USD",
  MEMORY_MBYTES: "ACTOR_MEMORY_MBYTES",
  RUN_ID: "ACTOR_RUN_ID",
  STANDBY_PORT: "ACTOR_STANDBY_PORT",
  STANDBY_URL: "ACTOR_STANDBY_URL",
  STARTED_AT: "ACTOR_STARTED_AT",
  TASK_ID: "ACTOR_TASK_ID",
  TIMEOUT_AT: "ACTOR_TIMEOUT_AT",
  WEB_SERVER_PORT: "ACTOR_WEB_SERVER_PORT",
  WEB_SERVER_URL: "ACTOR_WEB_SERVER_URL"
};
var INTEGER_ENV_VARS = [
  // Actor env vars
  ACTOR_ENV_VARS.MAX_PAID_DATASET_ITEMS,
  ACTOR_ENV_VARS.MEMORY_MBYTES,
  ACTOR_ENV_VARS.STANDBY_PORT,
  ACTOR_ENV_VARS.WEB_SERVER_PORT,
  // Apify env vars
  APIFY_ENV_VARS.ACTOR_MAX_PAID_DATASET_ITEMS,
  APIFY_ENV_VARS.CONTAINER_PORT,
  APIFY_ENV_VARS.DEDICATED_CPUS,
  APIFY_ENV_VARS.MEMORY_MBYTES,
  APIFY_ENV_VARS.METAMORPH_AFTER_SLEEP_MILLIS,
  APIFY_ENV_VARS.PERSIST_STATE_INTERVAL_MILLIS,
  APIFY_ENV_VARS.PROXY_PORT,
  APIFY_ENV_VARS.SYSTEM_INFO_INTERVAL_MILLIS
];
var COMMA_SEPARATED_LIST_ENV_VARS = [
  ACTOR_ENV_VARS.BUILD_TAGS
];
var ACTOR_BUILD_ARGS = {
  ACTOR_PATH_IN_DOCKER_CONTEXT: "ACTOR_PATH_IN_DOCKER_CONTEXT"
};
var DEFAULT_CONTAINER_PORT = 4321;
var DEFAULT_ACTOR_STANDBY_PORT = DEFAULT_CONTAINER_PORT;
var LOCAL_STORAGE_SUBDIRS = {
  datasets: "datasets",
  keyValueStores: "key_value_stores",
  requestQueues: "request_queues"
};
var LOCAL_ACTOR_ENV_VARS = {
  [ACTOR_ENV_VARS.STANDBY_PORT]: DEFAULT_CONTAINER_PORT.toString(),
  [ACTOR_ENV_VARS.DEFAULT_DATASET_ID]: "default",
  [ACTOR_ENV_VARS.DEFAULT_KEY_VALUE_STORE_ID]: "default",
  [ACTOR_ENV_VARS.DEFAULT_REQUEST_QUEUE_ID]: "default",
  [ACTOR_ENV_VARS.WEB_SERVER_PORT]: DEFAULT_CONTAINER_PORT.toString(),
  [ACTOR_ENV_VARS.WEB_SERVER_URL]: `http://localhost:${DEFAULT_CONTAINER_PORT}`
  // Must match port line above!
};
var LOCAL_APIFY_ENV_VARS = {
  [APIFY_ENV_VARS.CONTAINER_PORT]: LOCAL_ACTOR_ENV_VARS.ACTOR_WEB_SERVER_PORT,
  [APIFY_ENV_VARS.CONTAINER_URL]: LOCAL_ACTOR_ENV_VARS.ACTOR_WEB_SERVER_URL,
  [APIFY_ENV_VARS.DEFAULT_DATASET_ID]: LOCAL_ACTOR_ENV_VARS.ACTOR_DEFAULT_DATASET_ID,
  [APIFY_ENV_VARS.DEFAULT_KEY_VALUE_STORE_ID]: LOCAL_ACTOR_ENV_VARS.ACTOR_DEFAULT_KEY_VALUE_STORE_ID,
  [APIFY_ENV_VARS.DEFAULT_REQUEST_QUEUE_ID]: LOCAL_ACTOR_ENV_VARS.ACTOR_DEFAULT_REQUEST_QUEUE_ID,
  [APIFY_ENV_VARS.PROXY_HOSTNAME]: "proxy.apify.com",
  [APIFY_ENV_VARS.PROXY_PORT]: 8e3.toString()
};
var LOCAL_ENV_VARS = LOCAL_APIFY_ENV_VARS;
var KEY_VALUE_STORE_KEYS = {
  INPUT: "INPUT",
  OUTPUT: "OUTPUT"
};
var MAX_PAYLOAD_SIZE_BYTES = 9437184;
var ACTOR_CATEGORIES = {
  AI: "AI",
  AGENTS: "Agents",
  AUTOMATION: "Automation",
  BUSINESS: "Business",
  COVID_19: "Covid-19",
  DEVELOPER_EXAMPLES: "Developer examples",
  DEVELOPER_TOOLS: "Developer tools",
  ECOMMERCE: "E-commerce",
  FOR_CREATORS: "For creators",
  GAMES: "Games",
  JOBS: "Jobs",
  LEAD_GENERATION: "Lead generation",
  MARKETING: "Marketing",
  NEWS: "News",
  SEO_TOOLS: "SEO tools",
  SOCIAL_MEDIA: "Social media",
  TRAVEL: "Travel",
  VIDEOS: "Videos",
  REAL_ESTATE: "Real estate",
  SPORTS: "Sports",
  EDUCATION: "Education",
  INTEGRATIONS: "Integrations",
  OTHER: "Other",
  OPEN_SOURCE: "Open source"
};
var ALL_ACTOR_CATEGORIES = {
  ...ACTOR_CATEGORIES
  // ...LEGACY_ACTOR_CATEGORIES,
};
var VERSION_INT_MAJOR_BASE = 1e7;
var VERSION_INT_MINOR_BASE = 1e5;
var USER_BASIC_TEXT_XSS_OPTIONS = {
  whiteList: {
    a: ["href", "title", "target"],
    code: [],
    strong: [],
    b: [],
    br: [],
    ul: [],
    li: [],
    ol: [],
    i: [],
    u: [],
    p: []
  }
};
var WEBHOOK_EVENT_TYPES = {
  ACTOR_RUN_CREATED: "ACTOR.RUN.CREATED",
  ACTOR_RUN_SUCCEEDED: "ACTOR.RUN.SUCCEEDED",
  ACTOR_RUN_FAILED: "ACTOR.RUN.FAILED",
  ACTOR_RUN_TIMED_OUT: "ACTOR.RUN.TIMED_OUT",
  ACTOR_RUN_ABORTED: "ACTOR.RUN.ABORTED",
  ACTOR_RUN_RESURRECTED: "ACTOR.RUN.RESURRECTED",
  ACTOR_BUILD_CREATED: "ACTOR.BUILD.CREATED",
  ACTOR_BUILD_SUCCEEDED: "ACTOR.BUILD.SUCCEEDED",
  ACTOR_BUILD_FAILED: "ACTOR.BUILD.FAILED",
  ACTOR_BUILD_TIMED_OUT: "ACTOR.BUILD.TIMED_OUT",
  ACTOR_BUILD_ABORTED: "ACTOR.BUILD.ABORTED",
  TEST: "TEST"
};
var WEBHOOK_EVENT_TYPE_GROUPS = {
  ACTOR_RUN: [
    WEBHOOK_EVENT_TYPES.ACTOR_RUN_CREATED,
    WEBHOOK_EVENT_TYPES.ACTOR_RUN_SUCCEEDED,
    WEBHOOK_EVENT_TYPES.ACTOR_RUN_FAILED,
    WEBHOOK_EVENT_TYPES.ACTOR_RUN_TIMED_OUT,
    WEBHOOK_EVENT_TYPES.ACTOR_RUN_ABORTED,
    WEBHOOK_EVENT_TYPES.ACTOR_RUN_RESURRECTED
  ],
  ACTOR_BUILD: [
    WEBHOOK_EVENT_TYPES.ACTOR_BUILD_CREATED,
    WEBHOOK_EVENT_TYPES.ACTOR_BUILD_SUCCEEDED,
    WEBHOOK_EVENT_TYPES.ACTOR_BUILD_FAILED,
    WEBHOOK_EVENT_TYPES.ACTOR_BUILD_TIMED_OUT,
    WEBHOOK_EVENT_TYPES.ACTOR_BUILD_ABORTED
  ],
  // If one of these occurs then we can be sure that none other can occur for the same triggerer.
  ACTOR_RUN_TERMINAL: [
    WEBHOOK_EVENT_TYPES.ACTOR_RUN_SUCCEEDED,
    WEBHOOK_EVENT_TYPES.ACTOR_RUN_FAILED,
    WEBHOOK_EVENT_TYPES.ACTOR_RUN_TIMED_OUT,
    WEBHOOK_EVENT_TYPES.ACTOR_RUN_ABORTED
  ],
  ACTOR_BUILD_TERMINAL: [
    WEBHOOK_EVENT_TYPES.ACTOR_BUILD_SUCCEEDED,
    WEBHOOK_EVENT_TYPES.ACTOR_BUILD_FAILED,
    WEBHOOK_EVENT_TYPES.ACTOR_BUILD_TIMED_OUT,
    WEBHOOK_EVENT_TYPES.ACTOR_BUILD_ABORTED
  ]
};
var WEBHOOK_DEFAULT_PAYLOAD_TEMPLATE = `{
    "userId": {{userId}},
    "createdAt": {{createdAt}},
    "eventType": {{eventType}},
    "eventData": {{eventData}},
    "resource": {{resource}}
}`;
var WEBHOOK_ALLOWED_PAYLOAD_VARIABLES = /* @__PURE__ */ new Set([
  "userId",
  "createdAt",
  "eventType",
  "eventData",
  "resource"
]);
var MAX_MULTIFILE_BYTES = 3 * 1024 ** 2;
var SOURCE_FILE_FORMATS = {
  TEXT: "TEXT",
  BASE64: "BASE64"
};
var PROJECT_STATUSES = {
  REQUEST: "REQUEST",
  SPECIFICATION: "SPECIFICATION",
  OFFERS: "OFFERS",
  DEPOSIT: "DEPOSIT",
  DEPOSIT_PAID: "DEPOSIT_PAID",
  NEW: "NEW",
  IN_PROGRESS: "IN_PROGRESS",
  QA: "QA",
  CUSTOMER_QA: "CUSTOMER_QA",
  READY_FOR_INVOICE: "READY_FOR_INVOICE",
  INVOICED: "INVOICED",
  PAID: "PAID",
  DELIVERED: "DELIVERED",
  CLOSED: "CLOSED",
  FINISHED: "FINISHED"
};
var FINISHED_PROJECT_STATUSES = [
  PROJECT_STATUSES.READY_FOR_INVOICE,
  PROJECT_STATUSES.INVOICED,
  PROJECT_STATUSES.PAID,
  PROJECT_STATUSES.DELIVERED,
  PROJECT_STATUSES.FINISHED
];
var MARKETPLACE_USER_ROLES = {
  DEVELOPER: "DEVELOPER",
  DATA_EXPERT: "DATA_EXPERT",
  CUSTOMER: "CUSTOMER"
};
var USER_PERSONA_TYPES = {
  DEVELOPER: "DEVELOPER",
  USER: "USER"
};
var GIT_MAIN_BRANCH = "main";
var REQUEST_QUEUE_MAX_REQUESTS_PER_BATCH_OPERATION = 25;
var ISSUES_STATUS_TYPES = {
  OPEN: "OPEN",
  CLOSED: "CLOSED"
};
var ISSUES_STATUS_ALL = "ALL";
var STORAGE_GENERAL_ACCESS = {
  /** Respect the user setting of the storage owner (default behavior). */
  FOLLOW_USER_SETTING: "FOLLOW_USER_SETTING",
  /** Only signed-in users with explicit access can read this storage. */
  RESTRICTED: "RESTRICTED",
  /** Anyone with a link, or the unique storage ID, can read the storage. */
  ANYONE_WITH_ID_CAN_READ: "ANYONE_WITH_ID_CAN_READ",
  /** Anyone with a link, the unique storage ID, or the storage name, can read the storage. */
  ANYONE_WITH_NAME_CAN_READ: "ANYONE_WITH_NAME_CAN_READ"
};
var RUN_GENERAL_ACCESS = {
  /** Respect the user setting of the run owner (default behavior). */
  FOLLOW_USER_SETTING: "FOLLOW_USER_SETTING",
  /** Only signed-in users with explicit access can read this run. */
  RESTRICTED: "RESTRICTED",
  /** Anyone with a link, or the unique run ID, can read the run. */
  ANYONE_WITH_ID_CAN_READ: "ANYONE_WITH_ID_CAN_READ"
};
export {
  ACTOR_BUILD_ARGS,
  ACTOR_CATEGORIES,
  ACTOR_ENV_VARS,
  ACTOR_EVENT_NAMES,
  ACTOR_JOB_STATUSES,
  ACTOR_JOB_TERMINAL_STATUSES,
  ACTOR_JOB_TYPES,
  ACTOR_LIMITS,
  ACTOR_NAME,
  ACTOR_RESTART_ON_ERROR,
  ACTOR_SOURCE_TYPES,
  ACTOR_TYPES,
  ACT_JOB_STATUSES,
  ACT_JOB_TERMINAL_STATUSES,
  ACT_JOB_TYPES,
  ACT_RESTART_ON_ERROR,
  ACT_SOURCE_TYPES,
  ACT_TYPES,
  ALL_ACTOR_CATEGORIES,
  ANONYMOUS_USERNAME,
  APIFY_ENV_VARS,
  APIFY_ID_REGEX,
  APIFY_PROXY_VALUE_REGEX,
  BUILD_TAG_LATEST,
  COMMA_SEPARATED_EMAILS_REGEX,
  COMMA_SEPARATED_EMAILS_REGEX_STR,
  COMMA_SEPARATED_LIST_ENV_VARS,
  COMPUTE_UNIT_MB,
  COMPUTE_UNIT_MILLIS,
  CONTACT_LINK_REGEX,
  DEFAULT_ACTOR_STANDBY_PORT,
  DEFAULT_CONTAINER_PORT,
  DEFAULT_PLATFORM_LIMITS,
  DNS_SAFE_NAME_MAX_LENGTH,
  DNS_SAFE_NAME_REGEX,
  DOCKER_LABELS,
  EMAIL,
  EMAIL_REGEX,
  EMAIL_REGEX_STR,
  ENV_VARS,
  FINISHED_PROJECT_STATUSES,
  FREE_SUBSCRIPTION_PLAN_CODE,
  GITHUB_GIST_URL_REGEX,
  GITHUB_REGEX,
  GIT_MAIN_BRANCH,
  GIT_REPO_REGEX,
  HTTP_URL_REGEX,
  INTEGER_ENV_VARS,
  ISSUES_STATUS_ALL,
  ISSUES_STATUS_TYPES,
  KEY_VALUE_STORE_KEYS,
  KEY_VALUE_STORE_KEY_REGEX,
  LINKEDIN_PROFILE_REGEX,
  LOCAL_ACTOR_ENV_VARS,
  LOCAL_APIFY_ENV_VARS,
  LOCAL_ENV_VARS,
  LOCAL_STORAGE_SUBDIRS,
  MARKETPLACE_USER_ROLES,
  MAX_MULTIFILE_BYTES,
  MAX_PAYLOAD_SIZE_BYTES,
  META_ORIGINS,
  ME_USER_NAME_PLACEHOLDER,
  PROFILE_NAME,
  PROJECT_STATUSES,
  PROXY_URL_REGEX,
  RELATIVE_URL_REGEX,
  REQUEST_QUEUE_HEAD_MAX_LIMIT,
  REQUEST_QUEUE_MAX_REQUESTS_PER_BATCH_OPERATION,
  RUN_GENERAL_ACCESS,
  SHORT_CRAWLER_ID_LENGTH,
  SOURCE_FILE_FORMATS,
  SPLIT_PATH_REGEX,
  STORAGE_GENERAL_ACCESS,
  TWITTER_REGEX,
  URL_REGEX,
  USERNAME,
  USER_BASIC_TEXT_XSS_OPTIONS,
  USER_PERSONA_TYPES,
  VERSION_INT_MAJOR_BASE,
  VERSION_INT_MINOR_BASE,
  WEBHOOK_ALLOWED_PAYLOAD_VARIABLES,
  WEBHOOK_DEFAULT_PAYLOAD_TEMPLATE,
  WEBHOOK_DISPATCH_STATUSES,
  WEBHOOK_EVENT_TYPES,
  WEBHOOK_EVENT_TYPE_GROUPS,
  WORKER_SERVICE_TYPES
};
//# sourceMappingURL=index.mjs.map