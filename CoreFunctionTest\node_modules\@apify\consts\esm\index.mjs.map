{"version": 3, "sources": ["../../src/regexs.ts", "../../src/consts.ts"], "sourcesContent": ["// Parts for building an email regex (email will be constructed as `name@domain`)\n// name parts can be alnum + some special characters\nconst namePartSubRegexStr = '[a-zA-Z0-9!#$%&\\'*+/=?^_`{|}~-]+';\n// name is 1+ name parts joined by periods (no leading or dangling period, no consecutive periods)\nconst nameSubRegexStr = `${namePartSubRegexStr}(?:\\\\.${namePartSubRegexStr})*`;\n// domain parts can be alnum and dash characters (no leading and dangling dashes, max 63 chars long)\nconst domainPartSubRegexStr = '[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?'; //\n// doman is 2+ domain parts joined by periods (no leading or dangling period, no consecutive periods)\nconst domainSubRegexStr = `${domainPartSubRegexStr}(?:\\\\.${domainPartSubRegexStr})+`;\n\n/**\n * Email validation regexp adapted from https://html.spec.whatwg.org/multipage/forms.html#valid-e-mail-address\n * with our restriction that hostname must be a TLD! (will not match example@localhost)\n * and two consecutive dots in name are not allowed (based on Mailgun convention, will <NAME_EMAIL>)\n */\nexport const EMAIL_REGEX_STR = `${nameSubRegexStr}@${domainSubRegexStr}`;\n\n/**\n * Matches a string containing valid email\n * Hostname must be a TLD! (will not match example@localhost)\n */\nexport const EMAIL_REGEX = new RegExp(`^${EMAIL_REGEX_STR}$`);\n\n/**\n * Matches a string containing single email or multiple emails separated by comma\n * Hostname must be a TLD! (will not match example@localhost)\n */\nexport const COMMA_SEPARATED_EMAILS_REGEX_STR = `(${EMAIL_REGEX_STR})( *, *${EMAIL_REGEX_STR})*`;\n\n/**\n * Matches a string containing single email or multiple emails separated by comma\n * Hostname must be a TLD! (will not match example@localhost)\n */\nexport const COMMA_SEPARATED_EMAILS_REGEX = new RegExp(`^${COMMA_SEPARATED_EMAILS_REGEX_STR}$`);\n\n/**\n * Comes from https://github.com/jonschlinkert/is-git-url/ but we have:\n * - added support for ...:/dir/subdir syntax\n */\nexport const GIT_REPO_REGEX = /^(?:git|ssh|https?|git@[-\\w.]+):(\\/\\/)?(.*?)(\\/?|#[-\\d\\w._:/]+?)$/;\n\n/**\n * Matches a string that might be used in a hostname (e.g. \"my-host-name\")\n */\nexport const DNS_SAFE_NAME_REGEX = /^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9])$/;\n\n/**\n * Regular expression to validate Apify Proxy group name and session ID.\n * This must correspond to REGEX_STR_USERNAME_VALUE in apify-system!\n */\nexport const APIFY_PROXY_VALUE_REGEX = /^[\\w._~]+$/;\n\n/**\n * Regular expression to validate proxy urls, matches\n * http://asd:<EMAIL>:8000\n * http://123123:qweqwe:<EMAIL>:55555\n * http://proxy.apify.com:5000\n * http://<EMAIL>:5000\n */\nexport const PROXY_URL_REGEX = /^(socks(4|4a|5|5h)?|https?):\\/\\/(([^:]+:)?[^@]*@)?[^.:@]+\\.[^:]+:[\\d]+?$/;\n\n/**\n * AWS S3 docs say:\n * \"The following character sets are generally safe for use in key names:\n * - Alphanumeric characters [0-9a-zA-Z]\n * - Special characters !, -, _, ., *, ', (, and )\"\n * However, some of those characters are not valid across Win/Unix OS.\n * Therefore we allow only a subset and limit the length to 256 characters (TODO: document this)\n */\nexport const KEY_VALUE_STORE_KEY_REGEX = /^([a-zA-Z0-9!\\-_.'()]{1,256})$/;\n\n// taken from https://github.com/shinnn/github-username-regex\nconst GITHUB_REGEX_STR = '[a-z\\\\d](?:[a-z\\\\d]|-(?=[a-z\\\\d])){0,38}';\n\nexport const TWITTER_REGEX = /^@[a-z0-9_]{1,15}$/i;\nexport const GITHUB_REGEX = new RegExp(`^${GITHUB_REGEX_STR}$`, 'i');\n\n/**\n * For matching linkedin URLs for both profiles and companies.\n * Used for validating urls in user settings.\n */\nexport const LINKEDIN_PROFILE_REGEX = /^(https?:\\/\\/)?(www\\.)?([a-z]{2}\\.)?linkedin.com\\/(in|company)\\/([A-Za-z0-9_-]+)\\/?$/;\n\n/**\n * @deprecated Discontinue usage of this regexps, in favor of HTTP_URL_REGEX\n */\nexport const URL_REGEX = /^https?:\\/\\//i;\n\n// Inspired by https://gist.github.com/dperini/729294, but doesn't match FTP URLs\n/* eslint-disable */\nexport const HTTP_URL_REGEX = new RegExp(\n    '^' +\n    // protocol identifier (optional)\n    // short syntax // still required\n    // NOTE: We removed \"|ftp\"\n    '(?:(?:(?:https?):)?\\\\/\\\\/)' +\n    // user:pass BasicAuth (optional)\n    '(?:\\\\S+(?::\\\\S*)?@)?' +\n    '(?:' +\n    // IP address exclusion\n    // private & local networks\n    '(?!(?:10|127)(?:\\\\.\\\\d{1,3}){3})' +\n    '(?!(?:169\\\\.254|192\\\\.168)(?:\\\\.\\\\d{1,3}){2})' +\n    '(?!172\\\\.(?:1[6-9]|2\\\\d|3[0-1])(?:\\\\.\\\\d{1,3}){2})' +\n    // IP address dotted notation octets\n    // excludes loopback network 0.0.0.0\n    // excludes reserved space >= *********\n    // excludes network & broadcast addresses\n    // (first & last IP address of each class)\n    '(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])' +\n    '(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}' +\n    '(?:\\\\.(?:[1-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))' +\n    '|' +\n    // host & domain names, may end with dot\n    // can be replaced by a shortest alternative\n    // (?![-_])(?:[-\\\\w\\\\u00a1-\\\\uffff]{0,63}[^-_]\\\\.)+\n    '(?:' +\n    '(?:' +\n    '[a-z0-9\\\\u00a1-\\\\uffff]' +\n    '[a-z0-9\\\\u00a1-\\\\uffff_-]{0,62}' +\n    ')?' +\n    '[a-z0-9\\\\u00a1-\\\\uffff]\\\\.' +\n    ')+' +\n    // TLD identifier name, may end with dot\n    // NOTE: \"|xn--[a-z0-9]+\" is our addition to support IDNs like \"http://xn--80aaxitdbjk.xn--p1ai\",\n    // they can be used in a browser, so we consider them valid\n    '(?:[a-z\\\\u00a1-\\\\uffff]{2,}\\\\.?|xn--[a-z0-9]+)' +\n    ')' +\n    // port number (optional)\n    '(?::\\\\d{2,5})?' +\n    // resource path (optional)\n    '(?:[/?#]\\\\S*)?' +\n    '$', 'i',\n);\n/* eslint-enable */\n\n// E.g. https://gist.github.com/jancurn/2dbe83fea77c439b1119fb3f118513e7\nexport const GITHUB_GIST_URL_REGEX = new RegExp(`^https:\\\\/\\\\/gist\\\\.github\\\\.com\\\\/${GITHUB_REGEX_STR}\\\\/[0-9a-f]{32}$`, 'i');\n\n/**\n * Split's path /aaa/bbb/ccc into an array ['aaa', 'bbb', 'ccc].\n */\nexport const SPLIT_PATH_REGEX = /[^/]+/g;\n\n/**\n * Check if a URL is relative, i.e. does not start with a protocol\n */\nexport const RELATIVE_URL_REGEX = /^(?!www\\.|(?:http|ftp)s?:\\/\\/|[A-Za-z]:\\|\\/\\/).*/i;\n\n/**\n * Check if a link is a mailto/tel/sms type\n */\nexport const CONTACT_LINK_REGEX = /^(mailto|tel|sms):.*$/i;\n\n/**\n * Regular expression to match valid ID - 17 alphanumeric chars including chars restricted by SimpleSchema.RegEx.Id (1,l,0,O),\n * because we have user objects with that in database.\n * @type {RegExp}\n */\n// TODO: @fnesveda [2022-08-15] revert to stricter regex /^[a-zA-Z0-9]{17}$/ once we properly delete user yZtyxMUADJHyInTIdl\nexport const APIFY_ID_REGEX = /[a-zA-Z0-9]{17}/;\n", "import type { ValueOf } from './helpers';\nimport { DNS_SAFE_NAME_REGEX, EMAIL_REGEX } from './regexs';\n\nexport const FREE_SUBSCRIPTION_PLAN_CODE = 'DEV';\n\nexport const ACTOR_JOB_TYPES = {\n    BUILD: 'BUILD',\n    RUN: 'RUN',\n} as const;\n\nexport const ACTOR_SOURCE_TYPES = {\n    SOURCE_CODE: 'SOURCE_CODE',\n    SOURCE_FILES: 'SOURCE_FILES',\n    GIT_REPO: 'GIT_REPO',\n    TARBALL: 'TARBALL',\n    GITHUB_GIST: 'GITHUB_GIST',\n} as const;\n\nexport const ACTOR_EVENT_NAMES = {\n    CPU_INFO: 'cpuInfo',\n    SYSTEM_INFO: 'systemInfo',\n    MIGRATING: 'migrating',\n    PERSIST_STATE: 'persistState',\n    ABORTING: 'aborting',\n} as const;\n\n/**\n * Dictionary of possible values for 'status' field of act2Builds or act2Runs collections.\n */\nexport const ACTOR_JOB_STATUSES = {\n    READY: 'READY', // started but not allocated to any worker yet\n    RUNNING: 'RUNNING', // running on worker\n    SUCCEEDED: 'SUCCEEDED', // finished and all good\n    FAILED: 'FAILED', // run or build failed\n    TIMING_OUT: 'TIMING-OUT', // timing out now\n    TIMED_OUT: 'TIMED-OUT', // timed out\n    ABORTING: 'ABORTING', // being aborted by user\n    ABORTED: 'ABORTED', // aborted by user\n} as const;\n\n/**\n * Dictionary of possible values for 'status' field of webhookDispatches collections.\n */\nexport const WEBHOOK_DISPATCH_STATUSES = {\n    ACTIVE: 'ACTIVE', // Attempting to deliver the webhook\n    SUCCEEDED: 'SUCCEEDED', // Webhook was delivered\n    FAILED: 'FAILED', // All calls to webhook target URL failed\n} as const;\n\n/**\n * An array of act jobs statuses that are final for the jobs.\n */\nexport const ACTOR_JOB_TERMINAL_STATUSES = [\n    ACTOR_JOB_STATUSES.SUCCEEDED,\n    ACTOR_JOB_STATUSES.FAILED,\n    ACTOR_JOB_STATUSES.TIMED_OUT,\n    ACTOR_JOB_STATUSES.ABORTED,\n];\n\n// NOTE: for legacy reasons these are lower-case, maybe we should migrate to upper case later.\n// these strings are also referenced from upstart-worker.conf !\nexport const WORKER_SERVICE_TYPES = {\n    CRAWLING: 'crawling',\n    ACTOR: 'actor',\n} as const;\n\nexport const META_ORIGINS = {\n    DEVELOPMENT: 'DEVELOPMENT', // Job started from Developer console in Source section of actor\n    WEB: 'WEB', // Job started from other place on the website (either console or task detail page)\n    API: 'API', // Job started through API\n    SCHEDULER: 'SCHEDULER', // Job started through Scheduler\n    TEST: 'TEST', // Job started through test actor page\n    WEBHOOK: 'WEBHOOK', // Job started by the webhook\n    ACTOR: 'ACTOR', // Job started by another actor run\n    CLI: 'CLI', // Job started by apify CLI\n    STANDBY: 'STANDBY', // Job started by Actor Standby\n} as const;\n\n/**\n * Keys of labels applied to act Docker images and containers.\n */\nexport const DOCKER_LABELS = {\n    ACTOR_BUILD_ID: 'com.apify.actBuildId',\n    ACTOR_RUN_ID: 'com.apify.actRunId',\n\n    // Kept for backwards compatibility, will be removed soon (TODO: remove old usages!)\n    /** @deprecated Use ACTOR_BUILD_ID instead! */\n    ACT_BUILD_ID: 'com.apify.actBuildId',\n    /** @deprecated Use ACTOR_RUN_ID instead! */\n    ACT_RUN_ID: 'com.apify.actRunId',\n} as const;\n\n/**\n * Acts types\n */\nexport const ACTOR_TYPES = {\n    ACT: 'acts',\n    CRAWLER: 'crawlers',\n} as const;\n\n/**\n * Used as username for returning user own info from API v2/users/username\n */\nexport const ME_USER_NAME_PLACEHOLDER = 'me';\n\n/**\n * Username used when user is anonymous.\n */\nexport const ANONYMOUS_USERNAME = 'anonymous';\n\n/**\n * Username constraints.\n */\nexport const USERNAME = {\n    MIN_LENGTH: 3,\n    MAX_LENGTH: 30,\n\n    // Regex matching a potentially allowed username. The numbers must match MIN and MAX!\n    // Note that username must also pass isForbiddenUser() test to be allowed!\n    REGEX: /^[a-zA-Z0-9_.-]{3,30}$/,\n};\n\nexport const EMAIL = {\n    MAX_LENGTH: 254, // see https://www.rfc-editor.org/errata_search.php?rfc=3696&eid=1690\n    REGEX: EMAIL_REGEX,\n};\n\n/**\n * Profile name (such as organization or first / last name) constraints.\n */\nexport const PROFILE_NAME = {\n    MAX_LENGTH: 50,\n    REGEX: /^(?!.*:\\/\\/)[^@><]*$/, // Prohibits usage of @, <, > and ://\n};\n\n/**\n * Max length for DNS safe string\n */\nexport const DNS_SAFE_NAME_MAX_LENGTH = 63;\n\n/**\n * Actor name constraints.\n */\nexport const ACTOR_NAME = {\n    MIN_LENGTH: 3,\n    MAX_LENGTH: DNS_SAFE_NAME_MAX_LENGTH, // DNS-safe string length\n    REGEX: DNS_SAFE_NAME_REGEX,\n};\n\n/**\n * Length of short crawler ID for nice public crawlers path.\n */\nexport const SHORT_CRAWLER_ID_LENGTH = 5;\n\n/**\n * Default build tag used for latest act version.\n */\nexport const BUILD_TAG_LATEST = 'latest';\n\n/**\n * Behaviour of act restart on error.\n * Act gets restarted when there are less than MAX_RESTARTS in the last INTERVAL_MILLIS.\n */\nexport const ACTOR_RESTART_ON_ERROR = {\n    MAX_RESTARTS: 3,\n    // This needs to be low enough so that it only covers restart loops, rather than e.g.\n    // errors during crawling of large lists of URLs\n    INTERVAL_MILLIS: 1 * 60 * 1000,\n};\n\n// The constants below are kept for backwards compatibility\n// TODO: Once all references to these are removed, remove these constants too\n\n/** @deprecated Use ACTOR_RESTART_ON_ERROR instead */\nexport const ACT_RESTART_ON_ERROR = ACTOR_RESTART_ON_ERROR;\n\n/** @deprecated Use ACTOR_JOB_TYPES instead */\nexport const ACT_JOB_TYPES = ACTOR_JOB_TYPES;\n\n/** @deprecated Use ACTOR_SOURCE_TYPES instead */\nexport const ACT_SOURCE_TYPES = ACTOR_SOURCE_TYPES;\n\n/** @deprecated Use ACTOR_JOB_STATUSES instead */\nexport const ACT_JOB_STATUSES = ACTOR_JOB_STATUSES;\n\n/** @deprecated Use ACTOR_JOB_TERMINAL_STATUSES instead */\nexport const ACT_JOB_TERMINAL_STATUSES = ACTOR_JOB_TERMINAL_STATUSES;\n\n/** @deprecated Use ACTOR_TYPES instead */\nexport const ACT_TYPES = ACTOR_TYPES;\n\n/**\n * 1 compute unit = 1GB * 1Hour.\n */\nexport const COMPUTE_UNIT_MB = 1024;\nexport const COMPUTE_UNIT_MILLIS = 60 * 60 * 1000;\n\n/**\n * Contains various Actor platform limits that are shared between the projects.\n * IMPORTANT: If you update any of them, update also https://github.com/apifytech/apify-docs/edit/master/docs/actor/limits.md !!!\n */\nexport const ACTOR_LIMITS = {\n    // The actualy used limit is taken from private package @apify-packages/consts\n    BUILD_DEFAULT_MEMORY_MBYTES: 4096,\n\n    // Maximum duration of build in seconds.\n    BUILD_TIMEOUT_SECS: 1800,\n\n    // For each build or run container, set disk quota based on memory size\n    RUN_DISK_TO_MEMORY_SIZE_COEFF: 2,\n\n    // For each build or run container, set CPU cores based on memory size\n    RUN_MEMORY_MBYTES_PER_CPU_CORE: 4096,\n\n    // The default limit of memory for all running Actor jobs for free accounts.\n    FREE_ACCOUNT_MAX_MEMORY_MBYTES: 8192,\n\n    // The default limit of memory for all running Actor jobs for paid accounts.\n    PAID_ACCOUNT_MAX_MEMORY_MBYTES: 65536,\n\n    // Minimum and maximum memory for a single act run.\n    MIN_RUN_MEMORY_MBYTES: 128,\n    MAX_RUN_MEMORY_MBYTES: 32768,\n\n    // Maximum size of actor input schema.\n    INPUT_SCHEMA_MAX_BYTES: 500 * 1024,\n\n    // Max length of run/build log in number of characters\n    LOG_MAX_CHARS: 10 * 1024 * 1024,\n};\n\n/**\n * Contains various limits of the Apify platform.\n */\nexport const DEFAULT_PLATFORM_LIMITS = {\n    // Maximum number of actors per user\n    MAX_ACTORS_PER_USER: 500,\n\n    // Maximum number of tasks per user\n    MAX_TASKS_PER_USER: 5000,\n\n    // Maximum number of schedules per user\n    MAX_SCHEDULES_PER_USER: 100,\n\n    // Maximum number of webhooks per user\n    MAX_WEBHOOKS_PER_USER: 100,\n\n    // Maximum number of concurrent actor runs per user for free accounts.\n    FREE_ACCOUNT_MAX_CONCURRENT_ACTOR_RUNS_PER_USER: 25,\n\n    // Maximum number of concurrent actor runs per user for paid accounts.\n    PAID_ACCOUNT_MAX_CONCURRENT_ACTOR_RUNS_PER_USER: 250,\n\n    // Maximum number of actors per scheduler\n    MAX_ACTORS_PER_SCHEDULER: 10,\n\n    // Maximum number of tasks per scheduler\n    MAX_TASKS_PER_SCHEDULER: 10,\n};\n\n/**\n * Max length of the queue head that server will return in Request Queue API.\n */\nexport const REQUEST_QUEUE_HEAD_MAX_LIMIT = 1000;\n\n/**\n * Dictionary of environment variable names prefixed with \"APIFY_\".\n */\nexport const APIFY_ENV_VARS = {\n    API_BASE_URL: 'APIFY_API_BASE_URL',\n    API_PUBLIC_BASE_URL: 'APIFY_API_PUBLIC_BASE_URL',\n    CHROME_EXECUTABLE_PATH: 'APIFY_CHROME_EXECUTABLE_PATH',\n    DEDICATED_CPUS: 'APIFY_DEDICATED_CPUS',\n    DISABLE_OUTDATED_WARNING: 'APIFY_DISABLE_OUTDATED_WARNING',\n    FACT: 'APIFY_FACT',\n    HEADLESS: 'APIFY_HEADLESS',\n    INPUT_SECRETS_PRIVATE_KEY_FILE: 'APIFY_INPUT_SECRETS_PRIVATE_KEY_FILE',\n    INPUT_SECRETS_PRIVATE_KEY_PASSPHRASE: 'APIFY_INPUT_SECRETS_PRIVATE_KEY_PASSPHRASE',\n    IS_AT_HOME: 'APIFY_IS_AT_HOME',\n    LOCAL_STORAGE_DIR: 'APIFY_LOCAL_STORAGE_DIR',\n    LOG_FORMAT: 'APIFY_LOG_FORMAT',\n    LOG_LEVEL: 'APIFY_LOG_LEVEL',\n    METAMORPH_AFTER_SLEEP_MILLIS: 'APIFY_METAMORPH_AFTER_SLEEP_MILLIS',\n    META_ORIGIN: 'APIFY_META_ORIGIN',\n    PERSIST_STATE_INTERVAL_MILLIS: 'APIFY_PERSIST_STATE_INTERVAL_MILLIS',\n    PROXY_HOSTNAME: 'APIFY_PROXY_HOSTNAME',\n    PROXY_PASSWORD: 'APIFY_PROXY_PASSWORD',\n    PROXY_PORT: 'APIFY_PROXY_PORT',\n    PROXY_STATUS_URL: 'APIFY_PROXY_STATUS_URL',\n    PURGE_ON_START: 'APIFY_PURGE_ON_START',\n    SDK_LATEST_VERSION: 'APIFY_SDK_LATEST_VERSION',\n    SYSTEM_INFO_INTERVAL_MILLIS: 'APIFY_SYSTEM_INFO_INTERVAL_MILLIS',\n    TOKEN: 'APIFY_TOKEN',\n    USER_ID: 'APIFY_USER_ID',\n    USER_IS_PAYING: 'APIFY_USER_IS_PAYING',\n    USER_PRICING_TIER: 'APIFY_USER_PRICING_TIER',\n    WORKFLOW_KEY: 'APIFY_WORKFLOW_KEY',\n    XVFB: 'APIFY_XVFB',\n\n    // Replaced by ACTOR_ENV_VARS, kept for backward compatibility:\n    ACTOR_BUILD_ID: 'APIFY_ACTOR_BUILD_ID',\n    ACTOR_BUILD_NUMBER: 'APIFY_ACTOR_BUILD_NUMBER',\n    ACTOR_EVENTS_WS_URL: 'APIFY_ACTOR_EVENTS_WS_URL',\n    ACTOR_ID: 'APIFY_ACTOR_ID',\n    ACTOR_MAX_PAID_DATASET_ITEMS: 'ACTOR_MAX_PAID_DATASET_ITEMS',\n    ACTOR_RUN_ID: 'APIFY_ACTOR_RUN_ID',\n    ACTOR_TASK_ID: 'APIFY_ACTOR_TASK_ID',\n    CONTAINER_PORT: 'APIFY_CONTAINER_PORT',\n    CONTAINER_URL: 'APIFY_CONTAINER_URL',\n    DEFAULT_DATASET_ID: 'APIFY_DEFAULT_DATASET_ID',\n    DEFAULT_KEY_VALUE_STORE_ID: 'APIFY_DEFAULT_KEY_VALUE_STORE_ID',\n    DEFAULT_REQUEST_QUEUE_ID: 'APIFY_DEFAULT_REQUEST_QUEUE_ID',\n    INPUT_KEY: 'APIFY_INPUT_KEY',\n    MEMORY_MBYTES: 'APIFY_MEMORY_MBYTES',\n    STARTED_AT: 'APIFY_STARTED_AT',\n    TIMEOUT_AT: 'APIFY_TIMEOUT_AT',\n\n    // Deprecated, keep them for backward compatibility:\n    ACT_ID: 'APIFY_ACT_ID',\n    ACT_RUN_ID: 'APIFY_ACT_RUN_ID',\n} as const;\n\n/**\n * @deprecated `ENV_VARS` were replaced by `APIFY_ENV_VARS`. We currently keep this for backwards compatibility.\n */\nexport const ENV_VARS = APIFY_ENV_VARS;\n\n/**\n * Dictionary of environment variable names prefixed with \"ACTOR_\".\n * Follows from Actor specs https://github.com/apify/actor-specs/#environment-variables\n */\nexport const ACTOR_ENV_VARS = {\n    BUILD_ID: 'ACTOR_BUILD_ID',\n    BUILD_NUMBER: 'ACTOR_BUILD_NUMBER',\n    BUILD_TAGS: 'ACTOR_BUILD_TAGS',\n    DEFAULT_DATASET_ID: 'ACTOR_DEFAULT_DATASET_ID',\n    DEFAULT_KEY_VALUE_STORE_ID: 'ACTOR_DEFAULT_KEY_VALUE_STORE_ID',\n    DEFAULT_REQUEST_QUEUE_ID: 'ACTOR_DEFAULT_REQUEST_QUEUE_ID',\n    EVENTS_WEBSOCKET_URL: 'ACTOR_EVENTS_WEBSOCKET_URL',\n    FULL_NAME: 'ACTOR_FULL_NAME',\n    ID: 'ACTOR_ID',\n    INPUT_KEY: 'ACTOR_INPUT_KEY',\n    MAX_PAID_DATASET_ITEMS: 'ACTOR_MAX_PAID_DATASET_ITEMS',\n    MAX_TOTAL_CHARGE_USD: 'ACTOR_MAX_TOTAL_CHARGE_USD',\n    MEMORY_MBYTES: 'ACTOR_MEMORY_MBYTES',\n    RUN_ID: 'ACTOR_RUN_ID',\n    STANDBY_PORT: 'ACTOR_STANDBY_PORT',\n    STANDBY_URL: 'ACTOR_STANDBY_URL',\n    STARTED_AT: 'ACTOR_STARTED_AT',\n    TASK_ID: 'ACTOR_TASK_ID',\n    TIMEOUT_AT: 'ACTOR_TIMEOUT_AT',\n    WEB_SERVER_PORT: 'ACTOR_WEB_SERVER_PORT',\n    WEB_SERVER_URL: 'ACTOR_WEB_SERVER_URL',\n} as const;\n\n// TODO: Discuss what to include here and whether to split into ACTOR and APIFY or not.\nexport const INTEGER_ENV_VARS = [\n    // Actor env vars\n    ACTOR_ENV_VARS.MAX_PAID_DATASET_ITEMS,\n    ACTOR_ENV_VARS.MEMORY_MBYTES,\n    ACTOR_ENV_VARS.STANDBY_PORT,\n    ACTOR_ENV_VARS.WEB_SERVER_PORT,\n    // Apify env vars\n    APIFY_ENV_VARS.ACTOR_MAX_PAID_DATASET_ITEMS,\n    APIFY_ENV_VARS.CONTAINER_PORT,\n    APIFY_ENV_VARS.DEDICATED_CPUS,\n    APIFY_ENV_VARS.MEMORY_MBYTES,\n    APIFY_ENV_VARS.METAMORPH_AFTER_SLEEP_MILLIS,\n    APIFY_ENV_VARS.PERSIST_STATE_INTERVAL_MILLIS,\n    APIFY_ENV_VARS.PROXY_PORT,\n    APIFY_ENV_VARS.SYSTEM_INFO_INTERVAL_MILLIS,\n] as const;\n\nexport const COMMA_SEPARATED_LIST_ENV_VARS = [\n    ACTOR_ENV_VARS.BUILD_TAGS,\n] as const;\n\n/**\n * Dictionary of names of build-time variables passed to the Actor's Docker build process.\n */\nexport const ACTOR_BUILD_ARGS = {\n    ACTOR_PATH_IN_DOCKER_CONTEXT: 'ACTOR_PATH_IN_DOCKER_CONTEXT',\n};\n\n/**\n * Default value for APIFY_CONTAINER_PORT used both locally and at Apify platform.\n */\nexport const DEFAULT_CONTAINER_PORT = 4321;\n\n/**\n * @deprecated Please use `DEFAULT_CONTAINER_PORT` instead, the value is the same.\n * Default value for ACTOR_STANDBY_PORT used both locally and at Apify platform.\n */\nexport const DEFAULT_ACTOR_STANDBY_PORT = DEFAULT_CONTAINER_PORT;\n\n/**\n * Local emulation sub directories for local stores\n */\nexport const LOCAL_STORAGE_SUBDIRS = {\n    datasets: 'datasets',\n    keyValueStores: 'key_value_stores',\n    requestQueues: 'request_queues',\n} as const;\n\n/**\n * Local defaults for of some of the Actor environment variables.\n * These are being preset in Apify SDK when it's running out of the Apify platform.\n */\nexport const LOCAL_ACTOR_ENV_VARS = {\n    [ACTOR_ENV_VARS.STANDBY_PORT]: DEFAULT_CONTAINER_PORT.toString(),\n    [ACTOR_ENV_VARS.DEFAULT_DATASET_ID]: 'default',\n    [ACTOR_ENV_VARS.DEFAULT_KEY_VALUE_STORE_ID]: 'default',\n    [ACTOR_ENV_VARS.DEFAULT_REQUEST_QUEUE_ID]: 'default',\n    [ACTOR_ENV_VARS.WEB_SERVER_PORT]: DEFAULT_CONTAINER_PORT.toString(),\n    [ACTOR_ENV_VARS.WEB_SERVER_URL]: `http://localhost:${DEFAULT_CONTAINER_PORT}`, // Must match port line above!\n};\n\n/**\n * Local defaults for of some of the Apify environment variables.\n * These are being preset in Apify SDK when it's running out of the Apify platform.\n */\nexport const LOCAL_APIFY_ENV_VARS = {\n    [APIFY_ENV_VARS.CONTAINER_PORT]: LOCAL_ACTOR_ENV_VARS.ACTOR_WEB_SERVER_PORT,\n    [APIFY_ENV_VARS.CONTAINER_URL]: LOCAL_ACTOR_ENV_VARS.ACTOR_WEB_SERVER_URL,\n    [APIFY_ENV_VARS.DEFAULT_DATASET_ID]: LOCAL_ACTOR_ENV_VARS.ACTOR_DEFAULT_DATASET_ID,\n    [APIFY_ENV_VARS.DEFAULT_KEY_VALUE_STORE_ID]: LOCAL_ACTOR_ENV_VARS.ACTOR_DEFAULT_KEY_VALUE_STORE_ID,\n    [APIFY_ENV_VARS.DEFAULT_REQUEST_QUEUE_ID]: LOCAL_ACTOR_ENV_VARS.ACTOR_DEFAULT_REQUEST_QUEUE_ID,\n    [APIFY_ENV_VARS.PROXY_HOSTNAME]: 'proxy.apify.com',\n    [APIFY_ENV_VARS.PROXY_PORT]: (8000).toString(),\n};\n\n/**\n * @deprecated `LOCAL_ENV_VARS` were replaced by `LOCAL_APIFY_ENV_VARS`. We currently keep this for backwards compatibility.\n */\nexport const LOCAL_ENV_VARS = LOCAL_APIFY_ENV_VARS;\n\n/**\n * Defaults input and output key-value stores keys\n */\nexport const KEY_VALUE_STORE_KEYS = {\n    INPUT: 'INPUT',\n    OUTPUT: 'OUTPUT',\n} as const;\n\n/**\n * Represents the maximum size in bytes of a request body (decompressed)\n * that will be accepted by the App and API servers.\n */\nexport const MAX_PAYLOAD_SIZE_BYTES = 9437184; // 9MB\n\n/**\n * Categories for crawlers and actors\n */\nexport const ACTOR_CATEGORIES = {\n    AI: 'AI',\n    AGENTS: 'Agents',\n    AUTOMATION: 'Automation',\n    BUSINESS: 'Business',\n    COVID_19: 'Covid-19',\n    DEVELOPER_EXAMPLES: 'Developer examples',\n    DEVELOPER_TOOLS: 'Developer tools',\n    ECOMMERCE: 'E-commerce',\n    FOR_CREATORS: 'For creators',\n    GAMES: 'Games',\n    JOBS: 'Jobs',\n    LEAD_GENERATION: 'Lead generation',\n    MARKETING: 'Marketing',\n    NEWS: 'News',\n    SEO_TOOLS: 'SEO tools',\n    SOCIAL_MEDIA: 'Social media',\n    TRAVEL: 'Travel',\n    VIDEOS: 'Videos',\n    REAL_ESTATE: 'Real estate',\n    SPORTS: 'Sports',\n    EDUCATION: 'Education',\n    INTEGRATIONS: 'Integrations',\n    OTHER: 'Other',\n    OPEN_SOURCE: 'Open source',\n} as const;\n\n// TODO: Remove this once it's no longer used, now that LEGACY_ACTOR_CATEGORIES is also gone\n/** @deprecated Use ACTOR_CATEGORIES instead! */\nexport const ALL_ACTOR_CATEGORIES = {\n    ...ACTOR_CATEGORIES,\n    // ...LEGACY_ACTOR_CATEGORIES,\n} as const;\n\n/**\n * Bases for converting version/build number to/from string/integer\n */\nexport const VERSION_INT_MAJOR_BASE = 1e7;\nexport const VERSION_INT_MINOR_BASE = 1e5;\n\n/**\n * Basic options for XSS sanitization\n */\nexport const USER_BASIC_TEXT_XSS_OPTIONS = {\n    whiteList: {\n        a: ['href', 'title', 'target'],\n        code: [],\n        strong: [],\n        b: [],\n        br: [],\n        ul: [],\n        li: [],\n        ol: [],\n        i: [],\n        u: [],\n        p: [],\n    },\n};\n\nexport const WEBHOOK_EVENT_TYPES = {\n    ACTOR_RUN_CREATED: 'ACTOR.RUN.CREATED',\n    ACTOR_RUN_SUCCEEDED: 'ACTOR.RUN.SUCCEEDED',\n    ACTOR_RUN_FAILED: 'ACTOR.RUN.FAILED',\n    ACTOR_RUN_TIMED_OUT: 'ACTOR.RUN.TIMED_OUT',\n    ACTOR_RUN_ABORTED: 'ACTOR.RUN.ABORTED',\n    ACTOR_RUN_RESURRECTED: 'ACTOR.RUN.RESURRECTED',\n\n    ACTOR_BUILD_CREATED: 'ACTOR.BUILD.CREATED',\n    ACTOR_BUILD_SUCCEEDED: 'ACTOR.BUILD.SUCCEEDED',\n    ACTOR_BUILD_FAILED: 'ACTOR.BUILD.FAILED',\n    ACTOR_BUILD_TIMED_OUT: 'ACTOR.BUILD.TIMED_OUT',\n    ACTOR_BUILD_ABORTED: 'ACTOR.BUILD.ABORTED',\n\n    TEST: 'TEST',\n} as const;\n\nexport const WEBHOOK_EVENT_TYPE_GROUPS = {\n    ACTOR_RUN: [\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_CREATED,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_SUCCEEDED,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_FAILED,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_TIMED_OUT,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_ABORTED,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_RESURRECTED,\n    ],\n    ACTOR_BUILD: [\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_CREATED,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_SUCCEEDED,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_FAILED,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_TIMED_OUT,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_ABORTED,\n    ],\n    // If one of these occurs then we can be sure that none other can occur for the same triggerer.\n    ACTOR_RUN_TERMINAL: [\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_SUCCEEDED,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_FAILED,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_TIMED_OUT,\n        WEBHOOK_EVENT_TYPES.ACTOR_RUN_ABORTED,\n    ],\n    ACTOR_BUILD_TERMINAL: [\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_SUCCEEDED,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_FAILED,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_TIMED_OUT,\n        WEBHOOK_EVENT_TYPES.ACTOR_BUILD_ABORTED,\n    ],\n} as const;\n\nexport const WEBHOOK_DEFAULT_PAYLOAD_TEMPLATE = `{\n    \"userId\": {{userId}},\n    \"createdAt\": {{createdAt}},\n    \"eventType\": {{eventType}},\n    \"eventData\": {{eventData}},\n    \"resource\": {{resource}}\n}`;\nexport const WEBHOOK_ALLOWED_PAYLOAD_VARIABLES = new Set([\n    'userId',\n    'createdAt',\n    'eventType',\n    'eventData',\n    'resource',\n]);\n\n// Max allowed size of files in multi-file editor\nexport const MAX_MULTIFILE_BYTES = 3 * (1024 ** 2); // 3MB\n\n// Formats for multi-file editor files\nexport const SOURCE_FILE_FORMATS = {\n    TEXT: 'TEXT',\n    BASE64: 'BASE64',\n} as const;\n\n// Marketplace project statuses\nexport const PROJECT_STATUSES = {\n    REQUEST: 'REQUEST',\n    SPECIFICATION: 'SPECIFICATION',\n    OFFERS: 'OFFERS',\n    DEPOSIT: 'DEPOSIT',\n    DEPOSIT_PAID: 'DEPOSIT_PAID',\n    NEW: 'NEW',\n    IN_PROGRESS: 'IN_PROGRESS',\n    QA: 'QA',\n    CUSTOMER_QA: 'CUSTOMER_QA',\n    READY_FOR_INVOICE: 'READY_FOR_INVOICE',\n    INVOICED: 'INVOICED',\n    PAID: 'PAID',\n    DELIVERED: 'DELIVERED',\n    CLOSED: 'CLOSED',\n    FINISHED: 'FINISHED',\n} as const;\n\n// Marketplace projects with status from this array is considered as successfully finished\nexport const FINISHED_PROJECT_STATUSES = [\n    PROJECT_STATUSES.READY_FOR_INVOICE,\n    PROJECT_STATUSES.INVOICED,\n    PROJECT_STATUSES.PAID,\n    PROJECT_STATUSES.DELIVERED,\n    PROJECT_STATUSES.FINISHED,\n] as const;\n\nexport const MARKETPLACE_USER_ROLES = {\n    DEVELOPER: 'DEVELOPER',\n    DATA_EXPERT: 'DATA_EXPERT',\n    CUSTOMER: 'CUSTOMER',\n} as const;\n\nexport const USER_PERSONA_TYPES = {\n    DEVELOPER: 'DEVELOPER',\n    USER: 'USER',\n} as const;\n\nexport const GIT_MAIN_BRANCH = 'main';\n\nexport const REQUEST_QUEUE_MAX_REQUESTS_PER_BATCH_OPERATION = 25;\n\nexport const ISSUES_STATUS_TYPES = {\n    OPEN: 'OPEN',\n    CLOSED: 'CLOSED',\n} as const;\n\n/**\n * This is used for filtering issues. All issue types to be considered.\n */\nexport const ISSUES_STATUS_ALL = 'ALL';\n\n/**\n * Storage setting determining how others can access the storage.\n *\n * This setting overrides the user setting of the storage owner.\n */\nexport const STORAGE_GENERAL_ACCESS = {\n    /** Respect the user setting of the storage owner (default behavior). */\n    FOLLOW_USER_SETTING: 'FOLLOW_USER_SETTING',\n\n    /** Only signed-in users with explicit access can read this storage. */\n    RESTRICTED: 'RESTRICTED',\n\n    /** Anyone with a link, or the unique storage ID, can read the storage. */\n    ANYONE_WITH_ID_CAN_READ: 'ANYONE_WITH_ID_CAN_READ',\n\n    /** Anyone with a link, the unique storage ID, or the storage name, can read the storage. */\n    ANYONE_WITH_NAME_CAN_READ: 'ANYONE_WITH_NAME_CAN_READ',\n} as const;\n\nexport type STORAGE_GENERAL_ACCESS = ValueOf<typeof STORAGE_GENERAL_ACCESS>\n\n/**\n * Run setting determining how others can access the run.\n *\n * This setting overrides the user setting of the run owner.\n */\nexport const RUN_GENERAL_ACCESS = {\n    /** Respect the user setting of the run owner (default behavior). */\n    FOLLOW_USER_SETTING: 'FOLLOW_USER_SETTING',\n\n    /** Only signed-in users with explicit access can read this run. */\n    RESTRICTED: 'RESTRICTED',\n\n    /** Anyone with a link, or the unique run ID, can read the run. */\n    ANYONE_WITH_ID_CAN_READ: 'ANYONE_WITH_ID_CAN_READ',\n} as const;\n\nexport type RUN_GENERAL_ACCESS = ValueOf<typeof RUN_GENERAL_ACCESS>\n"], "mappings": ";AAEA,IAAM,sBAAsB;AAE5B,IAAM,kBAAkB,GAAG,mBAAmB,SAAS,mBAAmB;AAE1E,IAAM,wBAAwB;AAE9B,IAAM,oBAAoB,GAAG,qBAAqB,SAAS,qBAAqB;AAOzE,IAAM,kBAAkB,GAAG,eAAe,IAAI,iBAAiB;AAM/D,IAAM,cAAc,IAAI,OAAO,IAAI,eAAe,GAAG;AAMrD,IAAM,mCAAmC,IAAI,eAAe,UAAU,eAAe;AAMrF,IAAM,+BAA+B,IAAI,OAAO,IAAI,gCAAgC,GAAG;AAMvF,IAAM,iBAAiB;AAKvB,IAAM,sBAAsB;AAM5B,IAAM,0BAA0B;AAShC,IAAM,kBAAkB;AAUxB,IAAM,4BAA4B;AAGzC,IAAM,mBAAmB;AAElB,IAAM,gBAAgB;AACtB,IAAM,eAAe,IAAI,OAAO,IAAI,gBAAgB,KAAK,GAAG;AAM5D,IAAM,yBAAyB;AAK/B,IAAM,YAAY;AAIlB,IAAM,iBAAiB,IAAI;AAAA,EAC9B;AAAA,EAyCK;AACT;AAIO,IAAM,wBAAwB,IAAI,OAAO,sCAAsC,gBAAgB,oBAAoB,GAAG;AAKtH,IAAM,mBAAmB;AAKzB,IAAM,qBAAqB;AAK3B,IAAM,qBAAqB;AAQ3B,IAAM,iBAAiB;;;AC7JvB,IAAM,8BAA8B;AAEpC,IAAM,kBAAkB;AAAA,EAC3B,OAAO;AAAA,EACP,KAAK;AACT;AAEO,IAAM,qBAAqB;AAAA,EAC9B,aAAa;AAAA,EACb,cAAc;AAAA,EACd,UAAU;AAAA,EACV,SAAS;AAAA,EACT,aAAa;AACjB;AAEO,IAAM,oBAAoB;AAAA,EAC7B,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AAAA,EACX,eAAe;AAAA,EACf,UAAU;AACd;AAKO,IAAM,qBAAqB;AAAA,EAC9B,OAAO;AAAA;AAAA,EACP,SAAS;AAAA;AAAA,EACT,WAAW;AAAA;AAAA,EACX,QAAQ;AAAA;AAAA,EACR,YAAY;AAAA;AAAA,EACZ,WAAW;AAAA;AAAA,EACX,UAAU;AAAA;AAAA,EACV,SAAS;AAAA;AACb;AAKO,IAAM,4BAA4B;AAAA,EACrC,QAAQ;AAAA;AAAA,EACR,WAAW;AAAA;AAAA,EACX,QAAQ;AAAA;AACZ;AAKO,IAAM,8BAA8B;AAAA,EACvC,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,mBAAmB;AACvB;AAIO,IAAM,uBAAuB;AAAA,EAChC,UAAU;AAAA,EACV,OAAO;AACX;AAEO,IAAM,eAAe;AAAA,EACxB,aAAa;AAAA;AAAA,EACb,KAAK;AAAA;AAAA,EACL,KAAK;AAAA;AAAA,EACL,WAAW;AAAA;AAAA,EACX,MAAM;AAAA;AAAA,EACN,SAAS;AAAA;AAAA,EACT,OAAO;AAAA;AAAA,EACP,KAAK;AAAA;AAAA,EACL,SAAS;AAAA;AACb;AAKO,IAAM,gBAAgB;AAAA,EACzB,gBAAgB;AAAA,EAChB,cAAc;AAAA;AAAA;AAAA,EAId,cAAc;AAAA;AAAA,EAEd,YAAY;AAChB;AAKO,IAAM,cAAc;AAAA,EACvB,KAAK;AAAA,EACL,SAAS;AACb;AAKO,IAAM,2BAA2B;AAKjC,IAAM,qBAAqB;AAK3B,IAAM,WAAW;AAAA,EACpB,YAAY;AAAA,EACZ,YAAY;AAAA;AAAA;AAAA,EAIZ,OAAO;AACX;AAEO,IAAM,QAAQ;AAAA,EACjB,YAAY;AAAA;AAAA,EACZ,OAAO;AACX;AAKO,IAAM,eAAe;AAAA,EACxB,YAAY;AAAA,EACZ,OAAO;AAAA;AACX;AAKO,IAAM,2BAA2B;AAKjC,IAAM,aAAa;AAAA,EACtB,YAAY;AAAA,EACZ,YAAY;AAAA;AAAA,EACZ,OAAO;AACX;AAKO,IAAM,0BAA0B;AAKhC,IAAM,mBAAmB;AAMzB,IAAM,yBAAyB;AAAA,EAClC,cAAc;AAAA;AAAA;AAAA,EAGd,iBAAiB,IAAI,KAAK;AAC9B;AAMO,IAAM,uBAAuB;AAG7B,IAAM,gBAAgB;AAGtB,IAAM,mBAAmB;AAGzB,IAAM,mBAAmB;AAGzB,IAAM,4BAA4B;AAGlC,IAAM,YAAY;AAKlB,IAAM,kBAAkB;AACxB,IAAM,sBAAsB,KAAK,KAAK;AAMtC,IAAM,eAAe;AAAA;AAAA,EAExB,6BAA6B;AAAA;AAAA,EAG7B,oBAAoB;AAAA;AAAA,EAGpB,+BAA+B;AAAA;AAAA,EAG/B,gCAAgC;AAAA;AAAA,EAGhC,gCAAgC;AAAA;AAAA,EAGhC,gCAAgC;AAAA;AAAA,EAGhC,uBAAuB;AAAA,EACvB,uBAAuB;AAAA;AAAA,EAGvB,wBAAwB,MAAM;AAAA;AAAA,EAG9B,eAAe,KAAK,OAAO;AAC/B;AAKO,IAAM,0BAA0B;AAAA;AAAA,EAEnC,qBAAqB;AAAA;AAAA,EAGrB,oBAAoB;AAAA;AAAA,EAGpB,wBAAwB;AAAA;AAAA,EAGxB,uBAAuB;AAAA;AAAA,EAGvB,iDAAiD;AAAA;AAAA,EAGjD,iDAAiD;AAAA;AAAA,EAGjD,0BAA0B;AAAA;AAAA,EAG1B,yBAAyB;AAC7B;AAKO,IAAM,+BAA+B;AAKrC,IAAM,iBAAiB;AAAA,EAC1B,cAAc;AAAA,EACd,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,gBAAgB;AAAA,EAChB,0BAA0B;AAAA,EAC1B,MAAM;AAAA,EACN,UAAU;AAAA,EACV,gCAAgC;AAAA,EAChC,sCAAsC;AAAA,EACtC,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,8BAA8B;AAAA,EAC9B,aAAa;AAAA,EACb,+BAA+B;AAAA,EAC/B,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,OAAO;AAAA,EACP,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,MAAM;AAAA;AAAA,EAGN,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,UAAU;AAAA,EACV,8BAA8B;AAAA,EAC9B,cAAc;AAAA,EACd,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,WAAW;AAAA,EACX,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,YAAY;AAAA;AAAA,EAGZ,QAAQ;AAAA,EACR,YAAY;AAChB;AAKO,IAAM,WAAW;AAMjB,IAAM,iBAAiB;AAAA,EAC1B,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,IAAI;AAAA,EACJ,WAAW;AAAA,EACX,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,gBAAgB;AACpB;AAGO,IAAM,mBAAmB;AAAA;AAAA,EAE5B,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA;AAAA,EAEf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AACnB;AAEO,IAAM,gCAAgC;AAAA,EACzC,eAAe;AACnB;AAKO,IAAM,mBAAmB;AAAA,EAC5B,8BAA8B;AAClC;AAKO,IAAM,yBAAyB;AAM/B,IAAM,6BAA6B;AAKnC,IAAM,wBAAwB;AAAA,EACjC,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,eAAe;AACnB;AAMO,IAAM,uBAAuB;AAAA,EAChC,CAAC,eAAe,YAAY,GAAG,uBAAuB,SAAS;AAAA,EAC/D,CAAC,eAAe,kBAAkB,GAAG;AAAA,EACrC,CAAC,eAAe,0BAA0B,GAAG;AAAA,EAC7C,CAAC,eAAe,wBAAwB,GAAG;AAAA,EAC3C,CAAC,eAAe,eAAe,GAAG,uBAAuB,SAAS;AAAA,EAClE,CAAC,eAAe,cAAc,GAAG,oBAAoB,sBAAsB;AAAA;AAC/E;AAMO,IAAM,uBAAuB;AAAA,EAChC,CAAC,eAAe,cAAc,GAAG,qBAAqB;AAAA,EACtD,CAAC,eAAe,aAAa,GAAG,qBAAqB;AAAA,EACrD,CAAC,eAAe,kBAAkB,GAAG,qBAAqB;AAAA,EAC1D,CAAC,eAAe,0BAA0B,GAAG,qBAAqB;AAAA,EAClE,CAAC,eAAe,wBAAwB,GAAG,qBAAqB;AAAA,EAChE,CAAC,eAAe,cAAc,GAAG;AAAA,EACjC,CAAC,eAAe,UAAU,GAAI,IAAM,SAAS;AACjD;AAKO,IAAM,iBAAiB;AAKvB,IAAM,uBAAuB;AAAA,EAChC,OAAO;AAAA,EACP,QAAQ;AACZ;AAMO,IAAM,yBAAyB;AAK/B,IAAM,mBAAmB;AAAA,EAC5B,IAAI;AAAA,EACJ,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,cAAc;AAAA,EACd,OAAO;AAAA,EACP,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,cAAc;AAAA,EACd,OAAO;AAAA,EACP,aAAa;AACjB;AAIO,IAAM,uBAAuB;AAAA,EAChC,GAAG;AAAA;AAEP;AAKO,IAAM,yBAAyB;AAC/B,IAAM,yBAAyB;AAK/B,IAAM,8BAA8B;AAAA,EACvC,WAAW;AAAA,IACP,GAAG,CAAC,QAAQ,SAAS,QAAQ;AAAA,IAC7B,MAAM,CAAC;AAAA,IACP,QAAQ,CAAC;AAAA,IACT,GAAG,CAAC;AAAA,IACJ,IAAI,CAAC;AAAA,IACL,IAAI,CAAC;AAAA,IACL,IAAI,CAAC;AAAA,IACL,IAAI,CAAC;AAAA,IACL,GAAG,CAAC;AAAA,IACJ,GAAG,CAAC;AAAA,IACJ,GAAG,CAAC;AAAA,EACR;AACJ;AAEO,IAAM,sBAAsB;AAAA,EAC/B,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EAEvB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,qBAAqB;AAAA,EAErB,MAAM;AACV;AAEO,IAAM,4BAA4B;AAAA,EACrC,WAAW;AAAA,IACP,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,EACxB;AAAA,EACA,aAAa;AAAA,IACT,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,EACxB;AAAA;AAAA,EAEA,oBAAoB;AAAA,IAChB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,EACxB;AAAA,EACA,sBAAsB;AAAA,IAClB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,EACxB;AACJ;AAEO,IAAM,mCAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOzC,IAAM,oCAAoC,oBAAI,IAAI;AAAA,EACrD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,CAAC;AAGM,IAAM,sBAAsB,IAAK,QAAQ;AAGzC,IAAM,sBAAsB;AAAA,EAC/B,MAAM;AAAA,EACN,QAAQ;AACZ;AAGO,IAAM,mBAAmB;AAAA,EAC5B,SAAS;AAAA,EACT,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,cAAc;AAAA,EACd,KAAK;AAAA,EACL,aAAa;AAAA,EACb,IAAI;AAAA,EACJ,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,UAAU;AAAA,EACV,MAAM;AAAA,EACN,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,UAAU;AACd;AAGO,IAAM,4BAA4B;AAAA,EACrC,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AACrB;AAEO,IAAM,yBAAyB;AAAA,EAClC,WAAW;AAAA,EACX,aAAa;AAAA,EACb,UAAU;AACd;AAEO,IAAM,qBAAqB;AAAA,EAC9B,WAAW;AAAA,EACX,MAAM;AACV;AAEO,IAAM,kBAAkB;AAExB,IAAM,iDAAiD;AAEvD,IAAM,sBAAsB;AAAA,EAC/B,MAAM;AAAA,EACN,QAAQ;AACZ;AAKO,IAAM,oBAAoB;AAO1B,IAAM,yBAAyB;AAAA;AAAA,EAElC,qBAAqB;AAAA;AAAA,EAGrB,YAAY;AAAA;AAAA,EAGZ,yBAAyB;AAAA;AAAA,EAGzB,2BAA2B;AAC/B;AASO,IAAM,qBAAqB;AAAA;AAAA,EAE9B,qBAAqB;AAAA;AAAA,EAGrB,YAAY;AAAA;AAAA,EAGZ,yBAAyB;AAC7B;", "names": []}