using System.Collections.Generic;

namespace BatchBrowserUI.Models
{
    /// <summary>
    /// 批量操作结果包装类 - 用于UI和控制器之间的标准化通信
    /// </summary>
    /// <typeparam name="T">返回数据的类型</typeparam>
    public class BatchOperationResult<T>
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误信息（如果操作失败）
        /// </summary>
        public string Error { get; set; } = string.Empty;        /// <summary>
        /// 返回的数据实例
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// 兼容性属性 - 指向Data属性
        /// </summary>
        public T? Instances 
        { 
            get => Data; 
            set => Data = value; 
        }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static BatchOperationResult<T> CreateSuccess(T data)
        {            return new BatchOperationResult<T>
            {
                Success = true,
                Data = data,
                Error = string.Empty
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static BatchOperationResult<T> CreateError(string error)
        {            return new BatchOperationResult<T>
            {
                Success = false,
                Error = error,
                Data = default(T)
            };
        }
    }
}
