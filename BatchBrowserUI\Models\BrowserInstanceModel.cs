using System;
using System.ComponentModel;

namespace BatchBrowserUI.Models
{
    /// <summary>
    /// 浏览器实例UI模型 - 用于DataGrid数据绑定
    /// 功能：展示浏览器实例的状态和信息
    /// </summary>
    public class BrowserInstanceModel : INotifyPropertyChanged
    {
        public string Id { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Proxy { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public DateTime Created { get; set; }
        public string Fingerprint { get; set; } = string.Empty;
        public long Memory { get; set; }
        public int RequestCount { get; set; }
        public bool IsActive { get; set; }
        public string ProxyServer { get; set; } = string.Empty;
        public DateTime CreatedTime { get; set; }
        public DateTime LastActiveTime { get; set; }
        public long MemoryUsage { get; set; }

        public string ProxyAddress => ProxyServer;
        public string CurrentUrl => Url;
        public DateTime CreatedAt => CreatedTime;
        public int UrlsProcessed => RequestCount;
        public string CreatedString => Created.ToString("yyyy-MM-dd HH:mm:ss");
        public string MemoryString => $"{Memory} MB";

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
