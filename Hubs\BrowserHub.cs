using Microsoft.AspNetCore.SignalR;

namespace BatchBrowserEngine.Hubs
{
    /// <summary>
    /// 浏览器实时通信Hub
    /// 功能说明：提供实时状态更新、事件通知、双向通信
    /// </summary>
    public class BrowserHub : Hub
    {
        private readonly ILogger<BrowserHub> _logger;

        public BrowserHub(ILogger<BrowserHub> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 客户端连接事件
        /// </summary>
        public override async Task OnConnectedAsync()
        {
            var connectionId = Context.ConnectionId;
            var userAgent = Context.GetHttpContext()?.Request.Headers["User-Agent"].ToString();
            
            _logger.LogInformation($"🔗 客户端连接: {connectionId}");
            
            // 发送欢迎消息
            await Clients.Caller.SendAsync("Connected", new 
            { 
                connectionId,
                message = "连接成功",
                timestamp = DateTime.Now
            });
            
            // 通知其他客户端有新连接
            await Clients.Others.SendAsync("ClientConnected", new 
            { 
                connectionId,
                timestamp = DateTime.Now
            });
            
            await base.OnConnectedAsync();
        }

        /// <summary>
        /// 客户端断开连接事件
        /// </summary>
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var connectionId = Context.ConnectionId;
            
            if (exception != null)
            {
                _logger.LogWarning($"🔌 客户端异常断开: {connectionId}, 错误: {exception.Message}");
            }
            else
            {
                _logger.LogInformation($"🔌 客户端正常断开: {connectionId}");
            }
            
            // 通知其他客户端有连接断开
            await Clients.Others.SendAsync("ClientDisconnected", new 
            { 
                connectionId,
                timestamp = DateTime.Now,
                reason = exception?.Message
            });
            
            await base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        /// 加入指定组
        /// </summary>
        /// <param name="groupName">组名</param>
        public async Task JoinGroup(string groupName)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
            
            _logger.LogInformation($"👥 客户端 {Context.ConnectionId} 加入组: {groupName}");
            
            await Clients.Caller.SendAsync("JoinedGroup", new 
            { 
                groupName,
                message = $"已加入组 {groupName}",
                timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 离开指定组
        /// </summary>
        /// <param name="groupName">组名</param>
        public async Task LeaveGroup(string groupName)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            
            _logger.LogInformation($"👥 客户端 {Context.ConnectionId} 离开组: {groupName}");
            
            await Clients.Caller.SendAsync("LeftGroup", new 
            { 
                groupName,
                message = $"已离开组 {groupName}",
                timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 订阅实例状态更新
        /// </summary>
        /// <param name="instanceIds">实例ID列表</param>
        public async Task SubscribeToInstances(List<string> instanceIds)
        {
            foreach (var instanceId in instanceIds)
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, $"instance_{instanceId}");
            }
            
            _logger.LogInformation($"📡 客户端 {Context.ConnectionId} 订阅 {instanceIds.Count} 个实例状态");
            
            await Clients.Caller.SendAsync("SubscribedToInstances", new 
            { 
                instanceIds,
                message = $"已订阅 {instanceIds.Count} 个实例状态",
                timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 取消订阅实例状态
        /// </summary>
        /// <param name="instanceIds">实例ID列表</param>
        public async Task UnsubscribeFromInstances(List<string> instanceIds)
        {
            foreach (var instanceId in instanceIds)
            {
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"instance_{instanceId}");
            }
            
            _logger.LogInformation($"📡 客户端 {Context.ConnectionId} 取消订阅 {instanceIds.Count} 个实例状态");
            
            await Clients.Caller.SendAsync("UnsubscribedFromInstances", new 
            { 
                instanceIds,
                message = $"已取消订阅 {instanceIds.Count} 个实例状态",
                timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 发送心跳
        /// </summary>
        public async Task Ping()
        {
            await Clients.Caller.SendAsync("Pong", new 
            { 
                timestamp = DateTime.Now,
                connectionId = Context.ConnectionId
            });
        }

        /// <summary>
        /// 获取连接信息
        /// </summary>
        public async Task GetConnectionInfo()
        {
            var httpContext = Context.GetHttpContext();
            
            var connectionInfo = new 
            {
                connectionId = Context.ConnectionId,
                userIdentifier = Context.UserIdentifier,
                remoteIpAddress = httpContext?.Connection.RemoteIpAddress?.ToString(),
                userAgent = httpContext?.Request.Headers["User-Agent"].ToString(),
                connectedAt = DateTime.Now,
                groups = new List<string>() // 实际应用中可以维护组信息
            };
            
            await Clients.Caller.SendAsync("ConnectionInfo", connectionInfo);
        }
    }

    /// <summary>
    /// SignalR Hub扩展方法
    /// </summary>
    public static class BrowserHubExtensions
    {
        /// <summary>
        /// 通知实例状态更新
        /// </summary>
        public static async Task NotifyInstanceStatusUpdate(this IHubContext<BrowserHub> hubContext, 
            string instanceId, string status, object? data = null)
        {
            await hubContext.Clients.Group($"instance_{instanceId}")
                .SendAsync("InstanceStatusUpdate", new 
                { 
                    instanceId,
                    status,
                    data,
                    timestamp = DateTime.Now
                });
        }

        /// <summary>
        /// 通知实例创建
        /// </summary>
        public static async Task NotifyInstanceCreated(this IHubContext<BrowserHub> hubContext, 
            object instanceInfo)
        {
            await hubContext.Clients.All.SendAsync("InstanceCreated", new 
            { 
                instance = instanceInfo,
                timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 通知实例删除
        /// </summary>
        public static async Task NotifyInstanceDeleted(this IHubContext<BrowserHub> hubContext, 
            string instanceId)
        {
            await hubContext.Clients.All.SendAsync("InstanceDeleted", new 
            { 
                instanceId,
                timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 通知系统状态更新
        /// </summary>
        public static async Task NotifySystemStatusUpdate(this IHubContext<BrowserHub> hubContext, 
            object systemStatus)
        {
            await hubContext.Clients.All.SendAsync("SystemStatusUpdate", new 
            { 
                status = systemStatus,
                timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 通知错误信息
        /// </summary>
        public static async Task NotifyError(this IHubContext<BrowserHub> hubContext, 
            string message, string? errorCode = null, object? details = null)
        {
            await hubContext.Clients.All.SendAsync("Error", new 
            { 
                message,
                errorCode,
                details,
                timestamp = DateTime.Now
            });
        }

        /// <summary>
        /// 广播消息
        /// </summary>
        public static async Task BroadcastMessage(this IHubContext<BrowserHub> hubContext, 
            string type, string message, object? data = null)
        {
            await hubContext.Clients.All.SendAsync("Broadcast", new 
            { 
                type,
                message,
                data,
                timestamp = DateTime.Now
            });
        }
    }
}
