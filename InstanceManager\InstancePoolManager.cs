using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Concurrent;

namespace InstanceManager
{
    /// <summary>
    /// 实例池管理器 - 独立DLL模块
    /// 功能说明：管理浏览器实例的生命周期、资源分配、状态监控
    /// </summary>
    public class InstancePoolManager : IDisposable
    {
        private readonly ILogger<InstancePoolManager>? _logger;
        private readonly ConcurrentDictionary<string, InstanceInfo> _instances;
        private readonly ConcurrentDictionary<string, InstanceMetrics> _metrics;
        private readonly Timer _cleanupTimer;
        private readonly Timer _metricsTimer;
        
        // 配置参数
        private readonly int _maxInstances;
        private readonly TimeSpan _instanceTimeout;
        private readonly TimeSpan _cleanupInterval;

        public InstancePoolManager(
            int maxInstances = 50,
            TimeSpan? instanceTimeout = null,
            TimeSpan? cleanupInterval = null,
            ILogger<InstancePoolManager>? logger = null)
        {
            _logger = logger;
            _instances = new ConcurrentDictionary<string, InstanceInfo>();
            _metrics = new ConcurrentDictionary<string, InstanceMetrics>();
            _maxInstances = maxInstances;
            _instanceTimeout = instanceTimeout ?? TimeSpan.FromHours(2);
            _cleanupInterval = cleanupInterval ?? TimeSpan.FromMinutes(10);
            
            // 定期清理过期实例
            _cleanupTimer = new Timer(PerformCleanup, null, _cleanupInterval, _cleanupInterval);
            
            // 定期更新指标
            _metricsTimer = new Timer(UpdateMetrics, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        }

        /// <summary>
        /// 初始化实例池
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger?.LogInformation("🏊 正在初始化实例池管理器...");
                
                // 可以在这里加载持久化的实例数据
                await LoadPersistedInstancesAsync();
                
                _logger?.LogInformation($"✅ 实例池管理器初始化完成，最大实例数: {_maxInstances}");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 实例池管理器初始化失败");
                return false;
            }
        }

        /// <summary>
        /// 注册新实例
        /// </summary>
        /// <param name="instanceInfo">实例信息</param>
        /// <returns>注册结果</returns>
        public RegistrationResult RegisterInstance(InstanceInfo instanceInfo)
        {
            try
            {
                // 检查实例数量限制
                if (_instances.Count >= _maxInstances)
                {
                    return new RegistrationResult
                    {
                        Success = false,
                        Message = $"已达到最大实例数限制: {_maxInstances}"
                    };
                }

                // 检查实例ID是否已存在
                if (_instances.ContainsKey(instanceInfo.Id))
                {
                    return new RegistrationResult
                    {
                        Success = false,
                        Message = $"实例ID已存在: {instanceInfo.Id}"
                    };
                }

                // 设置实例状态
                instanceInfo.Status = InstanceStatus.Active;
                instanceInfo.CreatedAt = DateTime.Now;
                instanceInfo.LastActivity = DateTime.Now;

                // 注册实例
                _instances.TryAdd(instanceInfo.Id, instanceInfo);
                
                // 初始化指标
                _metrics.TryAdd(instanceInfo.Id, new InstanceMetrics
                {
                    InstanceId = instanceInfo.Id,
                    StartTime = DateTime.Now,
                    LastUpdate = DateTime.Now
                });

                _logger?.LogInformation($"✅ 实例注册成功: {instanceInfo.Id}");
                
                return new RegistrationResult
                {
                    Success = true,
                    Message = "实例注册成功",
                    InstanceId = instanceInfo.Id
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 实例注册失败: {instanceInfo.Id}");
                return new RegistrationResult
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 注销实例
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <returns>注销结果</returns>
        public bool UnregisterInstance(string instanceId)
        {
            try
            {
                var removed = _instances.TryRemove(instanceId, out var instance);
                if (removed)
                {
                    _metrics.TryRemove(instanceId, out _);
                    _logger?.LogInformation($"🗑️ 实例注销成功: {instanceId}");
                }
                else
                {
                    _logger?.LogWarning($"⚠️ 实例不存在: {instanceId}");
                }
                
                return removed;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 实例注销失败: {instanceId}");
                return false;
            }
        }

        /// <summary>
        /// 更新实例状态
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <param name="status">新状态</param>
        /// <param name="message">状态消息</param>
        /// <returns>更新结果</returns>
        public bool UpdateInstanceStatus(string instanceId, InstanceStatus status, string? message = null)
        {
            try
            {
                if (_instances.TryGetValue(instanceId, out var instance))
                {
                    instance.Status = status;
                    instance.LastActivity = DateTime.Now;
                    instance.StatusMessage = message;
                    
                    // 更新指标
                    if (_metrics.TryGetValue(instanceId, out var metrics))
                    {
                        metrics.LastUpdate = DateTime.Now;
                        metrics.StatusChanges++;
                    }
                    
                    _logger?.LogDebug($"📊 实例状态更新: {instanceId} -> {status}");
                    return true;
                }
                
                _logger?.LogWarning($"⚠️ 更新状态失败，实例不存在: {instanceId}");
                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 更新实例状态失败: {instanceId}");
                return false;
            }
        }

        /// <summary>
        /// 记录实例活动
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <param name="activity">活动类型</param>
        /// <param name="details">活动详情</param>
        public void RecordActivity(string instanceId, string activity, object? details = null)
        {
            try
            {
                if (_instances.TryGetValue(instanceId, out var instance))
                {
                    instance.LastActivity = DateTime.Now;
                    
                    // 更新指标
                    if (_metrics.TryGetValue(instanceId, out var metrics))
                    {
                        metrics.LastUpdate = DateTime.Now;
                        metrics.ActivityCount++;
                        
                        // 记录活动历史
                        if (metrics.RecentActivities.Count >= 10)
                        {
                            metrics.RecentActivities.Dequeue();
                        }
                        
                        metrics.RecentActivities.Enqueue(new ActivityRecord
                        {
                            Activity = activity,
                            Timestamp = DateTime.Now,
                            Details = details
                        });
                    }
                    
                    _logger?.LogDebug($"📝 记录实例活动: {instanceId} - {activity}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 记录实例活动失败: {instanceId}");
            }
        }

        /// <summary>
        /// 获取实例信息
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <returns>实例信息</returns>
        public InstanceInfo? GetInstance(string instanceId)
        {
            return _instances.TryGetValue(instanceId, out var instance) ? instance : null;
        }

        /// <summary>
        /// 获取所有实例
        /// </summary>
        /// <returns>实例列表</returns>
        public List<InstanceInfo> GetAllInstances()
        {
            return _instances.Values.ToList();
        }

        /// <summary>
        /// 获取活跃实例
        /// </summary>
        /// <returns>活跃实例列表</returns>
        public List<InstanceInfo> GetActiveInstances()
        {
            return _instances.Values
                .Where(i => i.Status == InstanceStatus.Active)
                .ToList();
        }

        /// <summary>
        /// 获取实例指标
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <returns>实例指标</returns>
        public InstanceMetrics? GetInstanceMetrics(string instanceId)
        {
            return _metrics.TryGetValue(instanceId, out var metrics) ? metrics : null;
        }

        /// <summary>
        /// 获取池统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public PoolStats GetPoolStats()
        {
            var instances = _instances.Values.ToList();
            var now = DateTime.Now;
            
            return new PoolStats
            {
                TotalInstances = instances.Count,
                ActiveInstances = instances.Count(i => i.Status == InstanceStatus.Active),
                IdleInstances = instances.Count(i => i.Status == InstanceStatus.Idle),
                ErrorInstances = instances.Count(i => i.Status == InstanceStatus.Error),
                MaxInstances = _maxInstances,
                UtilizationRate = _maxInstances > 0 ? (double)instances.Count / _maxInstances : 0,
                AverageAge = instances.Any() ? 
                    TimeSpan.FromTicks((long)instances.Average(i => (now - i.CreatedAt).Ticks)) : 
                    TimeSpan.Zero,
                LastCleanup = DateTime.Now // 应该记录实际的清理时间
            };
        }

        /// <summary>
        /// 执行清理操作
        /// </summary>
        /// <returns>清理结果</returns>
        public Task<CleanupResult> PerformCleanupAsync()
        {
            var result = new CleanupResult();
            var now = DateTime.Now;
            
            try
            {
                _logger?.LogInformation("🧹 开始执行实例池清理...");
                
                var instancesToRemove = new List<string>();
                
                foreach (var kvp in _instances)
                {
                    var instance = kvp.Value;
                    var age = now - instance.LastActivity;
                    
                    // 检查超时实例
                    if (age > _instanceTimeout)
                    {
                        instancesToRemove.Add(instance.Id);
                        result.TimeoutInstances++;
                        _logger?.LogInformation($"⏰ 发现超时实例: {instance.Id}, 闲置时间: {age}");
                    }
                    // 检查错误实例
                    else if (instance.Status == InstanceStatus.Error)
                    {
                        var errorAge = now - instance.CreatedAt;
                        if (errorAge > TimeSpan.FromMinutes(30)) // 错误实例30分钟后清理
                        {
                            instancesToRemove.Add(instance.Id);
                            result.ErrorInstances++;
                            _logger?.LogInformation($"❌ 发现长期错误实例: {instance.Id}");
                        }
                    }
                }
                
                // 移除实例
                foreach (var instanceId in instancesToRemove)
                {
                    if (UnregisterInstance(instanceId))
                    {
                        result.RemovedInstances++;
                    }
                }
                
                result.Success = true;
                result.Message = $"清理完成，移除 {result.RemovedInstances} 个实例";
                
                _logger?.LogInformation($"✅ 实例池清理完成: {result.Message}");
                
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 实例池清理失败");
                result.Success = false;
                result.Message = ex.Message;
                return Task.FromResult(result);
            }
        }

        /// <summary>
        /// 定期清理
        /// </summary>
        private async void PerformCleanup(object? state)
        {
            try
            {
                await PerformCleanupAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 定期清理过程中发生错误");
            }
        }

        /// <summary>
        /// 更新指标
        /// </summary>
        private void UpdateMetrics(object? state)
        {
            try
            {
                var now = DateTime.Now;
                
                foreach (var kvp in _metrics)
                {
                    var metrics = kvp.Value;
                    if (_instances.TryGetValue(kvp.Key, out var instance))
                    {
                        metrics.Uptime = now - metrics.StartTime;
                        metrics.LastUpdate = now;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 更新指标失败");
            }
        }

        /// <summary>
        /// 加载持久化实例数据
        /// </summary>
        private async Task LoadPersistedInstancesAsync()
        {
            // 这里可以实现从文件或数据库加载实例数据
            await Task.CompletedTask;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _cleanupTimer?.Dispose();
                _metricsTimer?.Dispose();
                
                _instances.Clear();
                _metrics.Clear();
                
                _logger?.LogInformation("🔄 实例池管理器已释放资源");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 释放资源时发生错误");
            }
        }
    }

    #region 数据模型

    /// <summary>
    /// 实例状态枚举
    /// </summary>
    public enum InstanceStatus
    {
        Active,     // 活跃
        Idle,       // 空闲
        Busy,       // 忙碌
        Error,      // 错误
        Stopped     // 已停止
    }

    /// <summary>
    /// 实例信息
    /// </summary>
    public class InstanceInfo
    {
        public string Id { get; set; } = string.Empty;
        public InstanceStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastActivity { get; set; }
        public string? StatusMessage { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new();
        public string? ProcessId { get; set; }
        public string? DataDirectory { get; set; }
    }

    /// <summary>
    /// 实例指标
    /// </summary>
    public class InstanceMetrics
    {
        public string InstanceId { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime LastUpdate { get; set; }
        public TimeSpan Uptime { get; set; }
        public int ActivityCount { get; set; }
        public int StatusChanges { get; set; }
        public Queue<ActivityRecord> RecentActivities { get; set; } = new();
    }

    /// <summary>
    /// 活动记录
    /// </summary>
    public class ActivityRecord
    {
        public string Activity { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public object? Details { get; set; }
    }

    /// <summary>
    /// 注册结果
    /// </summary>
    public class RegistrationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? InstanceId { get; set; }
    }

    /// <summary>
    /// 池统计信息
    /// </summary>
    public class PoolStats
    {
        public int TotalInstances { get; set; }
        public int ActiveInstances { get; set; }
        public int IdleInstances { get; set; }
        public int ErrorInstances { get; set; }
        public int MaxInstances { get; set; }
        public double UtilizationRate { get; set; }
        public TimeSpan AverageAge { get; set; }
        public DateTime LastCleanup { get; set; }
    }

    /// <summary>
    /// 清理结果
    /// </summary>
    public class CleanupResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int RemovedInstances { get; set; }
        public int TimeoutInstances { get; set; }
        public int ErrorInstances { get; set; }
    }

    #endregion
}
