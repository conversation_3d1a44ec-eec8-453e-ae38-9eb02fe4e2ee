var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// src/index.ts
import { AsyncLocalStorage } from "node:async_hooks";
var storage = new AsyncLocalStorage();
var _TimeoutError = class _TimeoutError extends Error {
};
__name(_TimeoutError, "TimeoutError");
var TimeoutError = _TimeoutError;
var _InternalTimeoutError = class _InternalTimeoutError extends TimeoutError {
};
__name(_InternalTimeoutError, "InternalTimeoutError");
var InternalTimeoutError = _InternalTimeoutError;
function tryCancel() {
  const signal = storage.getStore()?.cancelTask.signal;
  if (signal?.aborted) {
    throw new InternalTimeoutError("Promise handler has been canceled due to a timeout");
  }
}
__name(tryCancel, "tryCancel");
async function addTimeoutToPromise(handler, timeoutMillis, errorMessage) {
  const context = storage.getStore() ?? {
    cancelTask: new AbortController()
  };
  let returnValue;
  const wrap = /* @__PURE__ */ __name(async () => {
    try {
      returnValue = await handler();
    } catch (e) {
      if (!(e instanceof InternalTimeoutError)) {
        throw e;
      }
    }
  }, "wrap");
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      context.cancelTask.abort();
      const error = errorMessage instanceof Error ? errorMessage : new TimeoutError(errorMessage);
      reject(error);
    }, timeoutMillis);
    storage.run(context, () => {
      wrap().then(() => resolve(returnValue)).catch(reject).finally(() => clearTimeout(timeout));
    });
  });
}
__name(addTimeoutToPromise, "addTimeoutToPromise");
export {
  TimeoutError,
  addTimeoutToPromise,
  storage,
  tryCancel
};
//# sourceMappingURL=index.mjs.map