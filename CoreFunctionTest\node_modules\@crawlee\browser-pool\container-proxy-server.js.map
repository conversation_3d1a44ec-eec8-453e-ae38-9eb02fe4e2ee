{"version": 3, "file": "container-proxy-server.js", "sourceRoot": "", "sources": ["../src/container-proxy-server.ts"], "names": [], "mappings": ";;AAQA,wEA0CC;AAlDD,6CAAyD;AAEzD;;;;;GAKG;AACI,KAAK,UAAU,8BAA8B,CAAC,gBAAyB;IAC1E,MAAM,SAAS,GAAG,IAAI,GAAG,EAAkB,CAAC;IAE5C,MAAM,WAAW,GAAG,IAAI,oBAAgB,CAAC;QACrC,sBAAsB,CAAC,EAAE,OAAO,EAAE;YAC9B,MAAM,UAAU,GAAG,SAAS,CAAC;YAC7B,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,YAAa,CAAC,UAAU,CAAC,UAAU,CAAC;gBACpE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,YAAa,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;gBACvD,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,YAAa,CAAC;YAEnC,MAAM,gBAAgB,GAAG,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAErD,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;gBACjC,IAAI,gBAAgB,EAAE,CAAC;oBACnB,OAAO;wBACH,gBAAgB,EAAE,gBAAgB;wBAClC,qBAAqB,EAAE,KAAK;qBAC/B,CAAC;gBACN,CAAC;gBAED,OAAO,CAAC,IAAI,CAAC,yBAAyB,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YAClF,CAAC;YAED,OAAO;gBACH,gBAAgB;gBAChB,qBAAqB,EAAE,KAAK;aAC/B,CAAC;QACN,CAAC;QACD,IAAI,EAAE,CAAC;KACV,CAAC,CAAC;IAEH,MAAM,WAAW,CAAC,MAAM,EAAE,CAAC;IAE3B,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IAE3B,OAAO;QACH,IAAI,EAAE,WAAW,CAAC,IAAI;QACtB,SAAS;QACT,KAAK,CAAC,KAAK,CAAC,gBAAyB;YACjC,OAAO,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAC/C,CAAC;KACJ,CAAC;AACN,CAAC"}