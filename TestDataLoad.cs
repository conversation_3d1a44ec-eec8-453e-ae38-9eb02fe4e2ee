using System;
using System.IO;
using BatchBrowserUI.Models;

class TestDataLoad
{
    static void Main()
    {
        Console.WriteLine("🧪 测试数据加载功能");
        Console.WriteLine("===================");
        
        try
        {
            // 创建数据持久化管理器
            var dataPersistenceManager = new DataPersistenceManager();
            Console.WriteLine("✅ 数据持久化管理器创建成功");
            
            // 测试加载现有数据
            Console.WriteLine("\n📂 测试加载现有数据...");
            var loadedInstances = dataPersistenceManager.LoadBrowserInstances();
            Console.WriteLine($"✅ 成功加载 {loadedInstances.Count} 个浏览器实例");
            
            if (loadedInstances.Count > 0)
            {
                Console.WriteLine("\n📋 实例详情:");
                foreach (var instance in loadedInstances)
                {
                    Console.WriteLine($"   - ID: {instance.Id}");
                    Console.WriteLine($"   - 状态: {instance.Status}");
                    Console.WriteLine($"   - 活跃: {instance.IsActive}");
                    Console.WriteLine($"   - 代理: {instance.ProxyServer ?? "无代理"}");
                    Console.WriteLine($"   - URL: {instance.Url}");
                    Console.WriteLine($"   - 创建时间: {instance.CreatedTime:yyyy-MM-dd HH:mm:ss}");
                    Console.WriteLine($"   - 指纹: {instance.Fingerprint}");
                    Console.WriteLine();
                }
                
                // 统计信息
                var activeCount = loadedInstances.Count(i => i.IsActive);
                var totalCount = loadedInstances.Count;
                Console.WriteLine($"📊 统计信息: {activeCount} / {totalCount} 个实例活跃");
            }
            else
            {
                Console.WriteLine("❌ 没有找到任何实例数据");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}
