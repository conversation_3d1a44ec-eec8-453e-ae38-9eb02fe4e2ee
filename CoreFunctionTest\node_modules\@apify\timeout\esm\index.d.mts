import { AsyncLocalStorage } from 'node:async_hooks';

interface AbortContext {
    cancelTask: AbortController;
}
/**
 * `AsyncLocalStorage` instance that is used for baring the AbortContext inside user provided handler.
 * We can use it to access the `AbortContext` instance via `storage.getStore()`, and there we can access
 * `cancelTask` instance of `AbortController`.
 */
declare const storage: AsyncLocalStorage<AbortContext>;
/**
 * Custom error class that will be used for timeout error.
 */
declare class TimeoutError extends Error {
}
/**
 * Checks whether we are inside timeout handler created by this package, and cancels current
 * task execution by throwing `TimeoutError`. This error will be ignored if the promise timed
 * out already, or explicitly skipped in `addTimeoutToPromise`.
 *
 * Use this function after every async call that runs inside the timeout handler:
 *
 * ```ts
 * async function doSomething() {
 *     await doSomethingTimeConsuming();
 *     tryCancel();
 *     await doSomethingElse();
 *     tryCancel();
 * }
 * ```
 */
declare function tryCancel(): void;
/**
 * Runs given handler and rejects with the given `errorMessage` (or `Error` instance)
 * after given `timeoutMillis`, unless the original promise resolves or rejects earlier.
 * Use `tryCancel()` function inside the handler after each await to finish its execution
 * early when the timeout appears.
 *
 * ```ts
 * const res = await addTimeoutToPromise(
 *   () => handler(),
 *   200,
 *   'Handler timed out after 200ms!',
 * );
 * ```
 */
declare function addTimeoutToPromise<T>(handler: () => Promise<T>, timeoutMillis: number, errorMessage: string | Error): Promise<T>;

export { type AbortContext, TimeoutError, addTimeoutToPromise, storage, tryCancel };
