using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Diagnostics;

namespace PerformanceMonitor
{
    /// <summary>
    /// 系统性能监控器 - 独立DLL模块
    /// 功能说明：监控系统资源使用情况、性能指标、告警通知
    /// </summary>
    public class SystemMonitor : IDisposable
    {
        private readonly ILogger<SystemMonitor>? _logger;
        private readonly Timer _monitoringTimer;
        private readonly List<PerformanceMetric> _metricsHistory;
        private readonly object _lockObject = new object();
        
        // 性能计数器
        private PerformanceCounter? _cpuCounter;
        private PerformanceCounter? _memoryCounter;
        private Process _currentProcess;
        
        // 配置参数
        private readonly TimeSpan _monitoringInterval;
        private readonly int _historySize;
        private readonly Dictionary<string, double> _thresholds;

        public SystemMonitor(
            TimeSpan? monitoringInterval = null,
            int historySize = 100,
            ILogger<SystemMonitor>? logger = null)
        {
            _logger = logger;
            _monitoringInterval = monitoringInterval ?? TimeSpan.FromSeconds(5);
            _historySize = historySize;
            _metricsHistory = new List<PerformanceMetric>();
            _currentProcess = Process.GetCurrentProcess();
            
            // 默认阈值
            _thresholds = new Dictionary<string, double>
            {
                ["CpuUsage"] = 80.0,        // CPU使用率 80%
                ["MemoryUsage"] = 85.0,     // 内存使用率 85%
                ["ProcessMemory"] = 2048,   // 进程内存 2GB
                ["ThreadCount"] = 100       // 线程数量 100
            };
            
            // 初始化性能计数器
            InitializePerformanceCounters();
            
            // 启动监控定时器
            _monitoringTimer = new Timer(CollectMetrics, null, TimeSpan.Zero, _monitoringInterval);
        }

        /// <summary>
        /// 初始化性能计数器
        /// </summary>
        private void InitializePerformanceCounters()
        {
            try
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
                
                // 预热CPU计数器
                _cpuCounter.NextValue();
                
                _logger?.LogInformation("✅ 性能计数器初始化成功");
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "⚠️ 性能计数器初始化失败，将使用替代方案");
            }
        }

        /// <summary>
        /// 获取当前性能指标
        /// </summary>
        /// <returns>性能指标</returns>
        public PerformanceMetric GetCurrentMetrics()
        {
            try
            {
                var metric = new PerformanceMetric
                {
                    Timestamp = DateTime.Now,
                    CpuUsage = GetCpuUsage(),
                    MemoryUsage = GetMemoryUsage(),
                    ProcessMemoryMB = GetProcessMemoryMB(),
                    ThreadCount = GetThreadCount(),
                    HandleCount = GetHandleCount(),
                    GCMemoryMB = GetGCMemoryMB(),
                    WorkingSetMB = GetWorkingSetMB()
                };

                return metric;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 获取性能指标失败");
                return new PerformanceMetric { Timestamp = DateTime.Now };
            }
        }

        /// <summary>
        /// 获取性能历史记录
        /// </summary>
        /// <param name="count">获取数量</param>
        /// <returns>历史记录</returns>
        public List<PerformanceMetric> GetMetricsHistory(int count = 50)
        {
            lock (_lockObject)
            {
                return _metricsHistory
                    .TakeLast(Math.Min(count, _metricsHistory.Count))
                    .ToList();
            }
        }

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        /// <param name="duration">统计时间段</param>
        /// <returns>统计信息</returns>
        public PerformanceStats GetPerformanceStats(TimeSpan? duration = null)
        {
            var timeRange = duration ?? TimeSpan.FromMinutes(10);
            var cutoffTime = DateTime.Now - timeRange;
            
            lock (_lockObject)
            {
                var recentMetrics = _metricsHistory
                    .Where(m => m.Timestamp >= cutoffTime)
                    .ToList();

                if (!recentMetrics.Any())
                {
                    return new PerformanceStats();
                }

                return new PerformanceStats
                {
                    TimeRange = timeRange,
                    SampleCount = recentMetrics.Count,
                    CpuUsage = new MetricStats
                    {
                        Average = recentMetrics.Average(m => m.CpuUsage),
                        Maximum = recentMetrics.Max(m => m.CpuUsage),
                        Minimum = recentMetrics.Min(m => m.CpuUsage)
                    },
                    MemoryUsage = new MetricStats
                    {
                        Average = recentMetrics.Average(m => m.MemoryUsage),
                        Maximum = recentMetrics.Max(m => m.MemoryUsage),
                        Minimum = recentMetrics.Min(m => m.MemoryUsage)
                    },
                    ProcessMemory = new MetricStats
                    {
                        Average = recentMetrics.Average(m => m.ProcessMemoryMB),
                        Maximum = recentMetrics.Max(m => m.ProcessMemoryMB),
                        Minimum = recentMetrics.Min(m => m.ProcessMemoryMB)
                    },
                    ThreadCount = new MetricStats
                    {
                        Average = recentMetrics.Average(m => m.ThreadCount),
                        Maximum = recentMetrics.Max(m => m.ThreadCount),
                        Minimum = recentMetrics.Min(m => m.ThreadCount)
                    }
                };
            }
        }

        /// <summary>
        /// 检查性能告警
        /// </summary>
        /// <returns>告警列表</returns>
        public List<PerformanceAlert> CheckAlerts()
        {
            var alerts = new List<PerformanceAlert>();
            var currentMetrics = GetCurrentMetrics();

            // CPU使用率告警
            if (currentMetrics.CpuUsage > _thresholds["CpuUsage"])
            {
                alerts.Add(new PerformanceAlert
                {
                    Type = AlertType.CpuHigh,
                    Message = $"CPU使用率过高: {currentMetrics.CpuUsage:F1}%",
                    Value = currentMetrics.CpuUsage,
                    Threshold = _thresholds["CpuUsage"],
                    Timestamp = DateTime.Now,
                    Severity = currentMetrics.CpuUsage > 95 ? AlertSeverity.Critical : AlertSeverity.Warning
                });
            }

            // 内存使用率告警
            if (currentMetrics.MemoryUsage > _thresholds["MemoryUsage"])
            {
                alerts.Add(new PerformanceAlert
                {
                    Type = AlertType.MemoryHigh,
                    Message = $"内存使用率过高: {currentMetrics.MemoryUsage:F1}%",
                    Value = currentMetrics.MemoryUsage,
                    Threshold = _thresholds["MemoryUsage"],
                    Timestamp = DateTime.Now,
                    Severity = currentMetrics.MemoryUsage > 95 ? AlertSeverity.Critical : AlertSeverity.Warning
                });
            }

            // 进程内存告警
            if (currentMetrics.ProcessMemoryMB > _thresholds["ProcessMemory"])
            {
                alerts.Add(new PerformanceAlert
                {
                    Type = AlertType.ProcessMemoryHigh,
                    Message = $"进程内存使用过高: {currentMetrics.ProcessMemoryMB:F0}MB",
                    Value = currentMetrics.ProcessMemoryMB,
                    Threshold = _thresholds["ProcessMemory"],
                    Timestamp = DateTime.Now,
                    Severity = currentMetrics.ProcessMemoryMB > _thresholds["ProcessMemory"] * 1.5 ? 
                        AlertSeverity.Critical : AlertSeverity.Warning
                });
            }

            // 线程数量告警
            if (currentMetrics.ThreadCount > _thresholds["ThreadCount"])
            {
                alerts.Add(new PerformanceAlert
                {
                    Type = AlertType.ThreadCountHigh,
                    Message = $"线程数量过多: {currentMetrics.ThreadCount}",
                    Value = currentMetrics.ThreadCount,
                    Threshold = _thresholds["ThreadCount"],
                    Timestamp = DateTime.Now,
                    Severity = AlertSeverity.Warning
                });
            }

            return alerts;
        }

        /// <summary>
        /// 设置告警阈值
        /// </summary>
        /// <param name="metric">指标名称</param>
        /// <param name="threshold">阈值</param>
        public void SetThreshold(string metric, double threshold)
        {
            _thresholds[metric] = threshold;
            _logger?.LogInformation($"📊 设置告警阈值: {metric} = {threshold}");
        }

        /// <summary>
        /// 获取系统信息
        /// </summary>
        /// <returns>系统信息</returns>
        public SystemInfo GetSystemInfo()
        {
            return new SystemInfo
            {
                MachineName = Environment.MachineName,
                OSVersion = Environment.OSVersion.ToString(),
                ProcessorCount = Environment.ProcessorCount,
                WorkingSet = Environment.WorkingSet,
                SystemPageSize = Environment.SystemPageSize,
                Is64BitOperatingSystem = Environment.Is64BitOperatingSystem,
                Is64BitProcess = Environment.Is64BitProcess,
                CLRVersion = Environment.Version.ToString(),
                ProcessId = _currentProcess.Id,
                ProcessName = _currentProcess.ProcessName,
                StartTime = _currentProcess.StartTime
            };
        }

        #region 私有方法

        /// <summary>
        /// 收集性能指标
        /// </summary>
        private void CollectMetrics(object? state)
        {
            try
            {
                var metric = GetCurrentMetrics();
                
                lock (_lockObject)
                {
                    _metricsHistory.Add(metric);
                    
                    // 保持历史记录大小
                    if (_metricsHistory.Count > _historySize)
                    {
                        _metricsHistory.RemoveAt(0);
                    }
                }
                
                _logger?.LogDebug($"📊 收集性能指标: CPU={metric.CpuUsage:F1}%, Memory={metric.MemoryUsage:F1}%");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 收集性能指标失败");
            }
        }

        private double GetCpuUsage()
        {
            try
            {
                return _cpuCounter?.NextValue() ?? 0;
            }
            catch
            {
                return 0;
            }
        }

        private double GetMemoryUsage()
        {
            try
            {
                if (_memoryCounter != null)
                {
                    var availableMB = _memoryCounter.NextValue();
                    var totalMemoryMB = GC.GetTotalMemory(false) / 1024.0 / 1024.0 + availableMB;
                    return totalMemoryMB > 0 ? (totalMemoryMB - availableMB) / totalMemoryMB * 100 : 0;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private double GetProcessMemoryMB()
        {
            try
            {
                _currentProcess.Refresh();
                return _currentProcess.WorkingSet64 / 1024.0 / 1024.0;
            }
            catch
            {
                return 0;
            }
        }

        private int GetThreadCount()
        {
            try
            {
                _currentProcess.Refresh();
                return _currentProcess.Threads.Count;
            }
            catch
            {
                return 0;
            }
        }

        private int GetHandleCount()
        {
            try
            {
                _currentProcess.Refresh();
                return _currentProcess.HandleCount;
            }
            catch
            {
                return 0;
            }
        }

        private double GetGCMemoryMB()
        {
            try
            {
                return GC.GetTotalMemory(false) / 1024.0 / 1024.0;
            }
            catch
            {
                return 0;
            }
        }

        private double GetWorkingSetMB()
        {
            try
            {
                return Environment.WorkingSet / 1024.0 / 1024.0;
            }
            catch
            {
                return 0;
            }
        }

        #endregion

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _monitoringTimer?.Dispose();
                _cpuCounter?.Dispose();
                _memoryCounter?.Dispose();
                _currentProcess?.Dispose();
                
                _logger?.LogInformation("🔄 性能监控器已释放资源");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 释放资源时发生错误");
            }
        }
    }

    #region 数据模型

    /// <summary>
    /// 性能指标
    /// </summary>
    public class PerformanceMetric
    {
        public DateTime Timestamp { get; set; }
        public double CpuUsage { get; set; }
        public double MemoryUsage { get; set; }
        public double ProcessMemoryMB { get; set; }
        public int ThreadCount { get; set; }
        public int HandleCount { get; set; }
        public double GCMemoryMB { get; set; }
        public double WorkingSetMB { get; set; }
    }

    /// <summary>
    /// 指标统计
    /// </summary>
    public class MetricStats
    {
        public double Average { get; set; }
        public double Maximum { get; set; }
        public double Minimum { get; set; }
    }

    /// <summary>
    /// 性能统计信息
    /// </summary>
    public class PerformanceStats
    {
        public TimeSpan TimeRange { get; set; }
        public int SampleCount { get; set; }
        public MetricStats CpuUsage { get; set; } = new();
        public MetricStats MemoryUsage { get; set; } = new();
        public MetricStats ProcessMemory { get; set; } = new();
        public MetricStats ThreadCount { get; set; } = new();
    }

    /// <summary>
    /// 告警类型
    /// </summary>
    public enum AlertType
    {
        CpuHigh,
        MemoryHigh,
        ProcessMemoryHigh,
        ThreadCountHigh,
        DiskSpaceLow,
        NetworkError
    }

    /// <summary>
    /// 告警严重程度
    /// </summary>
    public enum AlertSeverity
    {
        Info,
        Warning,
        Critical
    }

    /// <summary>
    /// 性能告警
    /// </summary>
    public class PerformanceAlert
    {
        public AlertType Type { get; set; }
        public AlertSeverity Severity { get; set; }
        public string Message { get; set; } = string.Empty;
        public double Value { get; set; }
        public double Threshold { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 系统信息
    /// </summary>
    public class SystemInfo
    {
        public string MachineName { get; set; } = string.Empty;
        public string OSVersion { get; set; } = string.Empty;
        public int ProcessorCount { get; set; }
        public long WorkingSet { get; set; }
        public int SystemPageSize { get; set; }
        public bool Is64BitOperatingSystem { get; set; }
        public bool Is64BitProcess { get; set; }
        public string CLRVersion { get; set; } = string.Empty;
        public int ProcessId { get; set; }
        public string ProcessName { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
    }

    #endregion
}
