@echo off
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🔧 构建所有DLL模块                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 开始构建独立DLL模块...
echo.

echo 📦 1. 构建 BrowserCore.dll...
cd BrowserCore
dotnet build --configuration Release
if %ERRORLEVEL% neq 0 (
    echo ❌ BrowserCore.dll 构建失败
    pause
    exit /b 1
)
echo ✅ BrowserCore.dll 构建成功
cd ..
echo.

echo 📦 2. 构建 ProxyManager.dll...
cd ProxyManager
dotnet build --configuration Release
if %ERRORLEVEL% neq 0 (
    echo ❌ ProxyManager.dll 构建失败
    pause
    exit /b 1
)
echo ✅ ProxyManager.dll 构建成功
cd ..
echo.

echo 📦 3. 构建 FingerprintGenerator.dll...
cd FingerprintGenerator
dotnet build --configuration Release
if %ERRORLEVEL% neq 0 (
    echo ❌ FingerprintGenerator.dll 构建失败
    pause
    exit /b 1
)
echo ✅ FingerprintGenerator.dll 构建成功
cd ..
echo.

echo 📦 4. 构建 InstanceManager.dll...
cd InstanceManager
dotnet build --configuration Release
if %ERRORLEVEL% neq 0 (
    echo ❌ InstanceManager.dll 构建失败
    pause
    exit /b 1
)
echo ✅ InstanceManager.dll 构建成功
cd ..
echo.

echo 📦 5. 构建 PerformanceMonitor.dll...
cd PerformanceMonitor
dotnet build --configuration Release
if %ERRORLEVEL% neq 0 (
    echo ❌ PerformanceMonitor.dll 构建失败
    pause
    exit /b 1
)
echo ✅ PerformanceMonitor.dll 构建成功
cd ..
echo.

echo 📦 6. 构建 DataPersistence.dll...
cd DataPersistence
dotnet build --configuration Release
if %ERRORLEVEL% neq 0 (
    echo ❌ DataPersistence.dll 构建失败
    pause
    exit /b 1
)
echo ✅ DataPersistence.dll 构建成功
cd ..
echo.

echo 📁 创建输出目录...
if not exist "dist" mkdir dist
if not exist "dist\dlls" mkdir dist\dlls

echo 📋 复制DLL文件到输出目录...
copy "BrowserCore\bin\Release\net8.0\BrowserCore.dll" "dist\dlls\"
copy "ProxyManager\bin\Release\net8.0\ProxyManager.dll" "dist\dlls\"
copy "FingerprintGenerator\bin\Release\net8.0\FingerprintGenerator.dll" "dist\dlls\"
copy "InstanceManager\bin\Release\net8.0\InstanceManager.dll" "dist\dlls\"
copy "PerformanceMonitor\bin\Release\net8.0\PerformanceMonitor.dll" "dist\dlls\"
copy "DataPersistence\bin\Release\net8.0\DataPersistence.dll" "dist\dlls\"

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 所有DLL构建完成！                      ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  输出目录: dist\dlls\                                        ║
echo ║                                                              ║
echo ║  ✅ BrowserCore.dll          - 浏览器核心管理                ║
echo ║  ✅ ProxyManager.dll         - 代理池管理                    ║
echo ║  ✅ FingerprintGenerator.dll - 指纹生成器                    ║
echo ║  ✅ InstanceManager.dll      - 实例池管理                    ║
echo ║  ✅ PerformanceMonitor.dll   - 性能监控                      ║
echo ║  ✅ DataPersistence.dll      - 数据持久化                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

pause
