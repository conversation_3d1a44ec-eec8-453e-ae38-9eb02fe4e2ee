Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BrowserCore", "BrowserCore\BrowserCore.csproj", "{5FC568C1-ACE7-713D-4845-908C5808E398}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataPersistence", "DataPersistence\DataPersistence.csproj", "{8A80D846-4A1D-82F3-C73B-45DD71026DA5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DllTestConsole", "DllTestConsole\DllTestConsole.csproj", "{2A5B89BD-B2DB-2095-A965-15D1848ED6FC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FingerprintGenerator", "FingerprintGenerator\FingerprintGenerator.csproj", "{EB81E338-E8B4-9EF1-884E-B4C2100834E0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InstanceManager", "InstanceManager\InstanceManager.csproj", "{A66DEF9C-01A7-E4D1-D0F9-A57F4B2D8369}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PerformanceMonitor", "PerformanceMonitor\PerformanceMonitor.csproj", "{897F4EC6-3342-D309-FACF-DBD1057EA7D0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProxyManager", "ProxyManager\ProxyManager.csproj", "{F808CF2A-224D-11D8-581A-8D067D130BFE}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5FC568C1-ACE7-713D-4845-908C5808E398}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5FC568C1-ACE7-713D-4845-908C5808E398}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5FC568C1-ACE7-713D-4845-908C5808E398}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5FC568C1-ACE7-713D-4845-908C5808E398}.Release|Any CPU.Build.0 = Release|Any CPU
		{8A80D846-4A1D-82F3-C73B-45DD71026DA5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8A80D846-4A1D-82F3-C73B-45DD71026DA5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8A80D846-4A1D-82F3-C73B-45DD71026DA5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8A80D846-4A1D-82F3-C73B-45DD71026DA5}.Release|Any CPU.Build.0 = Release|Any CPU
		{2A5B89BD-B2DB-2095-A965-15D1848ED6FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2A5B89BD-B2DB-2095-A965-15D1848ED6FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2A5B89BD-B2DB-2095-A965-15D1848ED6FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2A5B89BD-B2DB-2095-A965-15D1848ED6FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{EB81E338-E8B4-9EF1-884E-B4C2100834E0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EB81E338-E8B4-9EF1-884E-B4C2100834E0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EB81E338-E8B4-9EF1-884E-B4C2100834E0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EB81E338-E8B4-9EF1-884E-B4C2100834E0}.Release|Any CPU.Build.0 = Release|Any CPU
		{A66DEF9C-01A7-E4D1-D0F9-A57F4B2D8369}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A66DEF9C-01A7-E4D1-D0F9-A57F4B2D8369}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A66DEF9C-01A7-E4D1-D0F9-A57F4B2D8369}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A66DEF9C-01A7-E4D1-D0F9-A57F4B2D8369}.Release|Any CPU.Build.0 = Release|Any CPU
		{897F4EC6-3342-D309-FACF-DBD1057EA7D0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{897F4EC6-3342-D309-FACF-DBD1057EA7D0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{897F4EC6-3342-D309-FACF-DBD1057EA7D0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{897F4EC6-3342-D309-FACF-DBD1057EA7D0}.Release|Any CPU.Build.0 = Release|Any CPU
		{F808CF2A-224D-11D8-581A-8D067D130BFE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F808CF2A-224D-11D8-581A-8D067D130BFE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F808CF2A-224D-11D8-581A-8D067D130BFE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F808CF2A-224D-11D8-581A-8D067D130BFE}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0B5F246A-095E-43CC-9289-FA248D93293F}
	EndGlobalSection
EndGlobal
