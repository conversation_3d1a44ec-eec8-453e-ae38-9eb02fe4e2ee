using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Net.NetworkInformation;

namespace ProxyManager
{
    /// <summary>
    /// 代理池管理器 - 独立DLL模块
    /// 功能说明：代理池管理、健康检查、故障转移、负载均衡
    /// </summary>
    public class ProxyPoolManager : IDisposable
    {
        private readonly ILogger<ProxyPoolManager>? _logger;
        private readonly ConcurrentQueue<ProxyInfo> _proxyPool;
        private readonly ConcurrentDictionary<string, ProxyStatus> _proxyStatus;
        private readonly Timer _healthCheckTimer;
        private readonly HttpClient _httpClient;
        
        // 代理轮换索引
        private int _currentIndex = 0;
        private readonly object _indexLock = new object();

        public ProxyPoolManager(ILogger<ProxyPoolManager>? logger = null)
        {
            _logger = logger;
            _proxyPool = new ConcurrentQueue<ProxyInfo>();
            _proxyStatus = new ConcurrentDictionary<string, ProxyStatus>();
            _httpClient = new HttpClient { Timeout = TimeSpan.FromSeconds(10) };
            
            // 每5分钟进行一次健康检查
            _healthCheckTimer = new Timer(PerformHealthCheck, null, Timeout.Infinite, Timeout.Infinite);
        }

        /// <summary>
        /// 初始化代理池
        /// </summary>
        /// <param name="proxyFilePath">代理配置文件路径</param>
        public async Task<bool> InitializeAsync(string proxyFilePath = "proxies.txt")
        {
            try
            {
                _logger?.LogInformation("🌐 正在初始化代理池...");
                
                await LoadProxiesFromFileAsync(proxyFilePath);
                await PerformInitialHealthCheckAsync();
                
                // 启动定期健康检查
                _healthCheckTimer.Change(TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(5));
                
                _logger?.LogInformation("✅ 代理池初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 代理池初始化失败");
                return false;
            }
        }

        /// <summary>
        /// 添加代理
        /// </summary>
        /// <param name="proxy">代理信息</param>
        public void AddProxy(ProxyInfo proxy)
        {
            _proxyPool.Enqueue(proxy);
            _proxyStatus.TryAdd(proxy.Address, new ProxyStatus 
            { 
                IsHealthy = true, 
                LastChecked = DateTime.Now 
            });
            
            _logger?.LogInformation($"➕ 添加代理: {proxy.Address}");
        }

        /// <summary>
        /// 批量添加代理
        /// </summary>
        /// <param name="proxies">代理列表</param>
        public void AddProxies(IEnumerable<ProxyInfo> proxies)
        {
            foreach (var proxy in proxies)
            {
                AddProxy(proxy);
            }
        }

        /// <summary>
        /// 获取下一个可用代理
        /// </summary>
        /// <returns>代理信息</returns>
        public ProxyInfo? GetNextProxy()
        {
            var healthyProxies = _proxyPool.Where(p => 
                _proxyStatus.TryGetValue(p.Address, out var status) && status.IsHealthy)
                .ToList();

            if (!healthyProxies.Any())
            {
                _logger?.LogWarning("⚠️ 没有可用的健康代理");
                return null;
            }

            lock (_indexLock)
            {
                var proxy = healthyProxies[_currentIndex % healthyProxies.Count];
                _currentIndex++;
                
                _logger?.LogDebug($"🔄 分配代理: {proxy.Address}");
                return proxy;
            }
        }

        /// <summary>
        /// 获取随机代理
        /// </summary>
        /// <returns>代理信息</returns>
        public ProxyInfo? GetRandomProxy()
        {
            var healthyProxies = _proxyPool.Where(p => 
                _proxyStatus.TryGetValue(p.Address, out var status) && status.IsHealthy)
                .ToList();

            if (!healthyProxies.Any())
            {
                _logger?.LogWarning("⚠️ 没有可用的健康代理");
                return null;
            }

            var randomIndex = Random.Shared.Next(healthyProxies.Count);
            var proxy = healthyProxies[randomIndex];
            
            _logger?.LogDebug($"🎲 随机分配代理: {proxy.Address}");
            return proxy;
        }

        /// <summary>
        /// 标记代理为不健康
        /// </summary>
        /// <param name="proxyAddress">代理地址</param>
        /// <param name="reason">失败原因</param>
        public void MarkProxyUnhealthy(string proxyAddress, string reason)
        {
            if (_proxyStatus.TryGetValue(proxyAddress, out var status))
            {
                status.IsHealthy = false;
                status.FailureCount++;
                status.LastFailureReason = reason;
                status.LastChecked = DateTime.Now;
                
                _logger?.LogWarning($"⚠️ 标记代理不健康: {proxyAddress}, 原因: {reason}");
            }
        }

        /// <summary>
        /// 获取代理池统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public ProxyPoolStats GetStats()
        {
            var totalProxies = _proxyPool.Count;
            var healthyProxies = _proxyStatus.Values.Count(s => s.IsHealthy);
            var unhealthyProxies = totalProxies - healthyProxies;

            return new ProxyPoolStats
            {
                TotalProxies = totalProxies,
                HealthyProxies = healthyProxies,
                UnhealthyProxies = unhealthyProxies,
                HealthRate = totalProxies > 0 ? (double)healthyProxies / totalProxies : 0,
                LastHealthCheck = _proxyStatus.Values.Any() ? 
                    _proxyStatus.Values.Max(s => s.LastChecked) : DateTime.MinValue
            };
        }

        /// <summary>
        /// 获取所有代理信息
        /// </summary>
        /// <returns>代理列表</returns>
        public List<ProxyInfo> GetAllProxies()
        {
            return _proxyPool.ToList();
        }

        /// <summary>
        /// 获取健康代理列表
        /// </summary>
        /// <returns>健康代理列表</returns>
        public List<ProxyInfo> GetHealthyProxies()
        {
            return _proxyPool.Where(p => 
                _proxyStatus.TryGetValue(p.Address, out var status) && status.IsHealthy)
                .ToList();
        }

        /// <summary>
        /// 手动执行健康检查
        /// </summary>
        /// <returns>检查结果</returns>
        public async Task<HealthCheckResult> PerformHealthCheckAsync()
        {
            var startTime = DateTime.Now;
            var checkedCount = 0;
            var healthyCount = 0;

            try
            {
                _logger?.LogInformation("🔍 开始代理健康检查...");
                
                var tasks = _proxyPool.Select(async proxy =>
                {
                    var isHealthy = await CheckProxyHealthAsync(proxy);
                    Interlocked.Increment(ref checkedCount);
                    
                    if (_proxyStatus.TryGetValue(proxy.Address, out var status))
                    {
                        status.IsHealthy = isHealthy;
                        status.LastChecked = DateTime.Now;
                        
                        if (isHealthy)
                        {
                            status.FailureCount = 0;
                            Interlocked.Increment(ref healthyCount);
                        }
                        else
                        {
                            status.FailureCount++;
                        }
                    }
                });

                await Task.WhenAll(tasks);
                
                var duration = DateTime.Now - startTime;
                var result = new HealthCheckResult
                {
                    Success = true,
                    CheckedCount = checkedCount,
                    HealthyCount = healthyCount,
                    Duration = duration,
                    Message = $"检查完成 - 健康: {healthyCount}/{checkedCount}"
                };
                
                _logger?.LogInformation($"✅ 健康检查完成 - 健康: {healthyCount}/{checkedCount}, 耗时: {duration.TotalSeconds:F2}秒");
                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 代理健康检查失败");
                return new HealthCheckResult
                {
                    Success = false,
                    Message = ex.Message,
                    Duration = DateTime.Now - startTime
                };
            }
        }

        /// <summary>
        /// 从文件加载代理配置
        /// </summary>
        private async Task LoadProxiesFromFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger?.LogWarning($"⚠️ 代理配置文件不存在: {filePath}");
                    return;
                }

                var lines = await File.ReadAllLinesAsync(filePath);
                var loadedCount = 0;

                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                        continue;

                    var proxy = ParseProxyLine(line.Trim());
                    if (proxy != null)
                    {
                        AddProxy(proxy);
                        loadedCount++;
                    }
                }

                _logger?.LogInformation($"📡 从文件加载 {loadedCount} 个代理服务器");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 加载代理配置文件失败");
            }
        }

        /// <summary>
        /// 解析代理配置行
        /// 格式要求：服务器:端口:账号:密码
        /// </summary>
        private ProxyInfo? ParseProxyLine(string line)
        {
            try
            {
                var parts = line.Split(':');

                // 必须是4个部分：服务器:端口:账号:密码
                if (parts.Length != 4)
                {
                    _logger?.LogWarning($"⚠️ 代理格式错误，应为 服务器:端口:账号:密码 格式: {line}");
                    return null;
                }

                var proxy = new ProxyInfo
                {
                    Host = parts[0].Trim(),
                    Port = int.Parse(parts[1].Trim()),
                    Username = parts[2].Trim(),
                    Password = parts[3].Trim(),
                    Type = "HTTP"
                };

                // 生成代理地址
                proxy.Address = $"{proxy.Username}:{proxy.Password}@{proxy.Host}:{proxy.Port}";

                return proxy;
            }
            catch (Exception ex)
            {
                _logger?.LogWarning($"⚠️ 解析代理配置失败: {line}, 错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 执行初始健康检查
        /// </summary>
        private async Task PerformInitialHealthCheckAsync()
        {
            await PerformHealthCheckAsync();
        }

        /// <summary>
        /// 定期健康检查
        /// </summary>
        private async void PerformHealthCheck(object? state)
        {
            await PerformHealthCheckAsync();
        }

        /// <summary>
        /// 检查单个代理的健康状态
        /// </summary>
        private async Task<bool> CheckProxyHealthAsync(ProxyInfo proxy)
        {
            try
            {
                // 简单的连接测试 - 尝试ping代理服务器
                using var ping = new Ping();
                var reply = await ping.SendPingAsync(proxy.Host, 5000);
                return reply.Status == IPStatus.Success;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _healthCheckTimer?.Dispose();
            _httpClient?.Dispose();
            _logger?.LogInformation("🔄 代理池管理器已释放资源");
        }
    }

    #region 数据模型

    /// <summary>
    /// 代理信息
    /// </summary>
    public class ProxyInfo
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; }
        public string Type { get; set; } = "HTTP";
        public string? Username { get; set; }
        public string? Password { get; set; }
        public string Address { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 代理状态
    /// </summary>
    public class ProxyStatus
    {
        public bool IsHealthy { get; set; } = true;
        public DateTime LastChecked { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public int FailureCount { get; set; }
        public string? LastFailureReason { get; set; }
    }

    /// <summary>
    /// 代理池统计信息
    /// </summary>
    public class ProxyPoolStats
    {
        public int TotalProxies { get; set; }
        public int HealthyProxies { get; set; }
        public int UnhealthyProxies { get; set; }
        public double HealthRate { get; set; }
        public DateTime LastHealthCheck { get; set; }
    }

    /// <summary>
    /// 健康检查结果
    /// </summary>
    public class HealthCheckResult
    {
        public bool Success { get; set; }
        public int CheckedCount { get; set; }
        public int HealthyCount { get; set; }
        public TimeSpan Duration { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    #endregion
}
