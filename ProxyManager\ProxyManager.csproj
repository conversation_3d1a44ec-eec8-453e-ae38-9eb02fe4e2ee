<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>BatchBrowser.ProxyManager</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>BatchBrowser Team</Authors>
    <Description>代理池管理模块 - 独立DLL</Description>
  </PropertyGroup>

  <ItemGroup>
    <!-- JSON处理 -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    
    <!-- 日志 -->
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    
    <!-- HTTP客户端 -->
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
  </ItemGroup>

</Project>
