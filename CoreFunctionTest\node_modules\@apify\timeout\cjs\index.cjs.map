{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["// eslint-disable-next-line max-classes-per-file\nimport { AsyncLocalStorage } from 'node:async_hooks';\n\nexport interface AbortContext {\n    cancelTask: AbortController;\n}\n\n/**\n * `AsyncLocalStorage` instance that is used for baring the AbortContext inside user provided handler.\n * We can use it to access the `AbortContext` instance via `storage.getStore()`, and there we can access\n * `cancelTask` instance of `AbortController`.\n */\nexport const storage = new AsyncLocalStorage<AbortContext>();\n\n/**\n * Custom error class that will be used for timeout error.\n */\nexport class TimeoutError extends Error {\n}\n\n/**\n * Custom error class to handle `tryCancel()` checks.\n * This should not be exposed to user land, as it will be caught in.\n */\nclass InternalTimeoutError extends TimeoutError {\n}\n\n/**\n * Checks whether we are inside timeout handler created by this package, and cancels current\n * task execution by throwing `TimeoutError`. This error will be ignored if the promise timed\n * out already, or explicitly skipped in `addTimeoutToPromise`.\n *\n * Use this function after every async call that runs inside the timeout handler:\n *\n * ```ts\n * async function doSomething() {\n *     await doSomethingTimeConsuming();\n *     tryCancel();\n *     await doSomethingElse();\n *     tryCancel();\n * }\n * ```\n */\nexport function tryCancel(): void {\n    const signal = storage.getStore()?.cancelTask.signal;\n\n    if (signal?.aborted) {\n        throw new InternalTimeoutError('Promise handler has been canceled due to a timeout');\n    }\n}\n\n/**\n * Runs given handler and rejects with the given `errorMessage` (or `Error` instance)\n * after given `timeoutMillis`, unless the original promise resolves or rejects earlier.\n * Use `tryCancel()` function inside the handler after each await to finish its execution\n * early when the timeout appears.\n *\n * ```ts\n * const res = await addTimeoutToPromise(\n *   () => handler(),\n *   200,\n *   'Handler timed out after 200ms!',\n * );\n * ```\n */\nexport async function addTimeoutToPromise<T>(handler: () => Promise<T>, timeoutMillis: number, errorMessage: string | Error): Promise<T> {\n    // respect existing context to support nesting\n    const context = storage.getStore() ?? {\n        cancelTask: new AbortController(),\n    };\n    let returnValue: T;\n\n    // calls handler, skips internal `TimeoutError`s that might have been thrown\n    // via `tryCancel()` and aborts the timeout promise after the handler finishes\n    const wrap = async () => {\n        try {\n            returnValue = await handler();\n        } catch (e) {\n            if (!(e instanceof InternalTimeoutError)) {\n                throw e;\n            }\n        }\n    };\n\n    return new Promise((resolve, reject) => {\n        const timeout = setTimeout(() => {\n            context.cancelTask.abort();\n            const error = errorMessage instanceof Error ? errorMessage : new TimeoutError(errorMessage);\n            reject(error);\n        }, timeoutMillis);\n\n        storage.run(context, () => {\n            wrap()\n                .then(() => resolve(returnValue))\n                .catch(reject)\n                .finally(() => clearTimeout(timeout));\n        });\n    });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,8BAAkC;AAW3B,IAAM,UAAU,IAAI,0CAAgC;AAKpD,IAAM,gBAAN,MAAM,sBAAqB,MAAM;AACxC;AADwC;AAAjC,IAAM,eAAN;AAOP,IAAM,wBAAN,MAAM,8BAA6B,aAAa;AAChD;AADgD;AAAhD,IAAM,uBAAN;AAmBO,SAAS,YAAkB;AAC9B,QAAM,SAAS,QAAQ,SAAS,GAAG,WAAW;AAE9C,MAAI,QAAQ,SAAS;AACjB,UAAM,IAAI,qBAAqB,oDAAoD;AAAA,EACvF;AACJ;AANgB;AAsBhB,eAAsB,oBAAuB,SAA2B,eAAuB,cAA0C;AAErI,QAAM,UAAU,QAAQ,SAAS,KAAK;AAAA,IAClC,YAAY,IAAI,gBAAgB;AAAA,EACpC;AACA,MAAI;AAIJ,QAAM,OAAO,mCAAY;AACrB,QAAI;AACA,oBAAc,MAAM,QAAQ;AAAA,IAChC,SAAS,GAAG;AACR,UAAI,EAAE,aAAa,uBAAuB;AACtC,cAAM;AAAA,MACV;AAAA,IACJ;AAAA,EACJ,GARa;AAUb,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,UAAU,WAAW,MAAM;AAC7B,cAAQ,WAAW,MAAM;AACzB,YAAM,QAAQ,wBAAwB,QAAQ,eAAe,IAAI,aAAa,YAAY;AAC1F,aAAO,KAAK;AAAA,IAChB,GAAG,aAAa;AAEhB,YAAQ,IAAI,SAAS,MAAM;AACvB,WAAK,EACA,KAAK,MAAM,QAAQ,WAAW,CAAC,EAC/B,MAAM,MAAM,EACZ,QAAQ,MAAM,aAAa,OAAO,CAAC;AAAA,IAC5C,CAAC;AAAA,EACL,CAAC;AACL;AAjCsB;", "names": []}