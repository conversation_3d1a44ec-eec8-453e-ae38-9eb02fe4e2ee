{"version": 3, "file": "browser-crawler.js", "sourceRoot": "", "sources": ["../../src/internals/browser-crawler.ts"], "names": [], "mappings": ";;;AAkyBA,gEA8BC;AAMD,kDA+BC;;AAt1BD,0CAcwB;AAUxB,wDAA+E;AAG/E,0CAA4F;AAC5F,oDAAoB;AAGpB,4CAAgE;AAmOhE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AACH,MAAsB,cAKpB,SAAQ,oBAAqB;IAwC3B;;OAEG;IACH,YACI,UAA0C,EAAE,EAC1B,SAAS,qBAAa,CAAC,eAAe,EAAE;;QAE1D,IAAA,YAAE,EAAC,OAAO,EAAE,uBAAuB,EAAE,YAAE,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC;QACxF,MAAM,EACF,qBAAqB,GAAG,EAAE,EAC1B,yBAAyB,GAAG,EAAE,EAC9B,wBAAwB,EACxB,kBAAkB,EAClB,aAAa,GAAG,EAAE,EAClB,kBAAkB,EAClB,kBAAkB,GAAG,EAAE,EACvB,mBAAmB,GAAG,EAAE;QACxB,UAAU;QACV,qBAAqB,EAErB,cAAc,EAAE,0BAA0B,EAC1C,kBAAkB,EAElB,oBAAoB,EACpB,2BAA2B,EAC3B,QAAQ,EACR,iBAAiB,EACjB,aAAa,EACb,GAAG,mBAAmB,EACzB,GAAG,OAAO,CAAC;QAEZ,KAAK,CACD;YACI,GAAG,mBAAmB;YACtB,cAAc,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAI,IAAkB,CAAC;YAClF,yBAAyB,EACrB,qBAAqB,GAAG,yBAAyB,GAAG,yCAAiC;SAC5F,EACD,MAAM,CACT,CAAC;QAlCF;;;;mBAAkB,MAAM;WAAkC;QA5C9D;;;WAGG;QACH;;;;;WAAwC;QAExC;;WAEG;QACH;;;;;WAAqD;QAErD;;;;;WAA4D;QAElD;;;;;WAA4D;QAC5D;;;;;WAAgC;QAChC;;;;;WAAyC;QACzC;;;;;WAA2C;QAC3C;;;;;WAA4C;QAC5C;;;;;WAAkC;QA8DxC,IAAI,CAAC,yBAAyB,CAAC;YAC3B,OAAO,EAAE,gBAAgB;YACzB,OAAO,EAAE,oBAAoB;YAC7B,WAAW,EAAE,4BAA4B;YACzC,WAAW,EAAE,0BAA0B;YACvC,WAAW,EAAE,kBAAkB;YAC/B,cAAc,EAAE,IAAI,EAAE,iCAAiC;SAC1D,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YACnC,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,MAAM,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,yBAAyB,CAAC;YAC3B,OAAO,EAAE,sBAAsB;YAC/B,OAAO,EAAE,6BAA6B;YACtC,WAAW,EAAE,sBAAsB;YACnC,WAAW,EAAE,oBAAoB;YACjC,WAAW,EAAE,2BAA2B;YACxC,cAAc,EAAE,IAAI;SACvB,CAAC,CAAC;QAEH,uEAAuE;QACvE,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,wBAAwB,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,iFAAiF,CAAC,CAAC;QACvG,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,uBAAuB,GAAG,qBAAqB,GAAG,IAAI,CAAC;QAC5D,IAAI,CAAC,gCAAgC,GAAG,yBAAyB,GAAG,IAAI,CAAC;QACzE,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAE/C,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACnB,MAAA,IAAI,CAAC,aAAa,EAAC,aAAa,QAAb,aAAa,GAAK,EAAmB,EAAC;YACxD,IAAI,CAAC,aAAa,CAAC,aAA4B,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,KAAK,SAAS,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7G,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;QAC1C,CAAC;QAED,IAAI,aAAa,EAAE,SAAS,EAAE,CAAC;YAC3B,IAAI,kBAAkB,CAAC,eAAe;gBAClC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gFAAgF,CAAC,CAAC;YACpG,kBAAkB,CAAC,eAAe,GAAG,KAAK,CAAC;QAC/C,CAAC;QAED,MAAM,EAAE,cAAc,GAAG,EAAE,EAAE,eAAe,GAAG,EAAE,EAAE,GAAG,IAAI,EAAE,GAAG,kBAAkB,CAAC;QAElF,IAAI,CAAC,WAAW,GAAG,IAAI,0BAAW,CAA6B;YAC3D,GAAI,IAAY;YAChB,cAAc,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,cAAc,CAAC;YACzE,eAAe,EAAE,CAAC,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,eAAe,CAAC;SACzF,CAAC,CAAC;IACP,CAAC;IAEkB,KAAK,CAAC,eAAe,CAAC,eAAwB;QAC7D,MAAM,EAAE,IAAI,EAAE,GAAG,eAAe,CAAC;QAEjC,+BAA+B;QAC/B,IAAI,IAAI,EAAE,CAAC;YACP,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QACtG,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAgB,EAAE,SAAmB;QACjE,MAAM,cAAc,GAAG,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAE,IAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC7F,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAkB,CAAC;aACjD,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC;aAC3B,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC;QAErC,OAAO,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7D,CAAC;IAEkB,KAAK,CAAC,gBAAgB,CAAC,eAAwB;QAC9D,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,eAAe,CAAC;QAE3C,MAAM,kBAAkB;QACpB,wCAAwC;QACxC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,oBAAoB,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC;YACtD,CAAC,CAAC,wCAAwC;gBACxC,IAAI,CAAC,WAAY,CAAC,oBAAoB,CAAC;YACzC,CAAC,CAAC,4BAA4B,CAAC;QAEvC,yGAAyG;QACzG,IAAI,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,sCAA8B,CAAC,CAAC,IAAI,QAAQ,EAAE,MAAM,EAAE,KAAK,GAAG,EAAE,CAAC;YACrG,MAAM,IAAA,aAAK,EAAC,IAAI,CAAC,CAAC;YAElB,+IAA+I;YAC/I,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,2BAAmB,CAAC,CAAC;YAE/E,IAAI,CAAC,cAAc;gBAAE,OAAO,KAAK,CAAC;YAClC,OAAO,iDAAiD,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACxF,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,2BAAmB,CAAC,CAAC;QAC/E,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QAE1F,IAAI,cAAc;YAAE,OAAO,oBAAoB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3E,IAAI,iBAAiB;YAAE,OAAO,iCAAiC,iBAAiB,EAAE,CAAC;QAEnF,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACgB,KAAK,CAAC,kBAAkB,CAAC,eAAwB;QAChE,MAAM,cAAc,GAAe;YAC/B,EAAE,EAAE,eAAe,CAAC,EAAE;SACzB,CAAC;QAEF,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,EAAE,iBAAiB,CAAC;QAChE,MAAM,sBAAsB,GAAG,IAAI,CAAC,aAAa,EAAE,sBAAsB,CAAC;QAE1E,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,MAAM,EAAE,OAAO,EAAE,GAAG,eAAe,CAAC;YAEpC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,EAAE;gBACtE,OAAO,EAAE,eAAe,CAAC,OAAO;aACnC,CAAC,CAAC;YACH,eAAe,CAAC,SAAS,GAAG,SAAS,CAAC;YAEtC,cAAc,CAAC,QAAQ,GAAG,SAAS,EAAE,GAAG,CAAC;YACzC,cAAc,CAAC,SAAS,GAAG,SAAS,EAAE,SAAS,CAAC;YAEhD,IAAI,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;gBAC3C;;;mBAGG;gBACH,cAAc,CAAC,WAAW,GAAG;oBACzB,iBAAiB,EAAE,IAAI;oBACvB,mBAAmB,EAAE,IAAI;iBAC5B,CAAC;YACN,CAAC;QACL,CAAC;QAED,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,cAAc,CAAC,CAAe,CAAC;QAC5E,IAAA,mBAAS,GAAE,CAAC;QACZ,IAAI,CAAC,mCAAmC,CAAC,eAAe,EAAE,IAAI,EAAE,iBAAiB,IAAI,sBAAsB,CAAC,CAAC;QAE7G,+BAA+B;QAC/B,iEAAiE;QACjE,6EAA6E;QAC7E,+GAA+G;QAC/G,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,eAAe,CAAC;QAE7C,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC9C,IAAA,mBAAS,GAAE,CAAC;YAEZ,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;YAC7C,IAAA,mBAAS,GAAE,CAAC;YAEZ,eAAe;YACf,oEAAoE;YACpE,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAChC,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACzE,IAAA,mBAAS,GAAE,CAAC;gBACZ,OAAO,EAAE,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,SAAU,CAAC,CAAC;YACrD,CAAC;QACL,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,EAAE,CAAC;YAC/C,IAAI,CAAC,GAAG,CAAC,KAAK;YACV,wCAAwC;YACxC,oBAAoB,OAAO,CAAC,EAAE,mBAAmB,OAAO,CAAC,GAAG,mBAAmB,OAAO,CAAC,SAAS,qDAAqD,OAAO,CAAC,iBAAiB,CAAC,IAAI,CACtL,CAAC;YAEF,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YACvB,OAAO,CAAC,KAAK,GAAG,oBAAY,CAAC,OAAO,CAAC;YAErC,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;YAC3D,IAAI,KAAK;gBAAE,MAAM,IAAI,oBAAY,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,CAAC,KAAK,GAAG,oBAAY,CAAC,eAAe,CAAC;QAC7C,IAAI,CAAC;YACD,MAAM,IAAA,6BAAmB,EACrB,KAAK,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,eAAyC,CAAC,CAAC,EACvG,IAAI,CAAC,gCAAgC,EACrC,kCAAkC,IAAI,CAAC,gCAAgC,GAAG,IAAI,WAAW,CAC5F,CAAC;YAEF,OAAO,CAAC,KAAK,GAAG,oBAAY,CAAC,IAAI,CAAC;QACtC,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YACd,OAAO,CAAC,KAAK,GAAG,oBAAY,CAAC,KAAK,CAAC;YACnC,MAAM,CAAC,CAAC;QACZ,CAAC;QACD,IAAA,mBAAS,GAAE,CAAC;IAChB,CAAC;IAES,mCAAmC,CACzC,eAAwB,EACxB,IAAgB,EAChB,gBAA0B;QAE1B,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC;QAE5B,gFAAgF;QAChF,6HAA6H;QAC7H,kGAAkG;QAClG,2DAA2D;QAC3D,MAAM,yBAAyB,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,CACzE,IAAW,CACkB,CAAC;QAClC,eAAe,CAAC,iBAAiB,GAAG,yBAAyB,CAAC;QAE9D,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpB,eAAe,CAAC,OAAO,GAAG,yBAAyB,CAAC,aAAa,CAAC,OAAkB,CAAC;QACzF,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;YAC7B,eAAe,CAAC,SAAS,GAAG,yBAAyB,CAAC,aAAa,CAAC,SAAsB,CAAC;QAC/F,CAAC;QAED,eAAe,CAAC,YAAY,GAAG,KAAK,EAAE,cAAc,EAAE,EAAE;YACpD,OAAO,0BAA0B,CAAC;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI;gBACJ,YAAY,EAAE,MAAM,IAAI,CAAC,eAAe,EAAE;gBAC1C,aAAa,EAAE,MAAM,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC;gBAC7E,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;gBACvC,kBAAkB,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG;gBAC/C,eAAe,EAAE,eAAe,CAAC,OAAO,CAAC,SAAS;aACrD,CAAC,CAAC;QACP,CAAC,CAAC;IACN,CAAC;IAES,KAAK,CAAC,iBAAiB,CAAC,eAAwB;QACtD,MAAM,WAAW,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,uBAAuB,EAA4B,CAAC;QAExF,MAAM,yBAAyB,GAAG,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAE5F,eAAe,CAAC,OAAO,CAAC,KAAK,GAAG,oBAAY,CAAC,UAAU,CAAC;QACxD,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,eAAe,EAAE,WAAW,CAAC,CAAC;QAChF,IAAA,mBAAS,GAAE,CAAC;QAEZ,MAAM,0BAA0B,GAAG,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAE7F,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,yBAAyB,EAAE,0BAA0B,CAAC,CAAC;QAEjG,IAAI,CAAC;YACD,eAAe,CAAC,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,IAAI,SAAS,CAAC;QAC1G,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,CAAC,wBAAwB,CAAC,eAAe,EAAE,KAAc,CAAC,CAAC;YAErE,eAAe,CAAC,OAAO,CAAC,KAAK,GAAG,oBAAY,CAAC,KAAK,CAAC;YAEnD,IAAI,CAAC,kBAAkB,CAAC,KAAc,CAAC,CAAC;YACxC,MAAM,KAAK,CAAC;QAChB,CAAC;QACD,IAAA,mBAAS,GAAE,CAAC;QAEZ,eAAe,CAAC,OAAO,CAAC,KAAK,GAAG,oBAAY,CAAC,SAAS,CAAC;QACvD,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,EAAE,eAAe,EAAE,WAAW,CAAC,CAAC;IACrF,CAAC;IAES,KAAK,CAAC,aAAa,CACzB,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAW,EACtD,eAAuB,EACvB,gBAAwB;QAExB,MAAM,aAAa,GAAG,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAC7D,MAAM,qBAAqB,GAAG,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,iCAAyB,EAAC,CAAC,CAAC,CAAC,CAAC;QACtG,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,iCAAyB,EAAC,CAAC,CAAC,CAAC,CAAC;QAExG,MAAM,iBAAiB,CAAC,UAAU,CAC9B,IAAI,EACJ,CAAC,GAAG,aAAa,EAAE,GAAG,qBAAqB,EAAE,GAAG,sBAAsB,CAAC;aAClE,MAAM,CAAC,CAAC,CAAC,EAAqB,EAAE,CAAC,OAAO,CAAC,KAAK,WAAW,IAAI,CAAC,KAAK,IAAI,CAAC;aACxE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CACvE,CAAC;IACN,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,wBAAwB,CAAC,eAAwB,EAAE,KAAY;QAC3E,MAAM,EAAE,OAAO,EAAE,GAAG,eAAe,CAAC;QAEpC,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YACrD,IAAA,4BAAoB,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACnE,CAAC;QAED,MAAM,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACO,kBAAkB,CAAC,KAAY;QACrC,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,oBAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAW,CAAC,CAAC;QACvE,CAAC;IACL,CAAC;IAOD;;OAEG;IACO,KAAK,CAAC,gBAAgB,CAAC,eAAwB;QACrD,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,eAAe,CAAC;QAE7D,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YACxE,MAAM,MAAM,GAAW,QAAQ,CAAC,MAAM,EAAE,CAAC;YAEzC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC;YAC1C,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBACxE,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAC5D,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC/E,CAAC;QACL,CAAC;QAED,OAAO,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;IACzC,CAAC;IAES,KAAK,CAAC,oBAAoB,CAAC,OAAe,EAAE,aAA4B;QAC9E,MAAM,oBAAoB,GAAiD,EAAE,CAAC;QAE9E,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,oBAAoB,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;QACvE,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YACrD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,oBAAoB,CAAC,OAAO,EAAE,EAAE,EAAE;gBAC3F,SAAS,EAAG,aAAa,CAAC,SAAoB,IAAI,SAAS;aAC9D,CAAC,CAAC;YACH,aAAa,CAAC,QAAQ,GAAG,SAAS,EAAE,GAAG,CAAC;YACxC,oBAAoB,CAAC,SAAS,GAAG,SAAS,CAAC;YAE3C,4CAA4C;YAC5C,IAAI,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;gBAC3C;;;mBAGG;gBACF,aAAa,CAAC,aAA4B,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBACpE,aAAa,CAAC,aAA4B,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAC3E,CAAC;QACL,CAAC;QAED,aAAa,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;IAC/C,CAAC;IAES,+BAA+B,CAAC,OAAe,EAAE,iBAA+C;QACtG,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,MAAM,QAAQ,GAAG,CAAC,OAAgB,EAAE,EAAE;gBAClC,MAAM,EAAE,aAAa,EAAE,GAAG,iBAAiB,CAAC;gBAC5C,IAAI,OAAO,CAAC,EAAE,KAAM,aAAa,CAAC,OAAmB,CAAC,EAAE,EAAE,CAAC;oBACvD,IAAI,CAAC,WAAW,CAAC,uBAAuB,CACpC,iBAEI,CACP,CAAC;gBACN,CAAC;YACL,CAAC,CAAC;YAEF,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,6BAAqB,EAAE,QAAQ,CAAC,CAAC;YACrD,iBAAiB,CAAC,EAAE,iEAA2C,GAAG,EAAE;gBAChE,OAAO,IAAI,CAAC,WAAY,CAAC,cAAc,CAAC,6BAAqB,EAAE,QAAQ,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED;;;OAGG;IACM,KAAK,CAAC,QAAQ;QACnB,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QACjC,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC3B,CAAC;;AA3dL,wCA4dC;AAlc6B;;;;WAAe;QACrC,GAAG,oBAAY,CAAC,YAAY;QAC5B,kBAAkB,EAAE,YAAE,CAAC,QAAQ,CAAC,QAAQ;QAExC,qBAAqB,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;QACxD,kBAAkB,EAAE,YAAE,CAAC,QAAQ,CAAC,KAAK;QACrC,mBAAmB,EAAE,YAAE,CAAC,QAAQ,CAAC,KAAK;QAEtC,aAAa,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;QACjC,QAAQ,EAAE,YAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAE,CAAC,OAAO,EAAE,YAAE,CAAC,MAAM,CAAC;QAChD,kBAAkB,EAAE,YAAE,CAAC,MAAM;QAC7B,kBAAkB,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;QACtC,wBAAwB,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;QAC7C,cAAc,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;QACnC,kBAAkB,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,kBAAU,CAAC,kBAAkB,CAAC;QAC9E,iBAAiB,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;QACtC,aAAa,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;KACrC;EAjBqC,CAiBpC;AA8bN,gBAAgB;AACT,KAAK,UAAU,0BAA0B,CAAC,EAC7C,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,aAAa,EACb,gBAAgB,EAChB,kBAAkB,EAClB,eAAe,GACW;IAC1B,MAAM,OAAO,GAAG,IAAA,8CAAsC,EAAC;QACnD,eAAe,EAAE,OAAO,EAAE,QAAQ;QAClC,eAAe;QACf,kBAAkB;QAClB,mBAAmB,EAAE,OAAO,EAAE,OAAO;KACxC,CAAC,CAAC;IAEH,MAAM,IAAI,GAAG,MAAM,mBAAmB,CAClC,IAAW,EACX,OAAO,EAAE,QAAQ,IAAI,GAAG,EACxB,OAAO,EAAE,OAAO,IAAI,eAAe,IAAI,kBAAkB,CAC5D,CAAC;IAEF,OAAO,IAAA,oBAAY,EAAC;QAChB,YAAY;QACZ,aAAa;QACb,gBAAgB;QAChB,IAAI;QACJ,OAAO;QACP,GAAI,OAA+B;KACtC,CAAC,CAAC;AACP,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,mBAAmB;AACrC,sEAAsE;AACtE,IAA0B,EAC1B,QAAgB,EAChB,OAAe;IAEf,MAAM,IAAI,GACN,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,OAA0B,EAAE,EAAE,CACxD,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAC5E,CAAC,IAAI,EAAE,CAAC;IACb,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAsB,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/G,MAAM,eAAe,GAAG,IAAI,IAAI,IAAA,sBAAc,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAE9D,IAAI,eAAe,EAAE,CAAC;QAClB,OAAO,GAAG,eAAe,CAAC;IAC9B,CAAC;IAED,OAAO,IAAI;SACN,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE;QAClB,yHAAyH;QACzH,MAAM,cAAc,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,6CAA6C;QACtG,IAAI,CAAC,cAAc,IAAI,CAAC,OAAO,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CACX,qBAAqB,IAAI,+CAA+C;gBACpE,+EAA+E,CACtF,CAAC;QACN,CAAC;QAED,OAAO,OAAO,CAAC,CAAC,CAAC,IAAA,sBAAc,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1D,CAAC,CAAC;SACD,MAAM,CAAC,CAAC,IAAwB,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACtD,CAAC"}