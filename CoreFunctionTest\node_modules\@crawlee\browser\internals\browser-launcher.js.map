{"version": 3, "file": "browser-launcher.js", "sourceRoot": "", "sources": ["../../src/internals/browser-launcher.ts"], "names": [], "mappings": ";;;;AAAA,8DAAyB;AACzB,8DAAyB;AAEzB,0CAA+C;AAG/C,oDAAoB;AAEpB,MAAM,gBAAgB,GAAG;IACrB,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,GAAG;CACd,CAAC;AAyEF;;;GAGG;AACH,MAAsB,eAAe;IA2BjC,MAAM,CAAC,sBAAsB,CAAI,QAAgB,EAAE,cAAsB;QACrE,IAAI,CAAC;YACD,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB;QACpD,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,MAAM,CAAC,GAAG,GAA+B,CAAC;YAC1C,IAAI,CAAC,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBAChC,MAAM,GAAG,GACL,uBAAuB,QAAQ,+BAA+B,QAAQ,cAAc;oBACpF,uBAAuB,QAAQ,mFAAmF,CAAC;gBACvH,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;oBAC/B,CAAC,CAAC,OAAO,GAAG,GAAG,GAAG,6BAA6B,QAAQ,+BAA+B,cAAc,gBAAgB,CAAC;gBACzH,CAAC;YACL,CAAC;YAED,MAAM,GAAG,CAAC;QACd,CAAC;IACL,CAAC;IAED;;OAEG;IACH,YACI,aAA4D,EACnD,SAAS,qBAAa,CAAC,eAAe,EAAE;QAAjD;;;;mBAAS,MAAM;WAAkC;QA3CrD;;;;;WAAmB;QACnB;;;;;WAAkB;QAClB;;;;;WAAoB;QACpB;;;;;WAA0B;QAC1B;;;;;WAAoC;QACpC,mCAAmC;QACnC;;;;;WAAW;QACX;;;;;WAAmB;QAsCf,MAAM,EACF,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,aAAa,GAAG,EAAE,EAClB,GAAG,uBAAuB,EAC7B,GAAG,aAAa,CAAC;QAElB,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QAEzC,6DAA6D;QAC7D,IAAI,CAAC,QAAQ,GAAG,QAAS,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,uBAAuB,GAAG,uBAAqC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,mBAAmB;QACf,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,aAAa,EAAE,IAAI,CAAC,mBAAmB,EAAE;YACzC,GAAG,IAAI,CAAC,uBAAuB;SAClC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACH,MAAM;QACF,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC1C,MAAM,OAAO,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;QAE7C,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAiB,CAAC;IAClD,CAAC;IAED,mBAAmB;QACf,MAAM,aAAa,GAAoC;YACnD,IAAI,EAAE,EAAE;YACR,eAAe,EAAE,gBAAgB;YACjC,GAAG,IAAI,CAAC,aAAa;SACxB,CAAC;QAEF,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,CAAC;YAC3C,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,aAAa,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;YACjC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YAClD,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACnE,CAAC;QAED,OAAO,aAAa,CAAC;IACzB,CAAC;IAES,yBAAyB;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC3E,CAAC;IAES,wBAAwB;QAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,+BAA+B,EAAE,CAAC,CAAC;IAC3F,CAAC;IAED;;OAEG;IACO,+BAA+B;QACrC;;;;;WAKG;QACH,MAAM,YAAY,GAAG,GAAG,EAAE;YACtB,IAAI,oBAAoB,GAAG,4DAA4D,CAAC;YACxF,MAAM,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,2CAA2C,CAAC;YACtF,MAAM,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,2CAA2C,CAAC;YAE9F,IAAI,iBAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBACxB,oBAAoB,GAAG,MAAM,CAAC;YAClC,CAAC;iBAAM,IAAI,iBAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC/B,oBAAoB,GAAG,MAAM,CAAC;YAClC,CAAC;YACD,OAAO,oBAAoB,CAAC;QAChC,CAAC,CAAC;QACF,QAAQ,iBAAE,CAAC,QAAQ,EAAE,EAAE,CAAC;YACpB,KAAK,QAAQ;gBACT,OAAO,8DAA8D,CAAC;YAC1E,KAAK,OAAO;gBACR,OAAO,YAAY,EAAE,CAAC;YAC1B;gBACI,OAAO,wBAAwB,CAAC;QACxC,CAAC;IACL,CAAC;IAES,yBAAyB,CAAC,QAAiB;QACjD,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,6CAA6C,QAAQ,GAAG,CAAC,CAAC;QAC9E,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE9B,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;YAC/B,IAAI,GAAG,CAAC,QAAQ,KAAK,OAAO,IAAI,GAAG,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACxD,MAAM,IAAI,KAAK,CAAC,kFAAkF,CAAC,CAAC;YACxG,CAAC;QACL,CAAC;IACL,CAAC;;AA9KL,0CA+KC;AA/JoB;;;;WAAe;QAC5B,QAAQ,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG;QAChC,SAAS,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;QAC9B,iBAAiB,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;QACtC,eAAe,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;QACpC,sBAAsB,EAAE,YAAE,CAAC,QAAQ,CAAC,OAAO;QAC3C,WAAW,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;QAC/B,aAAa,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;QACjC,SAAS,EAAE,YAAE,CAAC,QAAQ,CAAC,MAAM;KAChC;EAT4B,CAS3B"}