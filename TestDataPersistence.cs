// 数据持久化功能测试程序
using System;
using System.Collections.Generic;
using BatchBrowserUI.Models;

namespace TestDataPersistence
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("🧪 开始测试数据持久化功能...");
            
            try
            {
                // 创建数据持久化管理器
                var dataPersistenceManager = new DataPersistenceManager();
                Console.WriteLine("✅ 数据持久化管理器创建成功");
                
                // 测试加载现有数据
                Console.WriteLine("\n📂 测试加载现有数据...");
                var loadedInstances = dataPersistenceManager.LoadBrowserInstances();
                Console.WriteLine($"✅ 成功加载 {loadedInstances.Count} 个浏览器实例");
                
                foreach (var instance in loadedInstances)
                {
                    Console.WriteLine($"   - ID: {instance.Id}");
                    Console.WriteLine($"   - 状态: {instance.Status}");
                    Console.WriteLine($"   - 代理: {instance.ProxyServer ?? "无代理"}");
                    Console.WriteLine($"   - URL: {instance.Url}");
                    Console.WriteLine($"   - 创建时间: {instance.CreatedTime:yyyy-MM-dd HH:mm:ss}");
                    Console.WriteLine($"   - 指纹: {instance.Fingerprint}");
                    Console.WriteLine();
                }
                
                // 测试创建新实例
                Console.WriteLine("🆕 测试创建新实例...");
                var newInstance = new BrowserInstanceModel
                {
                    Id = $"test-{DateTime.Now.Ticks}",
                    Status = "已停止",
                    ProxyServer = "127.0.0.1:9999",
                    Url = "https://test.example.com",
                    CreatedTime = DateTime.Now,
                    LastActiveTime = DateTime.Now,
                    MemoryUsage = 100000000,
                    Fingerprint = "测试指纹",
                    IsActive = false,
                    RequestCount = 0
                };
                
                loadedInstances.Add(newInstance);
                Console.WriteLine($"✅ 创建新实例: {newInstance.Id}");
                
                // 测试保存数据
                Console.WriteLine("\n💾 测试保存数据...");
                var saveSuccess = dataPersistenceManager.SaveBrowserInstances(loadedInstances);
                
                if (saveSuccess)
                {
                    Console.WriteLine("✅ 数据保存成功");
                }
                else
                {
                    Console.WriteLine("❌ 数据保存失败");
                }
                
                // 测试重新加载验证
                Console.WriteLine("\n🔄 测试重新加载验证...");
                var reloadedInstances = dataPersistenceManager.LoadBrowserInstances();
                Console.WriteLine($"✅ 重新加载 {reloadedInstances.Count} 个浏览器实例");
                
                // 验证新实例是否存在
                var foundNewInstance = reloadedInstances.Find(i => i.Id == newInstance.Id);
                if (foundNewInstance != null)
                {
                    Console.WriteLine($"✅ 新实例验证成功: {foundNewInstance.Id}");
                }
                else
                {
                    Console.WriteLine("❌ 新实例验证失败");
                }
                
                Console.WriteLine("\n🎉 数据持久化功能测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
