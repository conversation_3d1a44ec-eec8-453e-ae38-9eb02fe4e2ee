﻿using System.Configuration;
using System.Data;
using System.Windows;
using System;

namespace BatchBrowserUI;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{    // 重写启动方法以捕获未处理的异常
    protected override void OnStartup(StartupEventArgs e)
    {
        try
        {
            Console.WriteLine("🚀 应用程序启动中...");
            
            // 添加全局异常处理器
            this.DispatcherUnhandledException += App_DispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            
            // 调用基类启动方法以初始化应用程序
            base.OnStartup(e);            // 现在使用完整的MainWindow - XAML问题已修复
            Console.WriteLine("🪟 正在创建完整主窗口...");
            var mainWindow = new MainWindow();
            this.MainWindow = mainWindow;
            
            Console.WriteLine("🪟 正在显示完整主窗口...");
            mainWindow.Show();
            mainWindow.Activate();
            
            Console.WriteLine("✅ 完整应用程序启动完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 启动失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
            MessageBox.Show($"应用程序启动失败: {ex.Message}\n\n详细错误: {ex.StackTrace}", "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);
            this.Shutdown(); // 在错误情况下关闭应用程序
        }
    }

    // 处理UI线程未处理的异常
    private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        Console.WriteLine($"❌ UI线程异常: {e.Exception.Message}");
        Console.WriteLine($"详细错误: {e.Exception}");
        MessageBox.Show($"应用程序错误: {e.Exception.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        e.Handled = true; // 防止应用程序崩溃
    }

    // 处理非UI线程未处理的异常
    private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        Console.WriteLine($"❌ 非UI线程异常: {e.ExceptionObject}");
        if (e.ExceptionObject is Exception ex)
        {
            MessageBox.Show($"严重错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}

