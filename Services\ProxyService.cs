using System.Collections.Concurrent;
using System.Net.NetworkInformation;
using BatchBrowserEngine.Models;

namespace BatchBrowserEngine.Services
{
    /// <summary>
    /// 代理服务 - 管理代理池和智能轮换
    /// 功能说明：代理池管理、健康检查、故障转移、负载均衡
    /// </summary>
    public class ProxyService
    {
        private readonly ILogger<ProxyService> _logger;
        private readonly ConcurrentQueue<ProxyInfo> _proxyPool;
        private readonly ConcurrentDictionary<string, ProxyStatus> _proxyStatus;
        private readonly Timer _healthCheckTimer;
        private readonly string _proxyFilePath = "proxies.txt";
        
        // 代理轮换索引
        private int _currentIndex = 0;
        private readonly object _indexLock = new object();

        public ProxyService(ILogger<ProxyService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _proxyPool = new ConcurrentQueue<ProxyInfo>();
            _proxyStatus = new ConcurrentDictionary<string, ProxyStatus>();
            
            // 每5分钟进行一次健康检查
            _healthCheckTimer = new Timer(PerformHealthCheck, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(5));
        }

        /// <summary>
        /// 初始化代理池
        /// </summary>
        public async Task InitializeAsync()
        {
            await LoadProxiesFromFileAsync();
            await PerformInitialHealthCheckAsync();
        }

        /// <summary>
        /// 从文件加载代理配置
        /// </summary>
        private async Task LoadProxiesFromFileAsync()
        {
            try
            {
                if (!File.Exists(_proxyFilePath))
                {
                    _logger.LogWarning($"⚠️ 代理配置文件不存在: {_proxyFilePath}");
                    return;
                }

                var lines = await File.ReadAllLinesAsync(_proxyFilePath);
                var loadedCount = 0;

                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                        continue;

                    var proxy = ParseProxyLine(line.Trim());
                    if (proxy != null)
                    {
                        _proxyPool.Enqueue(proxy);
                        _proxyStatus.TryAdd(proxy.Address, new ProxyStatus 
                        { 
                            IsHealthy = true, 
                            LastChecked = DateTime.Now,
                            ResponseTime = TimeSpan.Zero
                        });
                        loadedCount++;
                    }
                }

                _logger.LogInformation($"📡 从文件加载 {loadedCount} 个代理服务器");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 加载代理配置文件失败");
            }
        }

        /// <summary>
        /// 解析代理配置行
        /// </summary>
        private ProxyInfo? ParseProxyLine(string line)
        {
            try
            {
                var parts = line.Split(':');
                if (parts.Length < 2)
                    return null;

                var proxy = new ProxyInfo
                {
                    Host = parts[0],
                    Port = int.Parse(parts[1]),
                    Type = "HTTP" // 默认类型
                };

                if (parts.Length >= 4)
                {
                    proxy.Username = parts[2];
                    proxy.Password = parts[3];
                }

                proxy.Address = string.IsNullOrEmpty(proxy.Username) 
                    ? $"{proxy.Host}:{proxy.Port}"
                    : $"{proxy.Username}:{proxy.Password}@{proxy.Host}:{proxy.Port}";

                return proxy;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"⚠️ 解析代理配置失败: {line}, 错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取下一个可用代理
        /// </summary>
        public async Task<ProxyInfo?> GetNextProxyAsync()
        {
            return await Task.Run(() =>
            {
                var healthyProxies = _proxyPool.Where(p => 
                    _proxyStatus.TryGetValue(p.Address, out var status) && status.IsHealthy)
                    .ToList();

                if (!healthyProxies.Any())
                {
                    _logger.LogWarning("⚠️ 没有可用的健康代理，返回直连");
                    return null;
                }

                lock (_indexLock)
                {
                    var proxy = healthyProxies[_currentIndex % healthyProxies.Count];
                    _currentIndex++;
                    
                    _logger.LogDebug($"🔄 分配代理: {proxy.Address}");
                    return proxy;
                }
            });
        }

        /// <summary>
        /// 获取代理池统计信息
        /// </summary>
        public ProxyPoolStats GetStats()
        {
            var totalProxies = _proxyPool.Count;
            var healthyProxies = _proxyStatus.Values.Count(s => s.IsHealthy);
            var unhealthyProxies = totalProxies - healthyProxies;

            return new ProxyPoolStats
            {
                TotalProxies = totalProxies,
                HealthyProxies = healthyProxies,
                UnhealthyProxies = unhealthyProxies,
                HealthRate = totalProxies > 0 ? (double)healthyProxies / totalProxies : 0
            };
        }

        /// <summary>
        /// 执行初始健康检查
        /// </summary>
        private async Task PerformInitialHealthCheckAsync()
        {
            _logger.LogInformation("🔍 开始代理健康检查...");
            
            var tasks = _proxyPool.Select(async proxy =>
            {
                var isHealthy = await CheckProxyHealthAsync(proxy);
                if (_proxyStatus.TryGetValue(proxy.Address, out var status))
                {
                    status.IsHealthy = isHealthy;
                    status.LastChecked = DateTime.Now;
                }
            });

            await Task.WhenAll(tasks);
            
            var stats = GetStats();
            _logger.LogInformation($"✅ 健康检查完成 - 健康: {stats.HealthyProxies}, 异常: {stats.UnhealthyProxies}");
        }

        /// <summary>
        /// 定期健康检查
        /// </summary>
        private async void PerformHealthCheck(object? state)
        {
            try
            {
                _logger.LogDebug("🔍 执行定期代理健康检查...");
                
                var tasks = _proxyPool.Select(async proxy =>
                {
                    var startTime = DateTime.Now;
                    var isHealthy = await CheckProxyHealthAsync(proxy);
                    var responseTime = DateTime.Now - startTime;
                    
                    if (_proxyStatus.TryGetValue(proxy.Address, out var status))
                    {
                        status.IsHealthy = isHealthy;
                        status.LastChecked = DateTime.Now;
                        status.ResponseTime = responseTime;
                        
                        if (!isHealthy)
                        {
                            status.FailureCount++;
                            _logger.LogWarning($"⚠️ 代理异常: {proxy.Address}, 失败次数: {status.FailureCount}");
                        }
                        else
                        {
                            status.FailureCount = 0; // 重置失败计数
                        }
                    }
                });

                await Task.WhenAll(tasks);
                
                var stats = GetStats();
                _logger.LogDebug($"🔍 健康检查完成 - 健康率: {stats.HealthRate:P2}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 代理健康检查失败");
            }
        }

        /// <summary>
        /// 检查单个代理的健康状态
        /// </summary>
        private async Task<bool> CheckProxyHealthAsync(ProxyInfo proxy)
        {
            try
            {
                // 简单的连接测试 - 尝试ping代理服务器
                using var ping = new Ping();
                var reply = await ping.SendPingAsync(proxy.Host, 5000);
                return reply.Status == IPStatus.Success;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 标记代理为不健康
        /// </summary>
        public void MarkProxyUnhealthy(string proxyAddress, string reason)
        {
            if (_proxyStatus.TryGetValue(proxyAddress, out var status))
            {
                status.IsHealthy = false;
                status.FailureCount++;
                status.LastFailureReason = reason;
                status.LastChecked = DateTime.Now;
                
                _logger.LogWarning($"⚠️ 标记代理不健康: {proxyAddress}, 原因: {reason}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _healthCheckTimer?.Dispose();
            _logger.LogInformation("🔄 代理服务已释放资源");
        }
    }

    /// <summary>
    /// 代理信息模型
    /// </summary>
    public class ProxyInfo
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; }
        public string Type { get; set; } = "HTTP";
        public string? Username { get; set; }
        public string? Password { get; set; }
        public string Address { get; set; } = string.Empty;
    }

    /// <summary>
    /// 代理状态模型
    /// </summary>
    public class ProxyStatus
    {
        public bool IsHealthy { get; set; } = true;
        public DateTime LastChecked { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public int FailureCount { get; set; }
        public string? LastFailureReason { get; set; }
    }

    /// <summary>
    /// 代理池统计信息
    /// </summary>
    public class ProxyPoolStats
    {
        public int TotalProxies { get; set; }
        public int HealthyProxies { get; set; }
        public int UnhealthyProxies { get; set; }
        public double HealthRate { get; set; }
    }
}
