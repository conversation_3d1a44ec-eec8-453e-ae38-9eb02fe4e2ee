<!-- 输入对话框XAML界面 -->
<!-- 功能说明：提供用户输入数字的简单对话框 -->

<Window x:Class="BatchBrowserUI.Views.InputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="输入数据" Height="200" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 提示文本 -->
        <TextBlock Grid.Row="0" 
                   Text="{Binding PromptText}" 
                   TextWrapping="Wrap"
                   Margin="0,0,0,15"
                   FontSize="14"/>
        
        <!-- 输入框 -->
        <TextBox Grid.Row="1" 
                 Name="InputTextBox"
                 Text="{Binding InputValue, UpdateSourceTrigger=PropertyChanged}"
                 FontSize="14"
                 Padding="8"
                 Margin="0,0,0,15"/>
        
        <!-- 按钮面板 -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button Name="OkButton" 
                    Content="确定" 
                    Width="80" 
                    Height="35"
                    Margin="0,0,10,0"
                    Click="OkButton_Click"
                    IsDefault="True"/>
            <Button Name="CancelButton" 
                    Content="取消" 
                    Width="80" 
                    Height="35"
                    Click="CancelButton_Click"
                    IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
