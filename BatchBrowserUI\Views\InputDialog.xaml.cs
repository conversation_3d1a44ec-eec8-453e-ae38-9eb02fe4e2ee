// 输入对话框后端代码
// 功能说明：提供用户输入功能的简单对话框

using System.ComponentModel;
using System.Windows;

namespace BatchBrowserUI.Views
{
    /// <summary>
    /// 输入对话框 - 用于获取用户输入
    /// 功能：提供简单的文本输入界面，替代VB.NET的InputBox
    /// </summary>
    public partial class InputDialog : Window, INotifyPropertyChanged
    {
        private string _promptText;     // 提示文本
        private string _inputValue;     // 输入值

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="prompt">提示文本</param>
        /// <param name="title">窗口标题</param>
        /// <param name="defaultValue">默认值</param>
        public InputDialog(string prompt, string title = "输入", string defaultValue = "")
        {
            InitializeComponent();
            DataContext = this;
            
            // 设置属性值
            PromptText = prompt;
            Title = title;
            InputValue = defaultValue;
            
            // 设置输入框焦点
            Loaded += (s, e) => InputTextBox.Focus();
        }

        /// <summary>
        /// 提示文本
        /// </summary>
        public string PromptText
        {
            get => _promptText;
            set
            {
                _promptText = value;
                OnPropertyChanged(nameof(PromptText));
            }
        }

        /// <summary>
        /// 输入值
        /// </summary>
        public string InputValue
        {
            get => _inputValue;
            set
            {
                _inputValue = value;
                OnPropertyChanged(nameof(InputValue));
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 显示输入对话框
        /// </summary>
        /// <param name="owner">父窗口</param>
        /// <param name="prompt">提示文本</param>
        /// <param name="title">窗口标题</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>用户输入的值，如果取消则返回null</returns>
        public static string ShowDialog(Window owner, string prompt, string title = "输入", string defaultValue = "")
        {
            var dialog = new InputDialog(prompt, title, defaultValue)
            {
                Owner = owner
            };

            return dialog.ShowDialog() == true ? dialog.InputValue : null;
        }

        /// <summary>
        /// 属性变更通知事件
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 触发属性变更通知
        /// </summary>
        /// <param name="propertyName">变更的属性名称</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
