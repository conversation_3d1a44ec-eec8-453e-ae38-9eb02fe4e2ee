{"version": 3, "file": "browser-plugin.d.ts", "sourceRoot": "", "sources": ["../../src/abstract-classes/browser-plugin.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAC9C,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAGjD,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,mBAAmB,CAAC;AAC9D,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAClD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAC9C,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AAE9D;;;;;;;;;GASG;AACH,eAAO,MAAM,kBAAkB,0HAC4F,CAAC;AAE5H;;;;;;GAMG;AACH,MAAM,WAAW,aAAa;IAC1B,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;IAClD,IAAI,CAAC,EAAE,MAAM,MAAM,CAAC;CACvB;AAED,gBAAgB;AAChB,MAAM,WAAW,aAAa;IAC1B,OAAO,CAAC,GAAG,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;CACpD;AAED,gBAAgB;AAChB,MAAM,WAAW,UAAU;IACvB,KAAK,CAAC,GAAG,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;IAC5C,GAAG,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;CACnC;AAED,MAAM,WAAW,oBAAoB,CAAC,cAAc;IAChD;;;;;OAKG;IACH,aAAa,CAAC,EAAE,cAAc,CAAC;IAC/B;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;;;;OAKG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B;;;;OAIG;IACH,sBAAsB,CAAC,EAAE,OAAO,CAAC;IACjC;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;;;;OAKG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;CAC7B;AAED,MAAM,WAAW,0BAA0B,CACvC,OAAO,SAAS,aAAa,EAC7B,cAAc,SAAS,UAAU,GAAG,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAChF,YAAY,SAAS,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EACjF,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EACvD,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CACpE,SAAQ,OAAO,CACT,IAAI,CACA,oBAAoB,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC,EAC1F,eAAe,CAClB,CACJ;CAAG;AAER;;;;;GAKG;AACH,8BAAsB,aAAa,CAC/B,OAAO,SAAS,aAAa,GAAG,aAAa,EAC7C,cAAc,SAAS,UAAU,GAAG,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAChF,YAAY,SAAS,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EACjF,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EACvD,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;IAElE,IAAI,SAAyB;IAE7B,OAAO,EAAE,OAAO,CAAC;IAEjB,aAAa,EAAE,cAAc,CAAC;IAE9B,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB,iBAAiB,EAAE,OAAO,CAAC;IAE3B,sBAAsB,EAAE,OAAO,CAAC;IAEhC,eAAe,CAAC,EAAE,OAAO,CAAC;gBAEd,OAAO,EAAE,OAAO,EAAE,OAAO,GAAE,oBAAoB,CAAC,cAAc,CAAM;IAmBhF;;;;;OAKG;IACH,mBAAmB,CACf,OAAO,GAAE,0BAA0B,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAM,GAC/G,aAAa,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC;IAyBtF,gBAAgB,IAAI,iBAAiB,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC;IAI3G;;OAEG;IACG,MAAM,CACR,aAAa,GAAE,aAAa,CACxB,OAAO,EACP,cAAc,EACd,YAAY,EACZ,cAAc,EACd,aAAa,CACa,GAC/B,OAAO,CAAC,YAAY,CAAC;IAwBxB,OAAO,CAAC,yBAAyB;IAgBjC,SAAS,CAAC,0BAA0B,CAChC,KAAK,EAAE,OAAO,EACd,cAAc,EAAE,MAAM,GAAG,SAAS,EAClC,WAAW,EAAE,MAAM,EACnB,oBAAoB,EAAE,MAAM,GAC7B,KAAK;IAuBR;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,wBAAwB,CACvC,aAAa,EAAE,aAAa,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC,GACnG,OAAO,CAAC,IAAI,CAAC;IAEhB,SAAS,CAAC,QAAQ,CAAC,uBAAuB,CACtC,aAAa,EAAE,aAAa,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC,GACnG,OAAO;IAEV;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,OAAO,CACtB,aAAa,EAAE,aAAa,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC,GACnG,OAAO,CAAC,YAAY,CAAC;IAExB;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,iBAAiB,IAAI,iBAAiB,CACrD,OAAO,EACP,cAAc,EACd,YAAY,EACZ,cAAc,EACd,aAAa,CAChB;CACJ;AAED,qBAAa,kBAAmB,SAAQ,aAAa;gBAC9B,GAAG,IAAI,EAAE,qBAAqB,CAAC,OAAO,aAAa,CAAC;CAgB1E"}