// 系统性能监控UI数据模型
// 功能说明：用于UI数据绑定的系统性能监控信息模型

using System;
using System.ComponentModel;

namespace BatchBrowserUI.Models
{
    /// <summary>
    /// 系统性能监控UI模型 - 用于实时显示系统状态
    /// 功能：展示CPU、内存、运行时间等性能指标
    /// </summary>
    public class SystemMonitorModel : INotifyPropertyChanged
    {        private double _cpuUsage;               // CPU使用率 (%)
        private long _memoryUsage;              // 内存使用量 (MB)
        private long _totalMemory;              // 总内存量 (MB)
        private TimeSpan _uptime;               // 运行时间
        private int _activeInstances;           // 活跃浏览器实例数量
        private int _totalRequests;             // 总请求数量
        private int _successfulRequests;        // 成功请求数量
        private double _successRate;            // 成功率 (%)
        private int _errorCount;                // 错误数量
        private string _engineStatus = string.Empty;           // 引擎状态
        private int _totalBrowsers;             // 总浏览器数量
        private int _activeBrowsers;            // 活跃浏览器数量
        private int _proxyCount;                // 代理数量
        private DateTime _lastUpdated;          // 最后更新时间
        private bool _isEngineRunning;          // 引擎是否运行中

        /// <summary>
        /// CPU使用率百分比
        /// </summary>
        public double CpuUsage
        {
            get => _cpuUsage;
            set
            {
                _cpuUsage = value;
                OnPropertyChanged(nameof(CpuUsage));
                OnPropertyChanged(nameof(CpuUsageString));
            }
        }

        /// <summary>
        /// CPU使用率的字符串表示 - 用于UI显示
        /// </summary>
        public string CpuUsageString
        {
            get
            {
                try
                {
                    return $"{CpuUsage:F1}%";
                }
                catch
                {
                    return "0.0%";
                }
            }
        }

        /// <summary>
        /// 内存使用量 (MB)
        /// </summary>
        public long MemoryUsage
        {
            get => _memoryUsage;
            set
            {
                _memoryUsage = value;
                OnPropertyChanged(nameof(MemoryUsage));
                OnPropertyChanged(nameof(MemoryUsageString));
                OnPropertyChanged(nameof(MemoryPercentage));
            }
        }

        /// <summary>
        /// 总内存量 (MB)
        /// </summary>
        public long TotalMemory
        {
            get => _totalMemory == 0 ? 8192 : _totalMemory; // 默认8GB内存
            set
            {
                _totalMemory = value;
                OnPropertyChanged(nameof(TotalMemory));
                OnPropertyChanged(nameof(MemoryUsageString));
                OnPropertyChanged(nameof(MemoryPercentage));
            }
        }

        /// <summary>
        /// 内存使用情况的字符串表示 - 用于UI显示
        /// </summary>
        public string MemoryUsageString
        {
            get
            {
                try
                {
                    return $"{MemoryUsage:N0} MB / {TotalMemory:N0} MB";
                }
                catch
                {
                    return "0 MB / 0 MB";
                }
            }
        }

        /// <summary>
        /// 内存使用百分比
        /// </summary>
        public double MemoryPercentage
        {
            get
            {
                try
                {
                    if (TotalMemory <= 0) return 0;
                    var percentage = (double)MemoryUsage / TotalMemory * 100;
                    return Math.Min(100, Math.Max(0, percentage)); // 确保在0-100范围内
                }
                catch
                {
                    return 0;
                }
            }
        }

        /// <summary>
        /// 系统运行时间
        /// </summary>
        public TimeSpan Uptime
        {
            get => _uptime;
            set
            {
                _uptime = value;
                OnPropertyChanged(nameof(Uptime));
                OnPropertyChanged(nameof(UptimeString));
            }
        }

        /// <summary>
        /// 运行时间的字符串表示 - 用于UI显示
        /// </summary>
        public string UptimeString
        {
            get
            {
                try
                {
                    return $"{Uptime.Days}天 {Uptime.Hours:D2}:{Uptime.Minutes:D2}:{Uptime.Seconds:D2}";
                }
                catch
                {
                    return "0天 00:00:00";
                }
            }
        }

        /// <summary>
        /// 当前活跃的浏览器实例数量
        /// </summary>
        public int ActiveInstances
        {
            get => _activeInstances;
            set
            {
                _activeInstances = value;
                OnPropertyChanged(nameof(ActiveInstances));
            }
        }

        /// <summary>
        /// 总请求数量
        /// </summary>
        public int TotalRequests
        {
            get => _totalRequests;
            set
            {
                _totalRequests = value;
                OnPropertyChanged(nameof(TotalRequests));
                OnPropertyChanged(nameof(SuccessRate));
            }
        }

        /// <summary>
        /// 成功请求数量
        /// </summary>
        public int SuccessfulRequests
        {
            get => _successfulRequests;
            set
            {
                _successfulRequests = value;
                OnPropertyChanged(nameof(SuccessfulRequests));
                OnPropertyChanged(nameof(SuccessRate));
            }
        }

        /// <summary>
        /// 请求成功率百分比
        /// </summary>
        public double SuccessRate
        {
            get
            {
                try
                {
                    if (TotalRequests <= 0) return 100; // 没有请求时显示100%
                    var rate = (double)SuccessfulRequests / TotalRequests * 100;
                    return Math.Min(100, Math.Max(0, rate)); // 确保在0-100范围内
                }
                catch
                {
                    return 100;
                }
            }
        }

        /// <summary>
        /// 成功率的字符串表示 - 用于UI显示
        /// </summary>
        public string SuccessRateString
        {
            get
            {
                try
                {
                    return $"{SuccessRate:F1}%";
                }
                catch
                {
                    return "100.0%";
                }
            }
        }

        /// <summary>
        /// 错误数量
        /// </summary>
        public int ErrorCount
        {
            get => _errorCount;
            set
            {
                _errorCount = value;
                OnPropertyChanged(nameof(ErrorCount));
            }
        }        /// <summary>
        /// 引擎状态 (运行中/停止/错误)
        /// </summary>
        public string EngineStatus
        {
            get => _engineStatus;
            set
            {
                _engineStatus = value;
                OnPropertyChanged(nameof(EngineStatus));
            }
        }

        /// <summary>
        /// 总浏览器数量
        /// </summary>
        public int TotalBrowsers
        {
            get => _totalBrowsers;
            set
            {
                _totalBrowsers = value;
                OnPropertyChanged(nameof(TotalBrowsers));
            }
        }

        /// <summary>
        /// 活跃浏览器数量
        /// </summary>
        public int ActiveBrowsers
        {
            get => _activeBrowsers;
            set
            {
                _activeBrowsers = value;
                OnPropertyChanged(nameof(ActiveBrowsers));
            }
        }

        /// <summary>
        /// 代理服务器数量
        /// </summary>
        public int ProxyCount
        {
            get => _proxyCount;
            set
            {
                _proxyCount = value;
                OnPropertyChanged(nameof(ProxyCount));
            }
        }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated
        {
            get => _lastUpdated;
            set
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }

        /// <summary>
        /// 引擎是否运行中
        /// </summary>
        public bool IsEngineRunning
        {
            get => _isEngineRunning;
            set
            {
                _isEngineRunning = value;
                OnPropertyChanged(nameof(IsEngineRunning));
            }
        }

        /// <summary>
        /// 属性变更通知事件 - 支持数据绑定
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 触发属性变更通知
        /// </summary>
        /// <param name="propertyName">变更的属性名称</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
