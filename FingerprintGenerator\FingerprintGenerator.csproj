<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>BatchBrowser.FingerprintGenerator</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>BatchBrowser Team</Authors>
    <Description>浏览器指纹生成器模块 - 独立DLL</Description>
  </PropertyGroup>

  <ItemGroup>
    <!-- JSON处理 -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    
    <!-- 日志 -->
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
  </ItemGroup>

</Project>
