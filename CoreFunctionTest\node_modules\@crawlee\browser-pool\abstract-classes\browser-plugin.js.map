{"version": 3, "file": "browser-plugin.js", "sourceRoot": "", "sources": ["../../src/abstract-classes/browser-plugin.ts"], "names": [], "mappings": ";;;;AAAA,wCAA8C;AAE9C,wEAAiC;AAGjC,sDAAkD;AAIlD;;;;;;;;;GASG;AACU,QAAA,kBAAkB,GAC3B,uHAAuH,CAAC;AA+E5H;;;;;GAKG;AACH,MAAsB,aAAa;IAuB/B,YAAY,OAAgB,EAAE,UAAgD,EAAE;QAhBhF;;;;mBAAO,IAAI,CAAC,WAAW,CAAC,IAAI;WAAC;QAE7B;;;;;WAAiB;QAEjB;;;;;WAA8B;QAE9B;;;;;WAAkB;QAElB;;;;;WAAqB;QAErB;;;;;WAA2B;QAE3B;;;;;WAAgC;QAEhC;;;;;WAA0B;QAGtB,MAAM,EACF,aAAa,GAAG,EAAoB,EACpC,QAAQ,EACR,WAAW,EACX,iBAAiB,GAAG,KAAK,EACzB,sBAAsB,GAAG,KAAK,EAC9B,eAAe,GAAG,KAAK,GAC1B,GAAG,OAAO,CAAC;QAEZ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACH,mBAAmB,CACf,UAA4G,EAAE;QAE9G,MAAM,EACF,EAAE,EACF,aAAa,GAAG,EAAE,EAClB,QAAQ,GAAG,IAAI,CAAC,QAAQ,EACxB,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,EAC1C,WAAW,GAAG,IAAI,CAAC,WAAW,EAC9B,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,EACpD,eAAe,GAAG,IAAI,CAAC,eAAe,EACtC,SAAS,GACZ,GAAG,OAAO,CAAC;QAEZ,OAAO,IAAI,8BAAa,CAAC;YACrB,EAAE;YACF,aAAa,EAAE,IAAA,sBAAK,EAAC,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC;YAC3D,aAAa,EAAE,IAAI;YACnB,QAAQ;YACR,iBAAiB;YACjB,sBAAsB;YACtB,WAAW;YACX,eAAe;YACf,SAAS;SACZ,CAAC,CAAC;IACP,CAAC;IAED,gBAAgB;QACZ,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,gBAMI,IAAI,CAAC,mBAAmB,EAAE;QAE9B,aAAa,CAAC,aAAa,KAA3B,aAAa,CAAC,aAAa,GAAK,EAAoB,EAAC;QAErD,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,aAAa,CAAC;QAElD,IAAI,QAAQ,EAAE,CAAC;YACX,MAAM,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC9C,4EAA4E;YAC3E,aAA4B,CAAC,IAAI,GAAG,IAAI,CAAC,yBAAyB,CAAC,aAAc,CAAC,IAAI,CAAC,CAAC;YACzF,yEAAyE;YACzE,wEAAwE;YACxE,iEAAiE;YACjE,MAAM,SAAS,GAAG,aAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC;YAC5F,IAAI,aAAc,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC;gBACtE,aAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,0BAAkB,EAAE,CAAC,CAAC;YACnE,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IACvC,CAAC;IAEO,yBAAyB,CAAC,YAAuB;QACrD,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC;YACxB,OAAO,CAAC,+CAA+C,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,aAAa,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,2BAA2B,CAAC,CAAC,CAAC;QAE3G,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC;YACvB,YAAY,CAAC,aAAa,CAAC,IAAI,uBAAuB,CAAC;QAC3D,CAAC;aAAM,CAAC;YACJ,YAAY,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAES,0BAA0B,CAChC,KAAc,EACd,cAAkC,EAClC,WAAmB,EACnB,oBAA4B;QAE5B,MAAM,YAAY,GAAG,CAAC,uDAAuD,CAAC,CAAC;QAE/E,IAAI,cAAc,EAAE,CAAC;YACjB,YAAY,CAAC,IAAI,CAAC,iDAAiD,cAAc,eAAe,CAAC,CAAC;QACtG,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;YAC/B,YAAY,CAAC,IAAI,CAAC,uCAAuC,WAAW,GAAG,CAAC,CAAC;QAC7E,CAAC;QAED,YAAY,CAAC,IAAI,CAAC,KAAK,oBAAoB,EAAE,CAAC,CAAC;QAE/C,YAAY,CAAC,IAAI,CACb,EAAE,EACF,uHAAuH,EACvH,EAAE,CACL,CAAC;QAEF,oFAAoF;QACpF,MAAM,IAAI,kBAAkB,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;IAChF,CAAC;CA8BJ;AA1LD,sCA0LC;AAED,MAAa,kBAAmB,SAAQ,oBAAa;IACjD,YAAmB,GAAG,IAAiD;QACnE,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;QAEjC,MAAM,CAAC,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAE/D,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;YACjC,GAAG,EAAE,GAAG,EAAE;gBACN,IAAI,IAAI,CAAC,KAAK,YAAY,KAAK,EAAE,CAAC;oBAC9B,OAAO,GAAG,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,uBAAuB,QAAQ,EAAE,CAAC;gBACjF,CAAC;gBAED,OAAO,GAAG,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YAC1C,CAAC;SACJ,CAAC,CAAC;IACP,CAAC;CACJ;AAjBD,gDAiBC"}