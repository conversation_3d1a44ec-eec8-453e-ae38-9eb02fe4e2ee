using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Microsoft.Data.Sqlite;
using System.Text;

namespace DataPersistence
{
    /// <summary>
    /// 数据管理器 - 独立DLL模块
    /// 功能说明：提供数据持久化、配置管理、数据库操作等功能
    /// </summary>
    public class DataManager : IDisposable
    {
        private readonly ILogger<DataManager>? _logger;
        private readonly string _dataDirectory;
        private readonly string _databasePath;
        private SqliteConnection? _connection;
        
        // JSON序列化设置
        private readonly JsonSerializerSettings _jsonSettings;

        public DataManager(string? dataDirectory = null, ILogger<DataManager>? logger = null)
        {
            _logger = logger;
            _dataDirectory = dataDirectory ?? Path.Combine(Environment.CurrentDirectory, "data");
            _databasePath = Path.Combine(_dataDirectory, "browser_data.db");
            
            _jsonSettings = new JsonSerializerSettings
            {
                Formatting = Formatting.Indented,
                NullValueHandling = NullValueHandling.Ignore,
                DateFormatHandling = DateFormatHandling.IsoDateFormat
            };
            
            // 确保数据目录存在
            EnsureDataDirectory();
        }

        /// <summary>
        /// 初始化数据管理器
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger?.LogInformation("💾 正在初始化数据管理器...");
                
                // 初始化数据库
                await InitializeDatabaseAsync();
                
                _logger?.LogInformation("✅ 数据管理器初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 数据管理器初始化失败");
                return false;
            }
        }

        #region JSON文件操作

        /// <summary>
        /// 保存对象到JSON文件
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="data">数据对象</param>
        /// <param name="fileName">文件名</param>
        /// <returns>保存结果</returns>
        public async Task<SaveResult> SaveToJsonAsync<T>(T data, string fileName)
        {
            try
            {
                var filePath = Path.Combine(_dataDirectory, fileName);
                var jsonString = JsonConvert.SerializeObject(data, _jsonSettings);
                
                await File.WriteAllTextAsync(filePath, jsonString, Encoding.UTF8);
                
                _logger?.LogInformation($"💾 JSON文件保存成功: {fileName}");
                
                return new SaveResult
                {
                    Success = true,
                    Message = "保存成功",
                    FilePath = filePath
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ JSON文件保存失败: {fileName}");
                return new SaveResult
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 从JSON文件加载对象
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="fileName">文件名</param>
        /// <returns>加载结果</returns>
        public async Task<LoadResult<T>> LoadFromJsonAsync<T>(string fileName) where T : class
        {
            try
            {
                var filePath = Path.Combine(_dataDirectory, fileName);
                
                if (!File.Exists(filePath))
                {
                    return new LoadResult<T>
                    {
                        Success = false,
                        Message = "文件不存在"
                    };
                }

                var jsonString = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
                var data = JsonConvert.DeserializeObject<T>(jsonString, _jsonSettings);
                
                _logger?.LogInformation($"📂 JSON文件加载成功: {fileName}");
                
                return new LoadResult<T>
                {
                    Success = true,
                    Message = "加载成功",
                    Data = data,
                    FilePath = filePath
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ JSON文件加载失败: {fileName}");
                return new LoadResult<T>
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 备份JSON文件
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>备份结果</returns>
        public async Task<BackupResult> BackupJsonFileAsync(string fileName)
        {
            try
            {
                var sourceFile = Path.Combine(_dataDirectory, fileName);
                if (!File.Exists(sourceFile))
                {
                    return new BackupResult
                    {
                        Success = false,
                        Message = "源文件不存在"
                    };
                }

                var backupDir = Path.Combine(_dataDirectory, "backups");
                if (!Directory.Exists(backupDir))
                {
                    Directory.CreateDirectory(backupDir);
                }

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"{Path.GetFileNameWithoutExtension(fileName)}_{timestamp}.json";
                var backupFile = Path.Combine(backupDir, backupFileName);

                await File.CopyAsync(sourceFile, backupFile);
                
                _logger?.LogInformation($"💾 文件备份成功: {fileName} -> {backupFileName}");
                
                return new BackupResult
                {
                    Success = true,
                    Message = "备份成功",
                    BackupPath = backupFile
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 文件备份失败: {fileName}");
                return new BackupResult
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        #endregion

        #region 数据库操作

        /// <summary>
        /// 执行SQL查询
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <param name="parameters">参数</param>
        /// <returns>查询结果</returns>
        public async Task<QueryResult> ExecuteQueryAsync(string sql, Dictionary<string, object>? parameters = null)
        {
            try
            {
                await EnsureConnectionAsync();
                
                using var command = _connection!.CreateCommand();
                command.CommandText = sql;
                
                // 添加参数
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        command.Parameters.AddWithValue($"@{param.Key}", param.Value);
                    }
                }

                using var reader = await command.ExecuteReaderAsync();
                var results = new List<Dictionary<string, object>>();
                
                while (await reader.ReadAsync())
                {
                    var row = new Dictionary<string, object>();
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        row[reader.GetName(i)] = reader.GetValue(i);
                    }
                    results.Add(row);
                }
                
                _logger?.LogDebug($"📊 SQL查询成功，返回 {results.Count} 行");
                
                return new QueryResult
                {
                    Success = true,
                    Data = results,
                    RowCount = results.Count
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ SQL查询失败: {sql}");
                return new QueryResult
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 执行SQL命令
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <param name="parameters">参数</param>
        /// <returns>执行结果</returns>
        public async Task<ExecuteResult> ExecuteCommandAsync(string sql, Dictionary<string, object>? parameters = null)
        {
            try
            {
                await EnsureConnectionAsync();
                
                using var command = _connection!.CreateCommand();
                command.CommandText = sql;
                
                // 添加参数
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        command.Parameters.AddWithValue($"@{param.Key}", param.Value);
                    }
                }

                var affectedRows = await command.ExecuteNonQueryAsync();
                
                _logger?.LogDebug($"📊 SQL命令执行成功，影响 {affectedRows} 行");
                
                return new ExecuteResult
                {
                    Success = true,
                    AffectedRows = affectedRows,
                    Message = "执行成功"
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ SQL命令执行失败: {sql}");
                return new ExecuteResult
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 保存实体到数据库
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="entity">实体对象</param>
        /// <param name="tableName">表名</param>
        /// <returns>保存结果</returns>
        public async Task<SaveResult> SaveEntityAsync<T>(T entity, string tableName) where T : class
        {
            try
            {
                var json = JsonConvert.SerializeObject(entity, _jsonSettings);
                var id = GetEntityId(entity);
                
                var sql = @"
                    INSERT OR REPLACE INTO entities (id, table_name, data, created_at, updated_at)
                    VALUES (@id, @table_name, @data, @created_at, @updated_at)";
                
                var parameters = new Dictionary<string, object>
                {
                    ["id"] = id ?? Guid.NewGuid().ToString(),
                    ["table_name"] = tableName,
                    ["data"] = json,
                    ["created_at"] = DateTime.Now,
                    ["updated_at"] = DateTime.Now
                };

                var result = await ExecuteCommandAsync(sql, parameters);
                
                if (result.Success)
                {
                    _logger?.LogInformation($"💾 实体保存成功: {tableName}");
                    return new SaveResult
                    {
                        Success = true,
                        Message = "保存成功"
                    };
                }
                else
                {
                    return new SaveResult
                    {
                        Success = false,
                        Message = result.Message
                    };
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 实体保存失败: {tableName}");
                return new SaveResult
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 从数据库加载实体
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="tableName">表名</param>
        /// <returns>加载结果</returns>
        public async Task<LoadResult<List<T>>> LoadEntitiesAsync<T>(string tableName) where T : class
        {
            try
            {
                var sql = "SELECT data FROM entities WHERE table_name = @table_name ORDER BY updated_at DESC";
                var parameters = new Dictionary<string, object>
                {
                    ["table_name"] = tableName
                };

                var queryResult = await ExecuteQueryAsync(sql, parameters);
                
                if (!queryResult.Success)
                {
                    return new LoadResult<List<T>>
                    {
                        Success = false,
                        Message = queryResult.Message
                    };
                }

                var entities = new List<T>();
                foreach (var row in queryResult.Data)
                {
                    if (row.TryGetValue("data", out var jsonData) && jsonData is string json)
                    {
                        var entity = JsonConvert.DeserializeObject<T>(json, _jsonSettings);
                        if (entity != null)
                        {
                            entities.Add(entity);
                        }
                    }
                }
                
                _logger?.LogInformation($"📂 实体加载成功: {tableName}, 数量: {entities.Count}");
                
                return new LoadResult<List<T>>
                {
                    Success = true,
                    Message = "加载成功",
                    Data = entities
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 实体加载失败: {tableName}");
                return new LoadResult<List<T>>
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 保存配置
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        /// <returns>保存结果</returns>
        public async Task<bool> SaveConfigAsync(string key, object value)
        {
            try
            {
                var json = JsonConvert.SerializeObject(value, _jsonSettings);
                var sql = @"
                    INSERT OR REPLACE INTO configs (key, value, updated_at)
                    VALUES (@key, @value, @updated_at)";
                
                var parameters = new Dictionary<string, object>
                {
                    ["key"] = key,
                    ["value"] = json,
                    ["updated_at"] = DateTime.Now
                };

                var result = await ExecuteCommandAsync(sql, parameters);
                
                _logger?.LogInformation($"⚙️ 配置保存成功: {key}");
                return result.Success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 配置保存失败: {key}");
                return false;
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        /// <typeparam name="T">配置类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public async Task<T?> LoadConfigAsync<T>(string key, T? defaultValue = default)
        {
            try
            {
                var sql = "SELECT value FROM configs WHERE key = @key";
                var parameters = new Dictionary<string, object>
                {
                    ["key"] = key
                };

                var result = await ExecuteQueryAsync(sql, parameters);
                
                if (result.Success && result.Data.Any())
                {
                    var row = result.Data.First();
                    if (row.TryGetValue("value", out var jsonData) && jsonData is string json)
                    {
                        var value = JsonConvert.DeserializeObject<T>(json, _jsonSettings);
                        _logger?.LogInformation($"⚙️ 配置加载成功: {key}");
                        return value;
                    }
                }
                
                _logger?.LogInformation($"⚙️ 配置不存在，使用默认值: {key}");
                return defaultValue;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 配置加载失败: {key}");
                return defaultValue;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 确保数据目录存在
        /// </summary>
        private void EnsureDataDirectory()
        {
            if (!Directory.Exists(_dataDirectory))
            {
                Directory.CreateDirectory(_dataDirectory);
                _logger?.LogInformation($"📁 创建数据目录: {_dataDirectory}");
            }
        }

        /// <summary>
        /// 初始化数据库
        /// </summary>
        private async Task InitializeDatabaseAsync()
        {
            await EnsureConnectionAsync();
            
            // 创建表结构
            var createTables = @"
                CREATE TABLE IF NOT EXISTS entities (
                    id TEXT PRIMARY KEY,
                    table_name TEXT NOT NULL,
                    data TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                );

                CREATE TABLE IF NOT EXISTS configs (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                );

                CREATE INDEX IF NOT EXISTS idx_entities_table_name ON entities(table_name);
                CREATE INDEX IF NOT EXISTS idx_entities_updated_at ON entities(updated_at);
            ";

            await ExecuteCommandAsync(createTables);
            _logger?.LogInformation("📊 数据库表结构初始化完成");
        }

        /// <summary>
        /// 确保数据库连接
        /// </summary>
        private async Task EnsureConnectionAsync()
        {
            if (_connection == null)
            {
                _connection = new SqliteConnection($"Data Source={_databasePath}");
                await _connection.OpenAsync();
                _logger?.LogInformation($"📊 数据库连接已建立: {_databasePath}");
            }
        }

        /// <summary>
        /// 获取实体ID
        /// </summary>
        private string? GetEntityId<T>(T entity)
        {
            var idProperty = typeof(T).GetProperty("Id");
            return idProperty?.GetValue(entity)?.ToString();
        }

        #endregion

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _connection?.Close();
                _connection?.Dispose();
                
                _logger?.LogInformation("🔄 数据管理器已释放资源");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 释放资源时发生错误");
            }
        }
    }

    #region 数据模型

    /// <summary>
    /// 保存结果
    /// </summary>
    public class SaveResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? FilePath { get; set; }
    }

    /// <summary>
    /// 加载结果
    /// </summary>
    public class LoadResult<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public string? FilePath { get; set; }
    }

    /// <summary>
    /// 备份结果
    /// </summary>
    public class BackupResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? BackupPath { get; set; }
    }

    /// <summary>
    /// 查询结果
    /// </summary>
    public class QueryResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<Dictionary<string, object>> Data { get; set; } = new();
        public int RowCount { get; set; }
    }

    /// <summary>
    /// 执行结果
    /// </summary>
    public class ExecuteResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int AffectedRows { get; set; }
    }

    #endregion
}
