"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  TimeoutError: () => TimeoutError,
  addTimeoutToPromise: () => addTimeoutToPromise,
  storage: () => storage,
  tryCancel: () => tryCancel
});
module.exports = __toCommonJS(index_exports);
var import_node_async_hooks = require("async_hooks");
var storage = new import_node_async_hooks.AsyncLocalStorage();
var _TimeoutError = class _TimeoutError extends Error {
};
__name(_TimeoutError, "TimeoutError");
var TimeoutError = _TimeoutError;
var _InternalTimeoutError = class _InternalTimeoutError extends TimeoutError {
};
__name(_InternalTimeoutError, "InternalTimeoutError");
var InternalTimeoutError = _InternalTimeoutError;
function tryCancel() {
  const signal = storage.getStore()?.cancelTask.signal;
  if (signal?.aborted) {
    throw new InternalTimeoutError("Promise handler has been canceled due to a timeout");
  }
}
__name(tryCancel, "tryCancel");
async function addTimeoutToPromise(handler, timeoutMillis, errorMessage) {
  const context = storage.getStore() ?? {
    cancelTask: new AbortController()
  };
  let returnValue;
  const wrap = /* @__PURE__ */ __name(async () => {
    try {
      returnValue = await handler();
    } catch (e) {
      if (!(e instanceof InternalTimeoutError)) {
        throw e;
      }
    }
  }, "wrap");
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      context.cancelTask.abort();
      const error = errorMessage instanceof Error ? errorMessage : new TimeoutError(errorMessage);
      reject(error);
    }, timeoutMillis);
    storage.run(context, () => {
      wrap().then(() => resolve(returnValue)).catch(reject).finally(() => clearTimeout(timeout));
    });
  });
}
__name(addTimeoutToPromise, "addTimeoutToPromise");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  TimeoutError,
  addTimeoutToPromise,
  storage,
  tryCancel
});
//# sourceMappingURL=index.cjs.map