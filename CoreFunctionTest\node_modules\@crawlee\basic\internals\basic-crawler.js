"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicCrawler = void 0;
exports.createBasicRouter = createBasicRouter;
const tslib_1 = require("tslib");
const node_path_1 = require("node:path");
const core_1 = require("@crawlee/core");
const utils_1 = require("@crawlee/utils");
const sync_1 = require("csv-stringify/sync");
const fs_extra_1 = require("fs-extra");
const ow_1 = tslib_1.__importStar(require("ow"));
const tldts_1 = require("tldts");
const datastructures_1 = require("@apify/datastructures");
const log_1 = tslib_1.__importStar(require("@apify/log"));
const timeout_1 = require("@apify/timeout");
const utilities_1 = require("@apify/utilities");
const send_request_1 = require("./send-request");
/**
 * Since there's no set number of seconds before the container is terminated after
 * a migration event, we need some reasonable number to use for RequestList persistence.
 * Once a migration event is received, the crawler will be paused, and it will wait for
 * this long before persisting the RequestList state. This should allow most healthy
 * requests to finish and be marked as handled, thus lowering the amount of duplicate
 * results after migration.
 * @ignore
 */
const SAFE_MIGRATION_WAIT_MILLIS = 20000;
/**
 * Provides a simple framework for parallel crawling of web pages.
 * The URLs to crawl are fed either from a static list of URLs
 * or from a dynamic queue of URLs enabling recursive crawling of websites.
 *
 * `BasicCrawler` is a low-level tool that requires the user to implement the page
 * download and data extraction functionality themselves.
 * If we want a crawler that already facilitates this functionality,
 * we should consider using {@link CheerioCrawler}, {@link PuppeteerCrawler} or {@link PlaywrightCrawler}.
 *
 * `BasicCrawler` invokes the user-provided {@link BasicCrawlerOptions.requestHandler|`requestHandler`}
 * for each {@link Request} object, which represents a single URL to crawl.
 * The {@link Request} objects are fed from the {@link RequestList} or {@link RequestQueue}
 * instances provided by the {@link BasicCrawlerOptions.requestList|`requestList`} or {@link BasicCrawlerOptions.requestQueue|`requestQueue`}
 * constructor options, respectively. If neither `requestList` nor `requestQueue` options are provided,
 * the crawler will open the default request queue either when the {@link BasicCrawler.addRequests|`crawler.addRequests()`} function is called,
 * or if `requests` parameter (representing the initial requests) of the {@link BasicCrawler.run|`crawler.run()`} function is provided.
 *
 * If both {@link BasicCrawlerOptions.requestList|`requestList`} and {@link BasicCrawlerOptions.requestQueue|`requestQueue`} options are used,
 * the instance first processes URLs from the {@link RequestList} and automatically enqueues all of them
 * to the {@link RequestQueue} before it starts their processing. This ensures that a single URL is not crawled multiple times.
 *
 * The crawler finishes if there are no more {@link Request} objects to crawl.
 *
 * New requests are only dispatched when there is enough free CPU and memory available,
 * using the functionality provided by the {@link AutoscaledPool} class.
 * All {@link AutoscaledPool} configuration options can be passed to the {@link BasicCrawlerOptions.autoscaledPoolOptions|`autoscaledPoolOptions`}
 * parameter of the `BasicCrawler` constructor.
 * For user convenience, the {@link AutoscaledPoolOptions.minConcurrency|`minConcurrency`} and
 * {@link AutoscaledPoolOptions.maxConcurrency|`maxConcurrency`} options of the
 * underlying {@link AutoscaledPool} constructor are available directly in the `BasicCrawler` constructor.
 *
 * **Example usage:**
 *
 * ```javascript
 * import { BasicCrawler, Dataset } from 'crawlee';
 *
 * // Create a crawler instance
 * const crawler = new BasicCrawler({
 *     async requestHandler({ request, sendRequest }) {
 *         // 'request' contains an instance of the Request class
 *         // Here we simply fetch the HTML of the page and store it to a dataset
 *         const { body } = await sendRequest({
 *             url: request.url,
 *             method: request.method,
 *             body: request.payload,
 *             headers: request.headers,
 *         });
 *
 *         await Dataset.pushData({
 *             url: request.url,
 *             html: body,
 *         })
 *     },
 * });
 *
 * // Enqueue the initial requests and run the crawler
 * await crawler.run([
 *     'http://www.example.com/page-1',
 *     'http://www.example.com/page-2',
 * ]);
 * ```
 * @category Crawlers
 */
class BasicCrawler {
    /**
     * All `BasicCrawler` parameters are passed via an options object.
     */
    constructor(options = {}, config = core_1.Configuration.getGlobalConfig()) {
        Object.defineProperty(this, "config", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: config
        });
        /**
         * A reference to the underlying {@link Statistics} class that collects and logs run statistics for requests.
         */
        Object.defineProperty(this, "stats", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /**
         * A reference to the underlying {@link RequestList} class that manages the crawler's {@link Request|requests}.
         * Only available if used by the crawler.
         */
        Object.defineProperty(this, "requestList", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /**
         * Dynamic queue of URLs to be processed. This is useful for recursive crawling of websites.
         * A reference to the underlying {@link RequestQueue} class that manages the crawler's {@link Request|requests}.
         * Only available if used by the crawler.
         */
        Object.defineProperty(this, "requestQueue", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /**
         * A reference to the underlying {@link SessionPool} class that manages the crawler's {@link Session|sessions}.
         * Only available if used by the crawler.
         */
        Object.defineProperty(this, "sessionPool", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /**
         * A reference to the underlying {@link AutoscaledPool} class that manages the concurrency of the crawler.
         * > *NOTE:* This property is only initialized after calling the {@link BasicCrawler.run|`crawler.run()`} function.
         * We can use it to change the concurrency settings on the fly,
         * to pause the crawler by calling {@link AutoscaledPool.pause|`autoscaledPool.pause()`}
         * or to abort it by calling {@link AutoscaledPool.abort|`autoscaledPool.abort()`}.
         */
        Object.defineProperty(this, "autoscaledPool", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /**
         * Default {@link Router} instance that will be used if we don't specify any {@link BasicCrawlerOptions.requestHandler|`requestHandler`}.
         * See {@link Router.addHandler|`router.addHandler()`} and {@link Router.addDefaultHandler|`router.addDefaultHandler()`}.
         */
        Object.defineProperty(this, "router", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: core_1.Router.create()
        });
        Object.defineProperty(this, "running", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "hasFinishedBefore", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "log", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "requestHandler", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "errorHandler", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "failedRequestHandler", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "requestHandlerTimeoutMillis", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "internalTimeoutMillis", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "maxRequestRetries", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "sameDomainDelayMillis", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "domainAccessedTime", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "maxSessionRotations", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "handledRequestsCount", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "statusMessageLoggingInterval", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "statusMessageCallback", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "sessionPoolOptions", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "useSessionPool", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "crawlingContexts", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new Map()
        });
        Object.defineProperty(this, "autoscaledPoolOptions", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "events", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "httpClient", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "retryOnBlocked", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "respectRobotsTxtFile", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "onSkippedRequest", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_closeEvents", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "experiments", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "robotsTxtFileCache", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_experimentWarnings", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: {}
        });
        (0, ow_1.default)(options, 'BasicCrawlerOptions', ow_1.default.object.exactShape(BasicCrawler.optionsShape));
        const { requestList, requestQueue, maxRequestRetries = 3, sameDomainDelaySecs = 0, maxSessionRotations = 10, maxRequestsPerCrawl, autoscaledPoolOptions = {}, keepAlive, sessionPoolOptions = {}, useSessionPool = true, 
        // AutoscaledPool shorthands
        minConcurrency, maxConcurrency, maxRequestsPerMinute, retryOnBlocked = false, respectRobotsTxtFile = false, onSkippedRequest, 
        // internal
        log = log_1.default.child({ prefix: this.constructor.name }), experiments = {}, 
        // Old and new request handler methods
        handleRequestFunction, requestHandler, handleRequestTimeoutSecs, requestHandlerTimeoutSecs, errorHandler, handleFailedRequestFunction, failedRequestHandler, statusMessageLoggingInterval = 10, statusMessageCallback, statisticsOptions, httpClient, } = options;
        this.requestList = requestList;
        this.requestQueue = requestQueue;
        this.httpClient = httpClient ?? new core_1.GotScrapingHttpClient();
        this.log = log;
        this.statusMessageLoggingInterval = statusMessageLoggingInterval;
        this.statusMessageCallback = statusMessageCallback;
        this.events = config.getEventManager();
        this.domainAccessedTime = new Map();
        this.experiments = experiments;
        this.robotsTxtFileCache = new datastructures_1.LruCache({ maxLength: 1000 });
        this._handlePropertyNameChange({
            newName: 'requestHandler',
            oldName: 'handleRequestFunction',
            propertyKey: 'requestHandler',
            newProperty: requestHandler,
            oldProperty: handleRequestFunction,
            allowUndefined: true, // fallback to the default router
        });
        if (!this.requestHandler) {
            this.requestHandler = this.router;
        }
        this.errorHandler = errorHandler;
        this._handlePropertyNameChange({
            newName: 'failedRequestHandler',
            oldName: 'handleFailedRequestFunction',
            propertyKey: 'failedRequestHandler',
            newProperty: failedRequestHandler,
            oldProperty: handleFailedRequestFunction,
            allowUndefined: true,
        });
        let newRequestHandlerTimeout;
        if (!handleRequestTimeoutSecs) {
            if (!requestHandlerTimeoutSecs) {
                newRequestHandlerTimeout = 60000;
            }
            else {
                newRequestHandlerTimeout = requestHandlerTimeoutSecs * 1000;
            }
        }
        else if (requestHandlerTimeoutSecs) {
            newRequestHandlerTimeout = requestHandlerTimeoutSecs * 1000;
        }
        this.retryOnBlocked = retryOnBlocked;
        this.respectRobotsTxtFile = respectRobotsTxtFile;
        this.onSkippedRequest = onSkippedRequest;
        this._handlePropertyNameChange({
            newName: 'requestHandlerTimeoutSecs',
            oldName: 'handleRequestTimeoutSecs',
            propertyKey: 'requestHandlerTimeoutMillis',
            newProperty: newRequestHandlerTimeout,
            oldProperty: handleRequestTimeoutSecs ? handleRequestTimeoutSecs * 1000 : undefined,
        });
        const tryEnv = (val) => (val == null ? null : +val);
        // allow at least 5min for internal timeouts
        this.internalTimeoutMillis =
            tryEnv(process.env.CRAWLEE_INTERNAL_TIMEOUT) ?? Math.max(this.requestHandlerTimeoutMillis * 2, 300e3);
        // override the default internal timeout of request queue to respect `requestHandlerTimeoutMillis`
        if (this.requestQueue) {
            this.requestQueue.internalTimeoutMillis = this.internalTimeoutMillis;
            // for request queue v2, we want to lock requests for slightly longer than the request handler timeout so that there is some padding for locking-related overhead,
            // but never for less than a minute
            this.requestQueue.requestLockSecs = Math.max(this.requestHandlerTimeoutMillis / 1000 + 5, 60);
        }
        this.maxRequestRetries = maxRequestRetries;
        this.sameDomainDelayMillis = sameDomainDelaySecs * 1000;
        this.maxSessionRotations = maxSessionRotations;
        this.handledRequestsCount = 0;
        this.stats = new core_1.Statistics({
            logMessage: `${log.getOptions().prefix} request statistics:`,
            log,
            config,
            ...statisticsOptions,
        });
        this.sessionPoolOptions = {
            ...sessionPoolOptions,
            log,
        };
        if (this.retryOnBlocked) {
            this.sessionPoolOptions.blockedStatusCodes = sessionPoolOptions.blockedStatusCodes ?? [];
            if (this.sessionPoolOptions.blockedStatusCodes.length !== 0) {
                log.warning(`Both 'blockedStatusCodes' and 'retryOnBlocked' are set. Please note that the 'retryOnBlocked' feature might not work as expected.`);
            }
        }
        this.useSessionPool = useSessionPool;
        this.crawlingContexts = new Map();
        const maxSignedInteger = 2 ** 31 - 1;
        if (this.requestHandlerTimeoutMillis > maxSignedInteger) {
            log.warning(`requestHandlerTimeoutMillis ${this.requestHandlerTimeoutMillis}` +
                ` does not fit a signed 32-bit integer. Limiting the value to ${maxSignedInteger}`);
            this.requestHandlerTimeoutMillis = maxSignedInteger;
        }
        this.internalTimeoutMillis = Math.min(this.internalTimeoutMillis, maxSignedInteger);
        let shouldLogMaxPagesExceeded = true;
        const isMaxPagesExceeded = () => maxRequestsPerCrawl && maxRequestsPerCrawl <= this.handledRequestsCount;
        // eslint-disable-next-line prefer-const
        let { isFinishedFunction, isTaskReadyFunction } = autoscaledPoolOptions;
        // override even if `isFinishedFunction` provided by user - `keepAlive` has higher priority
        if (keepAlive) {
            isFinishedFunction = async () => false;
        }
        const basicCrawlerAutoscaledPoolConfiguration = {
            minConcurrency: minConcurrency ?? autoscaledPoolOptions?.minConcurrency,
            maxConcurrency: maxConcurrency ?? autoscaledPoolOptions?.maxConcurrency,
            maxTasksPerMinute: maxRequestsPerMinute ?? autoscaledPoolOptions?.maxTasksPerMinute,
            runTaskFunction: this._runTaskFunction.bind(this),
            isTaskReadyFunction: async () => {
                if (isMaxPagesExceeded()) {
                    if (shouldLogMaxPagesExceeded) {
                        log.info('Crawler reached the maxRequestsPerCrawl limit of ' +
                            `${maxRequestsPerCrawl} requests and will shut down soon. Requests that are in progress will be allowed to finish.`);
                        shouldLogMaxPagesExceeded = false;
                    }
                    return false;
                }
                return isTaskReadyFunction ? await isTaskReadyFunction() : await this._isTaskReadyFunction();
            },
            isFinishedFunction: async () => {
                if (isMaxPagesExceeded()) {
                    log.info(`Earlier, the crawler reached the maxRequestsPerCrawl limit of ${maxRequestsPerCrawl} requests ` +
                        'and all requests that were in progress at that time have now finished. ' +
                        `In total, the crawler processed ${this.handledRequestsCount} requests and will shut down.`);
                    return true;
                }
                const isFinished = isFinishedFunction
                    ? await isFinishedFunction()
                    : await this._defaultIsFinishedFunction();
                if (isFinished) {
                    const reason = isFinishedFunction
                        ? "Crawler's custom isFinishedFunction() returned true, the crawler will shut down."
                        : 'All requests from the queue have been processed, the crawler will shut down.';
                    log.info(reason);
                }
                return isFinished;
            },
            log,
        };
        this.autoscaledPoolOptions = { ...autoscaledPoolOptions, ...basicCrawlerAutoscaledPoolConfiguration };
    }
    /**
     * Checks if the given error is a proxy error by comparing its message to a list of known proxy error messages.
     * Used for retrying requests that failed due to proxy errors.
     *
     * @param error The error to check.
     */
    isProxyError(error) {
        return utils_1.ROTATE_PROXY_ERRORS.some((x) => this._getMessageFromError(error)?.includes(x));
    }
    /**
     * Checks whether the given crawling context is getting blocked by anti-bot protection using several heuristics.
     * Returns `false` if the request is not blocked, otherwise returns a string with a description of the block reason.
     * @param _crawlingContext The crawling context to check.
     */
    async isRequestBlocked(_crawlingContext) {
        throw new Error('the "isRequestBlocked" method is not implemented in this crawler.');
    }
    /**
     * This method is periodically called by the crawler, every `statusMessageLoggingInterval` seconds.
     */
    async setStatusMessage(message, options = {}) {
        const data = options.isStatusMessageTerminal != null ? { terminal: options.isStatusMessageTerminal } : undefined;
        this.log.internal(log_1.LogLevel[options.level ?? 'DEBUG'], message, data);
        const client = this.config.getStorageClient();
        if (!client.setStatusMessage) {
            return;
        }
        // just to be sure, this should be fast
        await (0, timeout_1.addTimeoutToPromise)(async () => client.setStatusMessage(message, options), 1000, 'Setting status message timed out after 1s').catch((e) => this.log.debug(e.message));
    }
    getPeriodicLogger() {
        let previousState = { ...this.stats.state };
        const getOperationMode = () => {
            const { requestsFailed } = this.stats.state;
            const { requestsFailed: previousRequestsFailed } = previousState;
            previousState = { ...this.stats.state };
            if (requestsFailed - previousRequestsFailed > 0) {
                return 'ERROR';
            }
            return 'REGULAR';
        };
        const log = async () => {
            const operationMode = getOperationMode();
            let message;
            if (operationMode === 'ERROR') {
                message = `Experiencing problems, ${this.stats.state.requestsFailed - previousState.requestsFailed || this.stats.state.requestsFailed} failed requests in the past ${this.statusMessageLoggingInterval} seconds.`;
            }
            else {
                const total = this.requestQueue?.getTotalCount() || this.requestList?.length();
                message = `Crawled ${this.stats.state.requestsFinished}${total ? `/${total}` : ''} pages, ${this.stats.state.requestsFailed} failed requests, desired concurrency ${this.autoscaledPool?.desiredConcurrency ?? 0}.`;
            }
            if (this.statusMessageCallback) {
                await this.statusMessageCallback({
                    crawler: this,
                    state: this.stats.state,
                    previousState,
                    message,
                });
                return;
            }
            await this.setStatusMessage(message);
        };
        const interval = setInterval(log, this.statusMessageLoggingInterval * 1e3);
        return { log, stop: () => clearInterval(interval) };
    }
    /**
     * Runs the crawler. Returns a promise that resolves once all the requests are processed
     * and `autoscaledPool.isFinished` returns `true`.
     *
     * We can use the `requests` parameter to enqueue the initial requests — it is a shortcut for
     * running {@link BasicCrawler.addRequests|`crawler.addRequests()`} before {@link BasicCrawler.run|`crawler.run()`}.
     *
     * @param [requests] The requests to add.
     * @param [options] Options for the request queue.
     */
    async run(requests, options) {
        if (this.running) {
            throw new Error('This crawler instance is already running, you can add more requests to it via `crawler.addRequests()`.');
        }
        const { purgeRequestQueue = true, ...addRequestsOptions } = options ?? {};
        if (this.hasFinishedBefore) {
            // When executing the run method for the second time explicitly,
            // we need to purge the default RQ to allow processing the same requests again - this is important so users can
            // pass in failed requests back to the `crawler.run()`, otherwise they would be considered as handled and
            // ignored - as a failed requests is still handled.
            if (this.requestQueue?.name === 'default' && purgeRequestQueue) {
                await this.requestQueue.drop();
                this.requestQueue = await this._getRequestQueue();
            }
            this.stats.reset();
            await this.stats.resetStore();
            await this.sessionPool?.resetStore();
        }
        this.running = true;
        await (0, core_1.purgeDefaultStorages)({ onlyPurgeOnce: true });
        if (requests) {
            await this.addRequests(requests, addRequestsOptions);
        }
        await this._init();
        await this.stats.startCapturing();
        const periodicLogger = this.getPeriodicLogger();
        await this.setStatusMessage('Starting the crawler.', { level: 'INFO' });
        const sigintHandler = async () => {
            this.log.warning('Pausing... Press CTRL+C again to force exit. To resume, do: CRAWLEE_PURGE_ON_START=0 npm start');
            await this._pauseOnMigration();
            await this.autoscaledPool.abort();
        };
        // Attach a listener to handle migration and aborting events gracefully.
        const boundPauseOnMigration = this._pauseOnMigration.bind(this);
        process.once('SIGINT', sigintHandler);
        this.events.on("migrating" /* EventType.MIGRATING */, boundPauseOnMigration);
        this.events.on("aborting" /* EventType.ABORTING */, boundPauseOnMigration);
        let stats = {};
        try {
            await this.autoscaledPool.run();
        }
        finally {
            await this.teardown();
            await this.stats.stopCapturing();
            process.off('SIGINT', sigintHandler);
            this.events.off("migrating" /* EventType.MIGRATING */, boundPauseOnMigration);
            this.events.off("aborting" /* EventType.ABORTING */, boundPauseOnMigration);
            const finalStats = this.stats.calculate();
            stats = {
                requestsFinished: this.stats.state.requestsFinished,
                requestsFailed: this.stats.state.requestsFailed,
                retryHistogram: this.stats.requestRetryHistogram,
                ...finalStats,
            };
            this.log.info('Final request statistics:', stats);
            if (this.stats.errorTracker.total !== 0) {
                const prettify = ([count, info]) => `${count}x: ${info.at(-1).trim()} (${info[0]})`;
                this.log.info(`Error analysis:`, {
                    totalErrors: this.stats.errorTracker.total,
                    uniqueErrors: this.stats.errorTracker.getUniqueErrorCount(),
                    mostCommonErrors: this.stats.errorTracker.getMostPopularErrors(3).map(prettify),
                });
            }
            const client = this.config.getStorageClient();
            if (client.teardown) {
                let finished = false;
                setTimeout(() => {
                    if (!finished) {
                        this.log.info('Waiting for the storage to write its state to file system.');
                    }
                }, 1000);
                await client.teardown();
                finished = true;
            }
            periodicLogger.stop();
            await this.setStatusMessage(`Finished! Total ${this.stats.state.requestsFinished + this.stats.state.requestsFailed} requests: ${this.stats.state.requestsFinished} succeeded, ${this.stats.state.requestsFailed} failed.`, { isStatusMessageTerminal: true, level: 'INFO' });
            this.running = false;
            this.hasFinishedBefore = true;
        }
        return stats;
    }
    /**
     * Gracefully stops the current run of the crawler.
     *
     * All the tasks active at the time of calling this method will be allowed to finish.
     */
    stop(message = 'The crawler has been gracefully stopped.') {
        // Gracefully starve the this.autoscaledPool, so it doesn't start new tasks. Resolves once the pool is cleared.
        this.autoscaledPool
            ?.pause()
            // Resolves the `autoscaledPool.run()` promise in the `BasicCrawler.run()` method. Since the pool is already paused, it resolves immediately and doesn't kill any tasks.
            .then(async () => this.autoscaledPool?.abort())
            .then(() => this.log.info(message))
            .catch((err) => {
            this.log.error('An error occurred when stopping the crawler:', err);
        });
    }
    async getRequestQueue() {
        if (!this.requestQueue && this.requestList) {
            this.log.warningOnce('When using RequestList and RequestQueue at the same time, you should instantiate both explicitly and provide them in the crawler options, to ensure correctly handled restarts of the crawler.');
        }
        this.requestQueue ?? (this.requestQueue = await this._getRequestQueue());
        return this.requestQueue;
    }
    async useState(defaultValue = {}) {
        const kvs = await core_1.KeyValueStore.open(null, { config: this.config });
        return kvs.getAutoSavedValue(BasicCrawler.CRAWLEE_STATE_KEY, defaultValue);
    }
    /**
     * Adds requests to the queue in batches. By default, it will resolve after the initial batch is added, and continue
     * adding the rest in background. You can configure the batch size via `batchSize` option and the sleep time in between
     * the batches via `waitBetweenBatchesMillis`. If you want to wait for all batches to be added to the queue, you can use
     * the `waitForAllRequestsToBeAdded` promise you get in the response object.
     *
     * This is an alias for calling `addRequestsBatched()` on the implicit `RequestQueue` for this crawler instance.
     *
     * @param requests The requests to add
     * @param options Options for the request queue
     */
    async addRequests(requests, options = {}) {
        const requestQueue = await this.getRequestQueue();
        if (!this.respectRobotsTxtFile) {
            return requestQueue.addRequestsBatched(requests, options);
        }
        const allowedRequests = [];
        const skipped = new Set();
        for (const request of requests) {
            const url = typeof request === 'string' ? request : request.url;
            if (await this.isAllowedBasedOnRobotsTxtFile(url)) {
                allowedRequests.push(request);
            }
            else {
                skipped.add(url);
                await this.onSkippedRequest?.({ url, reason: 'robotsTxt' });
            }
        }
        if (skipped.size > 0) {
            this.log.warning(`Some requests were skipped because they were disallowed based on the robots.txt file`, {
                skipped: [...skipped],
            });
            if (this.onSkippedRequest) {
                await Promise.all([...skipped].map((url) => {
                    return this.onSkippedRequest({ url, reason: 'robotsTxt' });
                }));
            }
        }
        return requestQueue.addRequestsBatched(allowedRequests, options);
    }
    /**
     * Pushes data to the specified {@link Dataset}, or the default crawler {@link Dataset} by calling {@link Dataset.pushData}.
     */
    async pushData(data, datasetIdOrName) {
        const dataset = await this.getDataset(datasetIdOrName);
        return dataset.pushData(data);
    }
    /**
     * Retrieves the specified {@link Dataset}, or the default crawler {@link Dataset}.
     */
    async getDataset(idOrName) {
        return core_1.Dataset.open(idOrName, { config: this.config });
    }
    /**
     * Retrieves data from the default crawler {@link Dataset} by calling {@link Dataset.getData}.
     */
    async getData(...args) {
        const dataset = await this.getDataset();
        return dataset.getData(...args);
    }
    /**
     * Retrieves all the data from the default crawler {@link Dataset} and exports them to the specified format.
     * Supported formats are currently 'json' and 'csv', and will be inferred from the `path` automatically.
     */
    async exportData(path, format, options) {
        const supportedFormats = ['json', 'csv'];
        if (!format && path.match(/\.(json|csv)$/i)) {
            format = path.toLowerCase().match(/\.(json|csv)$/)[1];
        }
        if (!format) {
            throw new Error(`Failed to infer format from the path: '${path}'. Supported formats: ${supportedFormats.join(', ')}`);
        }
        if (!supportedFormats.includes(format)) {
            throw new Error(`Unsupported format: '${format}'. Use one of ${supportedFormats.join(', ')}`);
        }
        const dataset = await this.getDataset();
        const items = await dataset.export(options);
        if (format === 'csv') {
            const value = (0, sync_1.stringify)([Object.keys(items[0]), ...items.map((item) => Object.values(item))]);
            await (0, fs_extra_1.ensureDir)((0, node_path_1.dirname)(path));
            await (0, fs_extra_1.writeFile)(path, value);
            this.log.info(`Export to ${path} finished!`);
        }
        if (format === 'json') {
            await (0, fs_extra_1.ensureDir)((0, node_path_1.dirname)(path));
            await (0, fs_extra_1.writeJSON)(path, items, { spaces: 4 });
            this.log.info(`Export to ${path} finished!`);
        }
        return items;
    }
    async _init() {
        if (!this.events.isInitialized()) {
            await this.events.init();
            this._closeEvents = true;
        }
        // Initialize AutoscaledPool before awaiting _loadHandledRequestCount(),
        // so that the caller can get a reference to it before awaiting the promise returned from run()
        // (otherwise there would be no way)
        this.autoscaledPool = new core_1.AutoscaledPool(this.autoscaledPoolOptions, this.config);
        if (this.useSessionPool) {
            this.sessionPool = await core_1.SessionPool.open(this.sessionPoolOptions, this.config);
            // Assuming there are not more than 20 browsers running at once;
            this.sessionPool.setMaxListeners(20);
        }
        await this._loadHandledRequestCount();
    }
    async _runRequestHandler(crawlingContext) {
        await this.requestHandler(crawlingContext);
    }
    /**
     * Handles blocked request
     */
    _throwOnBlockedRequest(session, statusCode) {
        const isBlocked = session.retireOnBlockedStatusCodes(statusCode);
        if (isBlocked) {
            throw new Error(`Request blocked - received ${statusCode} status code.`);
        }
    }
    async isAllowedBasedOnRobotsTxtFile(url) {
        if (!this.respectRobotsTxtFile) {
            return true;
        }
        const robotsTxtFile = await this.getRobotsTxtFileForUrl(url);
        return !robotsTxtFile || robotsTxtFile.isAllowed(url);
    }
    async getRobotsTxtFileForUrl(url) {
        if (!this.respectRobotsTxtFile) {
            return undefined;
        }
        try {
            const origin = new URL(url).origin;
            const cachedRobotsTxtFile = this.robotsTxtFileCache.get(origin);
            if (cachedRobotsTxtFile) {
                return cachedRobotsTxtFile;
            }
            const robotsTxtFile = await utils_1.RobotsTxtFile.find(url);
            this.robotsTxtFileCache.add(origin, robotsTxtFile);
            return robotsTxtFile;
        }
        catch (e) {
            this.log.warning(`Failed to fetch robots.txt for request ${url}`);
            return undefined;
        }
    }
    async _pauseOnMigration() {
        if (this.autoscaledPool) {
            // if run wasn't called, this is going to crash
            await this.autoscaledPool.pause(SAFE_MIGRATION_WAIT_MILLIS).catch((err) => {
                if (err.message.includes('running tasks did not finish')) {
                    this.log.error('The crawler was paused due to migration to another host, ' +
                        "but some requests did not finish in time. Those requests' results may be duplicated.");
                }
                else {
                    throw err;
                }
            });
        }
        const requestListPersistPromise = (async () => {
            if (this.requestList) {
                if (await this.requestList.isFinished())
                    return;
                await this.requestList.persistState().catch((err) => {
                    if (err.message.includes('Cannot persist state.')) {
                        this.log.error("The crawler attempted to persist its request list's state and failed due to missing or " +
                            'invalid config. Make sure to use either RequestList.open() or the "stateKeyPrefix" option of RequestList ' +
                            'constructor to ensure your crawling state is persisted through host migrations and restarts.');
                    }
                    else {
                        this.log.exception(err, 'An unexpected error occurred when the crawler ' +
                            "attempted to persist its request list's state.");
                    }
                });
            }
        })();
        await Promise.all([requestListPersistPromise, this.stats.persistState()]);
    }
    /**
     * Fetches request from either RequestList or RequestQueue. If request comes from a RequestList
     * and RequestQueue is present then enqueues it to the queue first.
     */
    async _fetchNextRequest() {
        if (!this.requestList || (await this.requestList.isFinished())) {
            return this.requestQueue?.fetchNextRequest();
        }
        const request = await this.requestList.fetchNextRequest();
        if (!this.requestQueue)
            return request;
        if (!request)
            return this.requestQueue.fetchNextRequest();
        try {
            await this.requestQueue.addRequest(request, { forefront: true });
        }
        catch (err) {
            // If requestQueue.addRequest() fails here then we must reclaim it back to
            // the RequestList because probably it's not yet in the queue!
            this.log.error('Adding of request from the RequestList to the RequestQueue failed, reclaiming request back to the list.', { request });
            await this.requestList.reclaimRequest(request);
            return null;
        }
        await this.requestList.markRequestHandled(request);
        return this.requestQueue.fetchNextRequest();
    }
    /**
     * Executed when `errorHandler` finishes or the request is successful.
     * Can be used to clean up orphaned browser pages.
     */
    async _cleanupContext(_crawlingContext) { }
    /**
     * Delays processing of the request based on the `sameDomainDelaySecs` option,
     * adding it back to the queue after the timeout passes. Returns `true` if the request
     * should be ignored and will be reclaimed to the queue once ready.
     */
    delayRequest(request, source) {
        const domain = (0, tldts_1.getDomain)(request.url);
        if (!domain || !request) {
            return false;
        }
        const now = Date.now();
        const lastAccessTime = this.domainAccessedTime.get(domain);
        if (!lastAccessTime || now - lastAccessTime >= this.sameDomainDelayMillis) {
            this.domainAccessedTime.set(domain, now);
            return false;
        }
        if (source instanceof core_1.RequestQueueV1) {
            // eslint-disable-next-line dot-notation
            source['inProgress']?.delete(request.id);
        }
        const delay = lastAccessTime + this.sameDomainDelayMillis - now;
        this.log.debug(`Request ${request.url} (${request.id}) will be reclaimed after ${delay} milliseconds due to same domain delay`);
        setTimeout(async () => {
            this.log.debug(`Adding request ${request.url} (${request.id}) back to the queue`);
            if (source instanceof core_1.RequestQueueV1) {
                // eslint-disable-next-line dot-notation
                source['inProgress'].add(request.id);
            }
            await source.reclaimRequest(request, { forefront: request.userData?.__crawlee?.forefront });
        }, delay);
        return true;
    }
    /**
     * Wrapper around requestHandler that fetches requests from RequestList/RequestQueue
     * then retries them in a case of an error, etc.
     */
    async _runTaskFunction() {
        const source = this.requestQueue || this.requestList || (await this.getRequestQueue());
        let request;
        let session;
        await this._timeoutAndRetry(async () => {
            request = await this._fetchNextRequest();
        }, this.internalTimeoutMillis, `Fetching next request timed out after ${this.internalTimeoutMillis / 1e3} seconds.`);
        (0, timeout_1.tryCancel)();
        if (this.useSessionPool) {
            await this._timeoutAndRetry(async () => {
                session = await this.sessionPool.getSession();
            }, this.internalTimeoutMillis, `Fetching session timed out after ${this.internalTimeoutMillis / 1e3} seconds.`);
        }
        (0, timeout_1.tryCancel)();
        if (!request || this.delayRequest(request, source)) {
            return;
        }
        if (!(await this.isAllowedBasedOnRobotsTxtFile(request.url))) {
            this.log.warning(`Skipping request ${request.url} (${request.id}) because it is disallowed based on robots.txt`);
            request.state = core_1.RequestState.SKIPPED;
            request.noRetry = true;
            await source.markRequestHandled(request);
            await this.onSkippedRequest?.({
                url: request.url,
                reason: 'robotsTxt',
            });
            return;
        }
        // Reset loadedUrl so an old one is not carried over to retries.
        request.loadedUrl = undefined;
        const statisticsId = request.id || request.uniqueKey;
        this.stats.startJob(statisticsId);
        // Shared crawling context
        // @ts-expect-error
        // All missing properties (that extend CrawlingContext) are set dynamically,
        // but TS does not know that, so otherwise it would throw when compiling.
        const crawlingContext = {
            id: (0, utilities_1.cryptoRandomObjectId)(10),
            crawler: this,
            log: this.log,
            request,
            session,
            enqueueLinks: async (options) => {
                return (0, core_1.enqueueLinks)({
                    // specify the RQ first to allow overriding it
                    requestQueue: await this.getRequestQueue(),
                    robotsTxtFile: await this.getRobotsTxtFileForUrl(request.url),
                    onSkippedRequest: this.onSkippedRequest,
                    ...options,
                });
            },
            addRequests: this.addRequests.bind(this),
            pushData: this.pushData.bind(this),
            useState: this.useState.bind(this),
            sendRequest: (0, send_request_1.createSendRequest)(this.httpClient, request, session, () => crawlingContext.proxyInfo?.url),
            getKeyValueStore: async (idOrName) => core_1.KeyValueStore.open(idOrName, { config: this.config }),
        };
        this.crawlingContexts.set(crawlingContext.id, crawlingContext);
        let isRequestLocked = true;
        try {
            request.state = core_1.RequestState.REQUEST_HANDLER;
            await (0, timeout_1.addTimeoutToPromise)(async () => this._runRequestHandler(crawlingContext), this.requestHandlerTimeoutMillis, `requestHandler timed out after ${this.requestHandlerTimeoutMillis / 1000} seconds (${request.id}).`);
            await this._timeoutAndRetry(async () => source.markRequestHandled(request), this.internalTimeoutMillis, `Marking request ${request.url} (${request.id}) as handled timed out after ${this.internalTimeoutMillis / 1e3} seconds.`);
            isRequestLocked = false; // markRequestHandled succeeded and unlocked the request
            this.stats.finishJob(statisticsId, request.retryCount);
            this.handledRequestsCount++;
            // reclaim session if request finishes successfully
            request.state = core_1.RequestState.DONE;
            crawlingContext.session?.markGood();
        }
        catch (err) {
            try {
                request.state = core_1.RequestState.ERROR_HANDLER;
                await (0, timeout_1.addTimeoutToPromise)(async () => this._requestFunctionErrorHandler(err, crawlingContext, source), this.internalTimeoutMillis, `Handling request failure of ${request.url} (${request.id}) timed out after ${this.internalTimeoutMillis / 1e3} seconds.`);
                if (!(err instanceof core_1.CriticalError)) {
                    isRequestLocked = false; // _requestFunctionErrorHandler calls either markRequestHandled or reclaimRequest
                }
                request.state = core_1.RequestState.DONE;
            }
            catch (secondaryError) {
                if (!secondaryError.triggeredFromUserHandler &&
                    // avoid reprinting the same critical error multiple times, as it will be printed by Nodejs at the end anyway
                    !(secondaryError instanceof core_1.CriticalError)) {
                    const apifySpecific = process.env.APIFY_IS_AT_HOME
                        ? `This may have happened due to an internal error of Apify's API or due to a misconfigured crawler.`
                        : '';
                    this.log.exception(secondaryError, 'An exception occurred during handling of failed request. ' +
                        `This places the crawler and its underlying storages into an unknown state and crawling will be terminated. ${apifySpecific}`);
                }
                request.state = core_1.RequestState.ERROR;
                throw secondaryError;
            }
            // decrease the session score if the request fails (but the error handler did not throw)
            crawlingContext.session?.markBad();
        }
        finally {
            await this._cleanupContext(crawlingContext);
            this.crawlingContexts.delete(crawlingContext.id);
            // Safety net - release the lock if nobody managed to do it before
            if (isRequestLocked && source instanceof core_1.RequestProvider) {
                try {
                    await source.client.deleteRequestLock(request.id);
                }
                catch {
                    // We don't have the lock, or the request was never locked. Either way it's fine
                }
            }
        }
    }
    /**
     * Run async callback with given timeout and retry.
     * @ignore
     */
    async _timeoutAndRetry(handler, timeout, error, maxRetries = 3, retried = 1) {
        try {
            await (0, timeout_1.addTimeoutToPromise)(handler, timeout, error);
        }
        catch (e) {
            if (retried <= maxRetries) {
                // we retry on any error, not just timeout
                this.log.warning(`${e.message} (retrying ${retried}/${maxRetries})`);
                void this._timeoutAndRetry(handler, timeout, error, maxRetries, retried + 1);
                return;
            }
            throw e;
        }
    }
    /**
     * Returns true if either RequestList or RequestQueue have a request ready for processing.
     */
    async _isTaskReadyFunction() {
        // First check RequestList, since it's only in memory.
        const isRequestListEmpty = this.requestList ? await this.requestList.isEmpty() : true;
        // If RequestList is not empty, task is ready, no reason to check RequestQueue.
        if (!isRequestListEmpty)
            return true;
        // If RequestQueue is not empty, task is ready, return true, otherwise false.
        return this.requestQueue ? !(await this.requestQueue.isEmpty()) : false;
    }
    /**
     * Returns true if both RequestList and RequestQueue have all requests finished.
     */
    async _defaultIsFinishedFunction() {
        const [isRequestListFinished, isRequestQueueFinished] = await Promise.all([
            this.requestList ? this.requestList.isFinished() : true,
            this.requestQueue ? this.requestQueue.isFinished() : true,
        ]);
        // If both are finished, return true, otherwise return false.
        return isRequestListFinished && isRequestQueueFinished;
    }
    async _rotateSession(crawlingContext) {
        const { request } = crawlingContext;
        request.sessionRotationCount ?? (request.sessionRotationCount = 0);
        request.sessionRotationCount++;
        crawlingContext.session?.retire();
    }
    /**
     * Handles errors thrown by user provided requestHandler()
     */
    async _requestFunctionErrorHandler(error, crawlingContext, source) {
        const { request } = crawlingContext;
        request.pushErrorMessage(error);
        if (error instanceof core_1.CriticalError) {
            throw error;
        }
        const shouldRetryRequest = this._canRequestBeRetried(request, error);
        if (shouldRetryRequest) {
            await this.stats.errorTrackerRetry.addAsync(error, crawlingContext);
            await this._tagUserHandlerError(() => this.errorHandler?.(this._augmentContextWithDeprecatedError(crawlingContext, error), error));
            if (error instanceof core_1.SessionError) {
                await this._rotateSession(crawlingContext);
            }
            if (!request.noRetry) {
                request.retryCount++;
                const { url, retryCount, id } = request;
                // We don't want to see the stack trace in the logs by default, when we are going to retry the request.
                // Thus, we print the full stack trace only when CRAWLEE_VERBOSE_LOG environment variable is set to true.
                const message = this._getMessageFromError(error);
                this.log.warning(`Reclaiming failed request back to the list or queue. ${message}`, {
                    id,
                    url,
                    retryCount,
                });
                await source.reclaimRequest(request, { forefront: request.userData?.__crawlee?.forefront });
                return;
            }
        }
        // If the request is non-retryable, the error and snapshot aren't saved in the errorTrackerRetry object.
        // Therefore, we pass the crawlingContext to the errorTracker.add method, enabling snapshot capture.
        // This is to make sure the error snapshot is not duplicated in the errorTrackerRetry and errorTracker objects.
        const { noRetry, maxRetries } = request;
        if (noRetry || !maxRetries) {
            await this.stats.errorTracker.addAsync(error, crawlingContext);
        }
        else {
            this.stats.errorTracker.add(error);
        }
        // If we get here, the request is either not retryable
        // or failed more than retryCount times and will not be retried anymore.
        // Mark the request as failed and do not retry.
        this.handledRequestsCount++;
        await source.markRequestHandled(request);
        this.stats.failJob(request.id || request.uniqueKey, request.retryCount);
        await this._handleFailedRequestHandler(crawlingContext, error); // This function prints an error message.
    }
    async _tagUserHandlerError(cb) {
        try {
            return (await cb());
        }
        catch (e) {
            Object.defineProperty(e, 'triggeredFromUserHandler', { value: true });
            throw e;
        }
    }
    async _handleFailedRequestHandler(crawlingContext, error) {
        // Always log the last error regardless if the user provided a failedRequestHandler
        const { id, url, method, uniqueKey } = crawlingContext.request;
        const message = this._getMessageFromError(error, true);
        this.log.error(`Request failed and reached maximum retries. ${message}`, { id, url, method, uniqueKey });
        if (this.failedRequestHandler) {
            await this._tagUserHandlerError(() => this.failedRequestHandler?.(this._augmentContextWithDeprecatedError(crawlingContext, error), error));
        }
    }
    /**
     * Resolves the most verbose error message from a thrown error
     * @param error The error received
     * @returns The message to be logged
     */
    _getMessageFromError(error, forceStack = false) {
        if ([TypeError, SyntaxError, ReferenceError].some((type) => error instanceof type)) {
            forceStack = true;
        }
        const stackLines = error?.stack ? error.stack.split('\n') : new Error().stack.split('\n').slice(2);
        const baseDir = process.cwd();
        const userLine = stackLines.find((line) => line.includes(baseDir) && !line.includes('node_modules'));
        if (error instanceof timeout_1.TimeoutError) {
            return process.env.CRAWLEE_VERBOSE_LOG ? error.stack : error.message || error; // stack in timeout errors does not really help
        }
        return process.env.CRAWLEE_VERBOSE_LOG || forceStack
            ? (error.stack ?? [error.message || error, ...stackLines].join('\n'))
            : [error.message || error, userLine].join('\n');
    }
    _canRequestBeRetried(request, error) {
        // Request should never be retried, or the error encountered makes it not able to be retried, or the session rotation limit has been reached
        if (request.noRetry ||
            error instanceof core_1.NonRetryableError ||
            (error instanceof core_1.SessionError && this.maxSessionRotations <= (request.sessionRotationCount ?? 0))) {
            return false;
        }
        // User requested retry (we ignore retry count here as its explicitly told by the user to retry)
        if (error instanceof core_1.RetryRequestError) {
            return true;
        }
        // Ensure there are more retries available for the request
        const maxRequestRetries = request.maxRetries ?? this.maxRequestRetries;
        return request.retryCount < maxRequestRetries;
    }
    _augmentContextWithDeprecatedError(context, error) {
        Object.defineProperty(context, 'error', {
            get: () => {
                this.log.deprecated("The 'error' property of the crawling context is deprecated, and it is now passed as the second parameter in 'errorHandler' and 'failedRequestHandler'. Please update your code, as this property will be removed in a future version.");
                return error;
            },
            configurable: true,
        });
        return context;
    }
    /**
     * Updates handledRequestsCount from possibly stored counts,
     * usually after worker migration. Since one of the stores
     * needs to have priority when both are present,
     * it is the request queue, because generally, the request
     * list will first be dumped into the queue and then left
     * empty.
     */
    async _loadHandledRequestCount() {
        if (this.requestQueue) {
            this.handledRequestsCount = await this.requestQueue.handledCount();
        }
        else if (this.requestList) {
            this.handledRequestsCount = this.requestList.handledCount();
        }
    }
    async _executeHooks(hooks, ...args) {
        if (Array.isArray(hooks) && hooks.length) {
            for (const hook of hooks) {
                await hook(...args);
            }
        }
    }
    /**
     * Function for cleaning up after all request are processed.
     * @ignore
     */
    async teardown() {
        this.events.emit("persistState" /* EventType.PERSIST_STATE */, { isMigrating: false });
        if (this.useSessionPool) {
            await this.sessionPool.teardown();
        }
        if (this._closeEvents) {
            await this.events.close();
        }
        await this.autoscaledPool?.abort();
    }
    _handlePropertyNameChange({ newProperty, newName, oldProperty, oldName, propertyKey, allowUndefined = false, }) {
        if (newProperty && oldProperty) {
            this.log.warning([
                `Both "${newName}" and "${oldName}" were provided in the crawler options.`,
                `"${oldName}" has been renamed to "${newName}", and will be removed in a future version.`,
                `As such, "${newName}" will be used instead.`,
            ].join('\n'));
            // @ts-expect-error Assigning to possibly readonly properties
            this[propertyKey] = newProperty;
        }
        else if (oldProperty) {
            this.log.warning([
                `"${oldName}" has been renamed to "${newName}", and will be removed in a future version.`,
                `The provided value will be used, but you should rename "${oldName}" to "${newName}" in your crawler options.`,
            ].join('\n'));
            // @ts-expect-error Assigning to possibly readonly properties
            this[propertyKey] = oldProperty;
        }
        else if (newProperty) {
            // @ts-expect-error Assigning to possibly readonly properties
            this[propertyKey] = newProperty;
        }
        else if (!allowUndefined) {
            throw new ow_1.ArgumentError(`"${newName}" must be provided in the crawler options`, this.constructor);
        }
    }
    _getCookieHeaderFromRequest(request) {
        if (request.headers?.Cookie && request.headers?.cookie) {
            this.log.warning(`Encountered mixed casing for the cookie headers for request ${request.url} (${request.id}). Their values will be merged.`);
            return (0, core_1.mergeCookies)(request.url, [request.headers.cookie, request.headers.Cookie]);
        }
        return request.headers?.Cookie || request.headers?.cookie || '';
    }
    async _getRequestQueue() {
        // Check if it's explicitly disabled
        if (this.experiments.requestLocking === false) {
            if (!this._experimentWarnings.requestLocking) {
                this.log.info('Using the old RequestQueue implementation without request locking.');
                this._experimentWarnings.requestLocking = true;
            }
            return core_1.RequestQueueV1.open(null, { config: this.config });
        }
        return core_1.RequestQueue.open(null, { config: this.config });
    }
    requestMatchesEnqueueStrategy(request) {
        const { url, loadedUrl } = request;
        // eslint-disable-next-line dot-notation -- private access
        const strategy = request['enqueueStrategy'];
        // No strategy set, so we assume it matches, or it was added outside of enqueueLinks
        if (!strategy) {
            return true;
        }
        // If we somehow don't have a loadedUrl, we can't check the strategy anyways, assume it matches
        if (!loadedUrl) {
            return true;
        }
        const baseUrl = new URL(url);
        const loadedBaseUrl = new URL(loadedUrl);
        switch (strategy) {
            case core_1.EnqueueStrategy.SameHostname: {
                return baseUrl.hostname === loadedBaseUrl.hostname;
            }
            case core_1.EnqueueStrategy.SameDomain: {
                const baseUrlHostname = (0, tldts_1.getDomain)(baseUrl.hostname, { mixedInputs: false });
                if (baseUrlHostname) {
                    const loadedBaseUrlHostname = (0, tldts_1.getDomain)(loadedBaseUrl.hostname, { mixedInputs: false });
                    return baseUrlHostname === loadedBaseUrlHostname;
                }
                // Can happen for IPs, we just check like same origin
                return baseUrl.origin === loadedBaseUrl.origin;
            }
            case core_1.EnqueueStrategy.SameOrigin: {
                // Same as hostname, but also checks protocol
                return baseUrl.origin === loadedBaseUrl.origin;
            }
            case core_1.EnqueueStrategy.All:
            default: {
                return baseUrl.protocol === 'http:' || baseUrl.protocol === 'https:';
            }
        }
    }
}
exports.BasicCrawler = BasicCrawler;
Object.defineProperty(BasicCrawler, "CRAWLEE_STATE_KEY", {
    enumerable: true,
    configurable: true,
    writable: true,
    value: 'CRAWLEE_STATE'
});
Object.defineProperty(BasicCrawler, "optionsShape", {
    enumerable: true,
    configurable: true,
    writable: true,
    value: {
        requestList: ow_1.default.optional.object.validate(core_1.validators.requestList),
        requestQueue: ow_1.default.optional.object.validate(core_1.validators.requestQueue),
        // Subclasses override this function instead of passing it
        // in constructor, so this validation needs to apply only
        // if the user creates an instance of BasicCrawler directly.
        requestHandler: ow_1.default.optional.function,
        // TODO: remove in a future release
        handleRequestFunction: ow_1.default.optional.function,
        requestHandlerTimeoutSecs: ow_1.default.optional.number,
        // TODO: remove in a future release
        handleRequestTimeoutSecs: ow_1.default.optional.number,
        errorHandler: ow_1.default.optional.function,
        failedRequestHandler: ow_1.default.optional.function,
        // TODO: remove in a future release
        handleFailedRequestFunction: ow_1.default.optional.function,
        maxRequestRetries: ow_1.default.optional.number,
        sameDomainDelaySecs: ow_1.default.optional.number,
        maxSessionRotations: ow_1.default.optional.number,
        maxRequestsPerCrawl: ow_1.default.optional.number,
        autoscaledPoolOptions: ow_1.default.optional.object,
        sessionPoolOptions: ow_1.default.optional.object,
        useSessionPool: ow_1.default.optional.boolean,
        statusMessageLoggingInterval: ow_1.default.optional.number,
        statusMessageCallback: ow_1.default.optional.function,
        retryOnBlocked: ow_1.default.optional.boolean,
        respectRobotsTxtFile: ow_1.default.optional.boolean,
        onSkippedRequest: ow_1.default.optional.function,
        httpClient: ow_1.default.optional.object,
        // AutoscaledPool shorthands
        minConcurrency: ow_1.default.optional.number,
        maxConcurrency: ow_1.default.optional.number,
        maxRequestsPerMinute: ow_1.default.optional.number.integerOrInfinite.positive.greaterThanOrEqual(1),
        keepAlive: ow_1.default.optional.boolean,
        // internal
        log: ow_1.default.optional.object,
        experiments: ow_1.default.optional.object,
        statisticsOptions: ow_1.default.optional.object,
    }
});
/**
 * Creates new {@link Router} instance that works based on request labels.
 * This instance can then serve as a {@link BasicCrawlerOptions.requestHandler|`requestHandler`} of our {@link BasicCrawler}.
 * Defaults to the {@link BasicCrawlingContext}.
 *
 * > Serves as a shortcut for using `Router.create<BasicCrawlingContext>()`.
 *
 * ```ts
 * import { BasicCrawler, createBasicRouter } from 'crawlee';
 *
 * const router = createBasicRouter();
 * router.addHandler('label-a', async (ctx) => {
 *    ctx.log.info('...');
 * });
 * router.addDefaultHandler(async (ctx) => {
 *    ctx.log.info('...');
 * });
 *
 * const crawler = new BasicCrawler({
 *     requestHandler: router,
 * });
 * await crawler.run();
 * ```
 */
function createBasicRouter(routes) {
    return core_1.Router.create(routes);
}
//# sourceMappingURL=basic-crawler.js.map