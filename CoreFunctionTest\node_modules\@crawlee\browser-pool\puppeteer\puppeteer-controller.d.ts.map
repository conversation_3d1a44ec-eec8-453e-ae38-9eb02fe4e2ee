{"version": 3, "file": "puppeteer-controller.d.ts", "sourceRoot": "", "sources": ["../../src/puppeteer/puppeteer-controller.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,KAAK,SAAS,MAAM,WAAW,CAAC;AACvC,OAAO,KAAK,KAAK,cAAc,MAAM,WAAW,CAAC;AAIjD,OAAO,EAAE,iBAAiB,EAAE,MAAM,wCAAwC,CAAC;AAI3E,MAAM,WAAW,uBAAwB,SAAQ,cAAc,CAAC,qBAAqB;IACjF,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,aAAa,CAAC,EAAE,MAAM,CAAC;CAC1B;AAID,qBAAa,mBAAoB,SAAQ,iBAAiB,CACtD,OAAO,SAAS,EAChB,cAAc,CAAC,aAAa,EAC5B,cAAc,CAAC,OAAO,EACtB,uBAAuB,CAC1B;IACG,qBAAqB,CAAC,QAAQ,EAAE,MAAM,GAAG,SAAS,EAAE,WAAW,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;cAiB9E,QAAQ,CAAC,cAAc,CAAC,EAAE,uBAAuB,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;cA4EhF,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;cAIvB,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;cAuBtB,WAAW,CAAC,IAAI,EAAE,cAAc,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;cAIzD,WAAW,CAAC,IAAI,EAAE,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;CAG3F"}