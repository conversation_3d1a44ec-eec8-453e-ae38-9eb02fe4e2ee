using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using BrowserCore;
using ProxyManager;
using FingerprintGenerator;
using InstanceManager;
using PerformanceMonitor;
using DataPersistence;

namespace DllTestConsole
{
    /// <summary>
    /// DLL模块测试控制台程序
    /// 功能说明：测试所有独立DLL模块的功能
    /// </summary>
    class Program
    {
        private static ILogger<Program>? _logger;

        static async Task Main(string[] args)
        {
            // 显示启动横幅
            ShowBanner();

            // 配置服务
            var services = new ServiceCollection();
            ConfigureServices(services);
            var serviceProvider = services.BuildServiceProvider();

            _logger = serviceProvider.GetRequiredService<ILogger<Program>>();

            try
            {
                _logger.LogInformation("🚀 开始测试所有DLL模块...");

                // 测试所有模块
                await TestAllModulesAsync(serviceProvider);

                _logger.LogInformation("✅ 所有模块测试完成！");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 测试过程中发生错误");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 配置服务
        /// </summary>
        private static void ConfigureServices(IServiceCollection services)
        {
            // 添加日志
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });
        }

        /// <summary>
        /// 测试所有模块
        /// </summary>
        private static async Task TestAllModulesAsync(IServiceProvider serviceProvider)
        {
            var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

            // 1. 测试数据持久化模块
            logger.LogInformation("\n📦 1. 测试 DataPersistence 模块...");
            await TestDataPersistenceAsync(serviceProvider);

            // 2. 测试指纹生成器模块
            logger.LogInformation("\n📦 2. 测试 FingerprintGenerator 模块...");
            await TestFingerprintGeneratorAsync(serviceProvider);

            // 3. 测试代理管理器模块
            logger.LogInformation("\n📦 3. 测试 ProxyManager 模块...");
            await TestProxyManagerAsync(serviceProvider);

            // 4. 测试实例管理器模块
            logger.LogInformation("\n📦 4. 测试 InstanceManager 模块...");
            await TestInstanceManagerAsync(serviceProvider);

            // 5. 测试性能监控模块
            logger.LogInformation("\n📦 5. 测试 PerformanceMonitor 模块...");
            await TestPerformanceMonitorAsync(serviceProvider);

            // 6. 测试浏览器核心模块
            logger.LogInformation("\n📦 6. 测试 BrowserCore 模块...");
            await TestBrowserCoreAsync(serviceProvider);
        }

        /// <summary>
        /// 测试数据持久化模块
        /// </summary>
        private static async Task TestDataPersistenceAsync(IServiceProvider serviceProvider)
        {
            var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
            
            try
            {
                using var dataManager = new DataManager(logger: logger);
                
                // 初始化
                var initResult = await dataManager.InitializeAsync();
                logger.LogInformation($"初始化结果: {initResult}");

                // 测试JSON保存和加载
                var testData = new { Name = "测试数据", Value = 123, Time = DateTime.Now };
                var saveResult = await dataManager.SaveToJsonAsync(testData, "test_data.json");
                logger.LogInformation($"JSON保存结果: {saveResult.Success} - {saveResult.Message}");

                var loadResult = await dataManager.LoadFromJsonAsync<object>("test_data.json");
                logger.LogInformation($"JSON加载结果: {loadResult.Success} - {loadResult.Message}");

                // 测试配置管理
                await dataManager.SaveConfigAsync("test_config", "测试配置值");
                var configValue = await dataManager.LoadConfigAsync<string>("test_config");
                logger.LogInformation($"配置测试: {configValue}");

                logger.LogInformation("✅ DataPersistence 模块测试完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "❌ DataPersistence 模块测试失败");
            }
        }

        /// <summary>
        /// 测试指纹生成器模块
        /// </summary>
        private static async Task TestFingerprintGeneratorAsync(IServiceProvider serviceProvider)
        {
            var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
            
            try
            {
                using var fingerprintManager = new FingerprintManager(logger);
                
                // 初始化
                var initResult = await fingerprintManager.InitializeAsync(10);
                logger.LogInformation($"初始化结果: {initResult}");

                // 生成随机指纹
                var fingerprint = fingerprintManager.GetRandomFingerprint();
                logger.LogInformation($"生成指纹: {fingerprint.Id} - {fingerprint.UserAgent}");

                // 生成指纹脚本
                var script = fingerprintManager.GenerateFingerprintScript(fingerprint);
                logger.LogInformation($"指纹脚本长度: {script.Length} 字符");

                // 验证指纹
                var validation = fingerprintManager.ValidateFingerprint(fingerprint);
                logger.LogInformation($"指纹验证: {validation.IsValid}");

                // 获取统计信息
                var stats = fingerprintManager.GetStats();
                logger.LogInformation($"指纹池大小: {stats.PoolSize}");

                logger.LogInformation("✅ FingerprintGenerator 模块测试完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "❌ FingerprintGenerator 模块测试失败");
            }
        }

        /// <summary>
        /// 测试代理管理器模块
        /// </summary>
        private static async Task TestProxyManagerAsync(IServiceProvider serviceProvider)
        {
            var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
            
            try
            {
                using var proxyManager = new ProxyPoolManager(logger);
                
                // 添加测试代理
                var testProxy = new ProxyInfo
                {
                    Host = "127.0.0.1",
                    Port = 8080,
                    Type = "HTTP",
                    Address = "127.0.0.1:8080"
                };
                proxyManager.AddProxy(testProxy);

                // 初始化（会尝试加载proxies.txt文件）
                var initResult = await proxyManager.InitializeAsync("proxies.txt");
                logger.LogInformation($"初始化结果: {initResult}");

                // 获取代理
                var proxy = proxyManager.GetNextProxy();
                logger.LogInformation($"获取代理: {proxy?.Address ?? "无可用代理"}");

                // 获取统计信息
                var stats = proxyManager.GetStats();
                logger.LogInformation($"代理统计 - 总数: {stats.TotalProxies}, 健康: {stats.HealthyProxies}");

                // 执行健康检查
                var healthCheck = await proxyManager.PerformHealthCheckAsync();
                logger.LogInformation($"健康检查: {healthCheck.Success} - {healthCheck.Message}");

                logger.LogInformation("✅ ProxyManager 模块测试完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "❌ ProxyManager 模块测试失败");
            }
        }

        /// <summary>
        /// 测试实例管理器模块
        /// </summary>
        private static async Task TestInstanceManagerAsync(IServiceProvider serviceProvider)
        {
            var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
            
            try
            {
                using var instanceManager = new InstancePoolManager(logger: logger);
                
                // 初始化
                var initResult = await instanceManager.InitializeAsync();
                logger.LogInformation($"初始化结果: {initResult}");

                // 注册测试实例
                var testInstance = new InstanceInfo
                {
                    Id = "test-instance-001",
                    Status = InstanceStatus.Active
                };
                
                var regResult = instanceManager.RegisterInstance(testInstance);
                logger.LogInformation($"实例注册: {regResult.Success} - {regResult.Message}");

                // 更新实例状态
                var updateResult = instanceManager.UpdateInstanceStatus("test-instance-001", InstanceStatus.Busy, "执行任务中");
                logger.LogInformation($"状态更新: {updateResult}");

                // 记录活动
                instanceManager.RecordActivity("test-instance-001", "导航", new { url = "https://example.com" });

                // 获取统计信息
                var stats = instanceManager.GetPoolStats();
                logger.LogInformation($"池统计 - 总数: {stats.TotalInstances}, 活跃: {stats.ActiveInstances}");

                // 注销实例
                var unregResult = instanceManager.UnregisterInstance("test-instance-001");
                logger.LogInformation($"实例注销: {unregResult}");

                logger.LogInformation("✅ InstanceManager 模块测试完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "❌ InstanceManager 模块测试失败");
            }
        }

        /// <summary>
        /// 测试性能监控模块
        /// </summary>
        private static async Task TestPerformanceMonitorAsync(IServiceProvider serviceProvider)
        {
            var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
            
            try
            {
                using var monitor = new SystemMonitor(logger: logger);
                
                // 等待一些性能数据收集
                await Task.Delay(2000);

                // 获取当前指标
                var metrics = monitor.GetCurrentMetrics();
                logger.LogInformation($"当前指标 - CPU: {metrics.CpuUsage:F1}%, 内存: {metrics.MemoryUsage:F1}%");

                // 获取历史数据
                var history = monitor.GetMetricsHistory(5);
                logger.LogInformation($"历史记录数量: {history.Count}");

                // 获取统计信息
                var stats = monitor.GetPerformanceStats(TimeSpan.FromMinutes(1));
                logger.LogInformation($"性能统计 - 样本数: {stats.SampleCount}");

                // 检查告警
                var alerts = monitor.CheckAlerts();
                logger.LogInformation($"告警数量: {alerts.Count}");

                // 获取系统信息
                var systemInfo = monitor.GetSystemInfo();
                logger.LogInformation($"系统信息 - 机器名: {systemInfo.MachineName}, 处理器数: {systemInfo.ProcessorCount}");

                logger.LogInformation("✅ PerformanceMonitor 模块测试完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "❌ PerformanceMonitor 模块测试失败");
            }
        }

        /// <summary>
        /// 测试浏览器核心模块
        /// </summary>
        private static async Task TestBrowserCoreAsync(IServiceProvider serviceProvider)
        {
            var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
            
            try
            {
                using var browserManager = new BrowserManager(logger);
                
                // 初始化
                var initResult = await browserManager.InitializeAsync();
                logger.LogInformation($"初始化结果: {initResult}");

                if (initResult)
                {
                    // 创建浏览器配置
                    var config = new BrowserConfig
                    {
                        Headless = true, // 无头模式测试
                        Args = new List<string> { "--no-sandbox", "--disable-dev-shm-usage" }
                    };

                    // 创建浏览器实例
                    var instanceId = await browserManager.CreateBrowserAsync(config);
                    logger.LogInformation($"浏览器实例创建: {instanceId ?? "失败"}");

                    if (!string.IsNullOrEmpty(instanceId))
                    {
                        // 导航测试
                        var navResult = await browserManager.NavigateAsync(instanceId, "https://www.baidu.com");
                        logger.LogInformation($"导航结果: {navResult.Success} - {navResult.Message}");

                        // 获取统计信息
                        var stats = browserManager.GetStats();
                        logger.LogInformation($"浏览器统计 - 总数: {stats.TotalInstances}, 活跃: {stats.ActiveInstances}");

                        // 关闭浏览器
                        var closeResult = await browserManager.CloseBrowserAsync(instanceId);
                        logger.LogInformation($"浏览器关闭: {closeResult}");
                    }
                }

                logger.LogInformation("✅ BrowserCore 模块测试完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "❌ BrowserCore 模块测试失败");
            }
        }

        /// <summary>
        /// 显示启动横幅
        /// </summary>
        private static void ShowBanner()
        {
            Console.WriteLine();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                    🧪 DLL模块测试程序                        ║");
            Console.WriteLine("║                     独立功能模块验证                          ║");
            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
            Console.WriteLine("║  测试模块:                                                    ║");
            Console.WriteLine("║    • BrowserCore.dll         - 浏览器核心管理                ║");
            Console.WriteLine("║    • ProxyManager.dll        - 代理池管理                    ║");
            Console.WriteLine("║    • FingerprintGenerator.dll - 指纹生成器                   ║");
            Console.WriteLine("║    • InstanceManager.dll     - 实例池管理                    ║");
            Console.WriteLine("║    • PerformanceMonitor.dll  - 性能监控                      ║");
            Console.WriteLine("║    • DataPersistence.dll     - 数据持久化                    ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.WriteLine();
        }
    }
}
