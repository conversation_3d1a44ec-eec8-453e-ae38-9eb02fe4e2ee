// 批量浏览器相关类型定义
// 功能：为WPF UI提供所有必需的类型定义

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading.Tasks;

namespace BatchBrowserUI.Models
{
    /// <summary>
    /// 代理信息类 - 管理代理服务器配置
    /// </summary>
    public class ProxyInfo
    {
        public string Host { get; set; } = string.Empty;        // 代理主机地址
        public int Port { get; set; }                           // 代理端口
        public string Username { get; set; } = string.Empty;    // 代理用户名
        public string Password { get; set; } = string.Empty;    // 代理密码
        public ProxyType Type { get; set; } = ProxyType.HTTP;   // 代理类型
        public bool IsHealthy { get; set; } = true;             // 代理健康状态
        public DateTime LastChecked { get; set; }               // 最后检查时间
        public string Address => $"{Host}:{Port}";              // 代理地址字符串
    }

    /// <summary>
    /// 代理类型枚举
    /// </summary>
    public enum ProxyType
    {
        HTTP,       // HTTP代理
        HTTPS,      // HTTPS代理
        SOCKS4,     // SOCKS4代理
        SOCKS5      // SOCKS5代理
    }

    /// <summary>
    /// 浏览器指纹信息
    /// </summary>
    public class Fingerprint
    {
        public string UserAgent { get; set; } = string.Empty;   // 用户代理字符串
        public string ViewportSize { get; set; } = "1920x1080"; // 视口大小
        public string Timezone { get; set; } = "UTC";           // 时区
        public string Language { get; set; } = "en-US";         // 语言
        public string Platform { get; set; } = "Win32";         // 平台信息
        public int ColorDepth { get; set; } = 24;               // 颜色深度
        public bool CookiesEnabled { get; set; } = true;        // Cookie启用状态
    }

    /// <summary>
    /// 浏览器实例数据模型
    /// </summary>
    public class BrowserInstance : IDisposable
    {
        public int Id { get; set; }                              // 实例唯一标识符
        public ProxyInfo? Proxy { get; set; }                    // 使用的代理信息
        public Fingerprint? Fingerprint { get; set; }            // 指纹信息
        public bool IsActive { get; set; } = true;               // 是否活跃状态
        public bool IsRunning { get; set; } = true;              // 是否正在运行
        public string ProxyUrl { get; set; } = string.Empty;     // 代理URL字符串
        public string ProxyAddress => Proxy?.Address ?? "direct"; // 代理地址
        public string CurrentUrl { get; set; } = string.Empty;   // 当前URL
        public DateTime CreatedAt { get; set; }                  // 创建时间
        public DateTime LastUsed { get; set; }                   // 最后使用时间
        public DateTime LastUpdated { get; set; }                // 最后更新时间
        public DateTime LastError { get; set; }                  // 最后错误时间
        public int UrlsProcessed { get; set; }                   // 已处理URL数量
        public int ErrorCount { get; set; }                      // 错误次数
        public double AverageProcessingTime { get; set; }        // 平均处理时间

        public BrowserInstance()
        {
            CreatedAt = DateTime.Now;
            LastUsed = DateTime.Now;
            LastUpdated = DateTime.Now;
        }
        
        /// <summary>
        /// 释放浏览器实例资源
        /// </summary>
        public void Dispose()
        {
            // 清理浏览器实例相关资源
            IsActive = false;
            IsRunning = false;
            Console.WriteLine($"浏览器实例 {Id} 资源已释放");
        }
    }

    /// <summary>
    /// 浏览器配置类
    /// </summary>
    public class BrowserConfig
    {
        public int InstanceId { get; set; }                     // 实例ID
        public ProxyInfo? Proxy { get; set; }                   // 代理配置
        public Fingerprint? Fingerprint { get; set; }           // 指纹配置
        public string UserAgent { get; set; } = string.Empty;   // 用户代理
        public string ViewportSize { get; set; } = "1920x1080"; // 视口大小
        public string Timezone { get; set; } = "UTC";           // 时区
        public string Language { get; set; } = "en-US";         // 语言
    }

    /// <summary>
    /// 批量创建结果
    /// </summary>
    public class BatchCreationResult
    {
        public bool Success { get; set; }                       // 是否成功
        public string BatchId { get; set; } = string.Empty;     // 批次ID
        public List<BrowserInstanceInfo> Instances { get; set; } = new(); // 实例信息
        public string Error { get; set; } = string.Empty;       // 错误信息
    }

    /// <summary>
    /// 浏览器实例信息
    /// </summary>
    public class BrowserInstanceInfo
    {
        public int Id { get; set; }                             // 实例ID
        public string? Proxy { get; set; }                      // 代理地址
        public FingerprintInfo? Fingerprint { get; set; }       // 指纹信息
        public DateTime CreatedAt { get; set; }                 // 创建时间
        public DateTime LastUsed { get; set; }                  // 最后使用时间
        public bool IsActive { get; set; }                      // 是否活跃
    }

    /// <summary>
    /// 指纹信息
    /// </summary>
    public class FingerprintInfo
    {
        public string? UserAgent { get; set; }                  // 用户代理
        public ViewportInfo? Viewport { get; set; }             // 视口信息
        public string? Timezone { get; set; }                   // 时区
        public string? Locale { get; set; }                     // 区域设置
    }

    /// <summary>
    /// 视口信息
    /// </summary>
    public class ViewportInfo
    {
        public int Width { get; set; }                          // 宽度
        public int Height { get; set; }                         // 高度
    }

    /// <summary>
    /// 代理池管理器（简化版）
    /// </summary>
    public class ProxyPoolManager
    {
        private readonly List<ProxyInfo> _proxies = new();      // 代理列表

        /// <summary>
        /// 获取下一个健康的代理
        /// </summary>
        public async Task<ProxyInfo?> GetNextHealthyProxyAsync()
        {
            await Task.Delay(1); // 模拟异步操作
            return _proxies.FirstOrDefault(p => p.IsHealthy);
        }

        /// <summary>
        /// 开始健康检查
        /// </summary>
        public async Task StartHealthCheckAsync()
        {
            await Task.Delay(1); // 模拟异步操作
            Console.WriteLine("代理健康检查已启动");
        }

        /// <summary>
        /// 停止健康检查
        /// </summary>
        public async Task StopHealthCheckAsync()
        {
            await Task.Delay(1); // 模拟异步操作
            Console.WriteLine("代理健康检查已停止");
        }

        /// <summary>
        /// 获取代理数量
        /// </summary>
        public int GetProxyCount()
        {
            return _proxies.Count;
        }

        /// <summary>
        /// 报告代理问题
        /// </summary>
        public void ReportProxyIssue(ProxyInfo? proxy)
        {
            if (proxy != null)
            {
                proxy.IsHealthy = false;
                proxy.LastChecked = DateTime.Now;
                Console.WriteLine($"代理 {proxy.Address} 已标记为不健康");
            }
        }

        /// <summary>
        /// 获取代理统计信息
        /// </summary>
        public ProxyStats GetProxyStats()
        {
            return new ProxyStats
            {
                TotalProxies = _proxies.Count,
                HealthyProxies = _proxies.Count(p => p.IsHealthy),
                FailedProxies = _proxies.Count(p => !p.IsHealthy)
            };
        }
    }

    /// <summary>
    /// 代理统计信息
    /// </summary>
    public class ProxyStats
    {
        public int TotalProxies { get; set; }                   // 总代理数
        public int HealthyProxies { get; set; }                 // 健康代理数
        public int FailedProxies { get; set; }                  // 失效代理数
    }

    /// <summary>
    /// 浏览器实例池管理器（简化版）
    /// </summary>
    public class BrowserInstancePool
    {
        private readonly List<BrowserInstance> _instances = new(); // 实例列表

        /// <summary>
        /// 添加实例
        /// </summary>
        public async Task AddInstanceAsync(BrowserInstance instance)
        {
            await Task.Delay(1); // 模拟异步操作
            _instances.Add(instance);
            Console.WriteLine($"浏览器实例 {instance.Id} 已添加到池中");
        }

        /// <summary>
        /// 获取实例
        /// </summary>
        public BrowserInstance? GetInstance(int id)
        {
            return _instances.FirstOrDefault(i => i.Id == id);
        }

        /// <summary>
        /// 移除实例
        /// </summary>
        public void RemoveInstance(int id)
        {
            var instance = _instances.FirstOrDefault(i => i.Id == id);
            if (instance != null)
            {
                _instances.Remove(instance);
                Console.WriteLine($"浏览器实例 {id} 已从池中移除");
            }
        }

        /// <summary>
        /// 获取所有实例
        /// </summary>
        public List<BrowserInstance> GetAllInstances()
        {
            return _instances.ToList();
        }
    }

    /// <summary>
    /// 指纹生成器（简化版）
    /// </summary>
    public class FingerprintGenerator
    {
        private readonly Random _random = new();               // 随机数生成器

        /// <summary>
        /// 生成随机指纹
        /// </summary>
        public Fingerprint GenerateFingerprint()
        {
            var userAgents = new[]
            {
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36"
            };

            var viewports = new[] { "1920x1080", "1366x768", "1440x900", "1280x720" };
            var timezones = new[] { "UTC", "America/New_York", "Europe/London", "Asia/Shanghai" };
            var languages = new[] { "en-US", "en-GB", "zh-CN", "es-ES" };

            return new Fingerprint
            {
                UserAgent = userAgents[_random.Next(userAgents.Length)],
                ViewportSize = viewports[_random.Next(viewports.Length)],
                Timezone = timezones[_random.Next(timezones.Length)],
                Language = languages[_random.Next(languages.Length)],
                Platform = "Win32",
                ColorDepth = 24,
                CookiesEnabled = true
            };
        }
    }

    /// <summary>
    /// 性能监控器（简化版）
    /// </summary>
    public class PerformanceMonitor
    {
        private bool _isMonitoring = false;                     // 监控状态

        /// <summary>
        /// 开始监控
        /// </summary>
        public void StartMonitoring()
        {
            _isMonitoring = true;
            Console.WriteLine("性能监控已启动");
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            _isMonitoring = false;
            Console.WriteLine("性能监控已停止");
        }

        /// <summary>
        /// 获取当前统计信息
        /// </summary>
        public PerformanceStats GetCurrentStats()
        {
            return new PerformanceStats
            {
                CpuUsage = _isMonitoring ? 45.5 : 0,
                MemoryUsage = _isMonitoring ? 60.2 : 0,
                NetworkUsage = _isMonitoring ? 15.8 : 0
            };
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopMonitoring();
        }
    }

    /// <summary>
    /// 性能统计信息
    /// </summary>
    public class PerformanceStats
    {
        public double CpuUsage { get; set; }                    // CPU使用率
        public double MemoryUsage { get; set; }                 // 内存使用率
        public double NetworkUsage { get; set; }                // 网络使用率
    }

    /// <summary>
    /// 批量操作统计信息
    /// </summary>
    public class BatchOperationStats
    {
        public int TotalInstances { get; set; }                 // 总实例数
        public int ActiveInstances { get; set; }                // 活跃实例数
        public int IdleInstances { get; set; }                  // 空闲实例数
        public int TotalProxies { get; set; }                   // 总代理数
        public int HealthyProxies { get; set; }                 // 健康代理数
        public int FailedProxies { get; set; }                  // 失效代理数
        public long TotalUrlsProcessed { get; set; }            // 总处理URL数
        public double AverageProcessingTime { get; set; }       // 平均处理时间
        public double CpuUsage { get; set; }                    // CPU使用率
        public double MemoryUsage { get; set; }                 // 内存使用率
        public double NetworkUsage { get; set; }                // 网络使用率
    }
}
