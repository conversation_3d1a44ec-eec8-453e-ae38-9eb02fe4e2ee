using Microsoft.Playwright;
using Microsoft.AspNetCore.SignalR;
using System.Collections.Concurrent;
using Newtonsoft.Json;
using BatchBrowserEngine.Hubs;
using BatchBrowserEngine.Models;

namespace BatchBrowserEngine.Services
{
    /// <summary>
    /// 批量浏览器引擎服务 - C#版本
    /// 功能说明：提供大规模批量浏览器管理，支持隔离目录、指纹分配、代理轮换
    /// </summary>
    public class BrowserEngineService : IDisposable
    {
        private readonly ILogger<BrowserEngineService> _logger;
        private readonly IHubContext<BrowserHub> _hubContext;
        private readonly FingerprintService _fingerprintService;
        private readonly ProxyService _proxyService;
        
        // 浏览器实例管理
        private readonly ConcurrentDictionary<string, BrowserInstanceInfo> _browserInstances;
        private readonly ConcurrentDictionary<string, IBrowser> _browsers;
        private IPlaywright? _playwright;
        
        // 配置参数
        private readonly int _maxConcurrency = 50;  // 最大并发浏览器数量
        private readonly string _instancesDir = "./browser_instances";  // 浏览器实例根目录
        private string? _chromeExecutablePath;  // Chrome可执行文件路径

        public BrowserEngineService(
            ILogger<BrowserEngineService> logger,
            IHubContext<BrowserHub> hubContext,
            FingerprintService fingerprintService,
            ProxyService proxyService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _hubContext = hubContext ?? throw new ArgumentNullException(nameof(hubContext));
            _fingerprintService = fingerprintService ?? throw new ArgumentNullException(nameof(fingerprintService));
            _proxyService = proxyService ?? throw new ArgumentNullException(nameof(proxyService));
            
            _browserInstances = new ConcurrentDictionary<string, BrowserInstanceInfo>();
            _browsers = new ConcurrentDictionary<string, IBrowser>();
            
            _logger.LogInformation("🚀 批量浏览器引擎服务初始化完成");
        }

        /// <summary>
        /// 初始化浏览器引擎
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("🔧 正在初始化Playwright浏览器引擎...");
                
                // 初始化Playwright
                _playwright = await Playwright.CreateAsync();
                
                // 检测Chrome路径
                await DetectChromePathAsync();
                
                // 创建实例根目录
                CreateInstancesDirectory();
                
                // 初始化指纹池
                await _fingerprintService.InitializeAsync();
                
                // 初始化代理池
                await _proxyService.InitializeAsync();
                
                _logger.LogInformation("✅ 浏览器引擎初始化成功");
                _logger.LogInformation($"⚡ 最大并发: {_maxConcurrency}个浏览器实例");
                _logger.LogInformation($"🔒 支持代理轮换和指纹分配");
                _logger.LogInformation($"📁 支持隔离目录和数据管理");
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 浏览器引擎初始化失败");
                return false;
            }
        }

        /// <summary>
        /// 创建浏览器实例
        /// </summary>
        /// <param name="count">创建数量</param>
        /// <param name="targetUrl">目标URL</param>
        /// <returns>创建结果</returns>
        public async Task<CreateInstancesResult> CreateInstancesAsync(int count, string? targetUrl = null)
        {
            try
            {
                _logger.LogInformation($"🚀 开始创建 {count} 个浏览器实例...");
                
                if (_browserInstances.Count + count > _maxConcurrency)
                {
                    var message = $"超出最大并发限制 {_maxConcurrency}，当前实例数: {_browserInstances.Count}";
                    _logger.LogWarning($"⚠️ {message}");
                    return new CreateInstancesResult { Success = false, Message = message };
                }

                var createdInstances = new List<BrowserInstanceInfo>();
                var tasks = new List<Task>();

                for (int i = 0; i < count; i++)
                {
                    var instanceId = GenerateInstanceId();
                    tasks.Add(CreateSingleInstanceAsync(instanceId, targetUrl, createdInstances));
                }

                await Task.WhenAll(tasks);
                
                // 通知客户端实例创建完成
                await NotifyInstancesCreated(createdInstances);
                
                _logger.LogInformation($"✅ 成功创建 {createdInstances.Count} 个浏览器实例");
                
                return new CreateInstancesResult 
                { 
                    Success = true, 
                    Message = $"成功创建 {createdInstances.Count} 个实例",
                    Instances = createdInstances
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 创建浏览器实例失败");
                return new CreateInstancesResult { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// 创建单个浏览器实例
        /// </summary>
        private async Task CreateSingleInstanceAsync(string instanceId, string? targetUrl, List<BrowserInstanceInfo> createdInstances)
        {
            try
            {
                // 获取指纹配置
                var fingerprint = await _fingerprintService.GetRandomFingerprintAsync();
                
                // 获取代理配置
                var proxy = await _proxyService.GetNextProxyAsync();
                
                // 创建实例数据目录
                var dataDir = CreateInstanceDataDirectory(instanceId);
                
                // 配置浏览器启动选项
                var launchOptions = new BrowserTypeLaunchOptions
                {
                    Headless = false,  // 可配置
                    ExecutablePath = _chromeExecutablePath,
                    Args = GenerateChromeArgs(dataDir, proxy?.Address),
                    UserDataDir = dataDir
                };

                // 启动浏览器
                var browser = await _playwright!.Chromium.LaunchAsync(launchOptions);
                
                // 创建页面并注入指纹
                var page = await browser.NewPageAsync();
                await InjectFingerprintAsync(page, fingerprint);
                
                // 导航到目标URL
                if (!string.IsNullOrEmpty(targetUrl))
                {
                    await page.GotoAsync(targetUrl);
                }

                // 创建实例信息
                var instanceInfo = new BrowserInstanceInfo
                {
                    Id = instanceId,
                    Status = "active",
                    ProxyServer = proxy?.Address ?? "direct",
                    CurrentUrl = targetUrl ?? "about:blank",
                    CreatedAt = DateTime.Now,
                    Fingerprint = fingerprint,
                    DataDirectory = dataDir
                };

                // 保存实例
                _browserInstances.TryAdd(instanceId, instanceInfo);
                _browsers.TryAdd(instanceId, browser);
                
                lock (createdInstances)
                {
                    createdInstances.Add(instanceInfo);
                }
                
                _logger.LogInformation($"✅ 实例 {instanceId} 创建成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ 创建实例 {instanceId} 失败");
            }
        }

        /// <summary>
        /// 获取所有浏览器实例
        /// </summary>
        public Task<List<BrowserInstanceInfo>> GetInstancesAsync()
        {
            var instances = _browserInstances.Values.ToList();
            _logger.LogInformation($"📊 当前活跃实例数: {instances.Count}");
            return Task.FromResult(instances);
        }

        /// <summary>
        /// 删除浏览器实例
        /// </summary>
        public async Task<bool> DeleteInstanceAsync(string instanceId)
        {
            try
            {
                if (_browsers.TryRemove(instanceId, out var browser))
                {
                    await browser.CloseAsync();
                    await browser.DisposeAsync();
                }

                if (_browserInstances.TryRemove(instanceId, out var instance))
                {
                    // 清理数据目录（可选）
                    // Directory.Delete(instance.DataDirectory, true);
                    
                    _logger.LogInformation($"🗑️ 实例 {instanceId} 已删除");
                    
                    // 通知客户端
                    await _hubContext.Clients.All.SendAsync("InstanceDeleted", instanceId);
                    
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ 删除实例 {instanceId} 失败");
                return false;
            }
        }

        /// <summary>
        /// 生成实例ID
        /// </summary>
        private string GenerateInstanceId()
        {
            return $"browser-{DateTime.Now.Ticks:X}-{Random.Shared.Next(1000, 9999):D3}";
        }

        /// <summary>
        /// 检测Chrome路径
        /// </summary>
        private async Task DetectChromePathAsync()
        {
            await Task.Run(() =>
            {
                _logger.LogInformation("🔍 开始检测本地Chrome浏览器...");
                
                var possiblePaths = new[]
                {
                    @"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    @"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    @"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
                };

                foreach (var path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        _chromeExecutablePath = path;
                        _logger.LogInformation($"✅ 发现本地Chrome: {path}");
                        return;
                    }
                }
                
                _logger.LogWarning("⚠️ 未找到本地Chrome，将使用Playwright内置浏览器");
            });
        }

        /// <summary>
        /// 创建实例根目录
        /// </summary>
        private void CreateInstancesDirectory()
        {
            if (!Directory.Exists(_instancesDir))
            {
                Directory.CreateDirectory(_instancesDir);
                _logger.LogInformation($"📁 创建实例根目录: {_instancesDir}");
            }
        }

        /// <summary>
        /// 创建实例数据目录
        /// </summary>
        private string CreateInstanceDataDirectory(string instanceId)
        {
            var dataDir = Path.Combine(_instancesDir, instanceId);
            if (!Directory.Exists(dataDir))
            {
                Directory.CreateDirectory(dataDir);
            }
            return dataDir;
        }

        /// <summary>
        /// 生成Chrome启动参数
        /// </summary>
        private string[] GenerateChromeArgs(string dataDir, string? proxyServer)
        {
            var args = new List<string>
            {
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                $"--user-data-dir={dataDir}"
            };

            if (!string.IsNullOrEmpty(proxyServer))
            {
                args.Add($"--proxy-server={proxyServer}");
            }

            return args.ToArray();
        }

        /// <summary>
        /// 注入指纹脚本
        /// </summary>
        private async Task InjectFingerprintAsync(IPage page, FingerprintInfo fingerprint)
        {
            try
            {
                // 注入用户代理
                await page.SetUserAgentAsync(fingerprint.UserAgent);
                
                // 注入视口大小
                await page.SetViewportSizeAsync(fingerprint.ViewportWidth, fingerprint.ViewportHeight);
                
                // 注入其他指纹脚本
                var fingerprintScript = _fingerprintService.GenerateFingerprintScript(fingerprint);
                await page.AddInitScriptAsync(fingerprintScript);
                
                _logger.LogDebug($"🎭 指纹注入完成: {fingerprint.UserAgent}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 指纹注入失败");
            }
        }

        /// <summary>
        /// 通知客户端实例创建完成
        /// </summary>
        private async Task NotifyInstancesCreated(List<BrowserInstanceInfo> instances)
        {
            try
            {
                await _hubContext.Clients.All.SendAsync("InstancesCreated", instances);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 通知客户端失败");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 关闭所有浏览器实例
                foreach (var browser in _browsers.Values)
                {
                    browser?.CloseAsync().Wait(5000);
                    browser?.DisposeAsync().AsTask().Wait(5000);
                }
                
                _browsers.Clear();
                _browserInstances.Clear();
                
                // 释放Playwright
                _playwright?.Dispose();
                
                _logger.LogInformation("🔄 浏览器引擎服务已释放资源");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 释放资源时发生错误");
            }
        }
    }
}
