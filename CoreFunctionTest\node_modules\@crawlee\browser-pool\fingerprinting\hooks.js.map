{"version": 3, "file": "hooks.js", "sourceRoot": "", "sources": ["../../src/fingerprinting/hooks.ts"], "names": [], "mappings": ";;AAaA,wEA2CC;AAKD,0DAaC;AAKD,4DA2BC;AApGD,uEAAmE;AACnE,oEAAgE;AAChE,mCAAqD;AAErD;;GAEG;AACH,SAAgB,8BAA8B,CAAC,WAAiD;IAC5F,MAAM,EACF,oBAAoB,EACpB,gBAAgB,EAChB,kBAAkB,EAAE,EAAE,2BAA2B,EAAE,GACtD,GAAG,WAAW,CAAC;IAEhB,OAAO,CAAC,OAAe,EAAE,aAA4B,EAAE,EAAE;QACrD,MAAM,EAAE,iBAAiB,EAAE,GAAG,aAAa,CAAC;QAC5C,MAAM,QAAQ,GAAI,aAAa,CAAC,OAAsC,EAAE,EAAE,IAAI,aAAa,CAAC,QAAQ,CAAC;QACrG,MAAM,EAAE,aAAa,EAAE,GAA2B,aAAa,CAAC;QAEhE,oGAAoG;QACpG,MAAM,gCAAgC,GAClC,2BAA2B,IAAI,IAAA,kCAA0B,EAAC,aAAa,CAAC,CAAC;QAC7E,IAAI,WAA0C,CAAC;QAE/C,IAAI,QAAQ,IAAI,gBAAgB,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9C,WAAW,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;QAClD,CAAC;aAAM,IAAI,QAAQ,EAAE,CAAC;YAClB,WAAW,GAAG,oBAAqB,CAAC,cAAc,CAAC,gCAAgC,CAAC,CAAC;YACrF,gBAAgB,EAAE,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACJ,WAAW,GAAG,oBAAqB,CAAC,cAAc,CAAC,gCAAgC,CAAC,CAAC;QACzF,CAAC;QAED,aAAa,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;QAEtC,IAAI,iBAAiB,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QACD,MAAM,EACF,SAAS,EAAE,EAAE,SAAS,EAAE,EACxB,MAAM,GACT,GAAG,WAAW,CAAC,WAAY,CAAC;QAE7B,aAAa,CAAC,SAAS,GAAG,SAAS,CAAC;QAEpC,aAAa,CAAC,QAAQ,GAAG;YACrB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,MAAM,EAAE,MAAM,CAAC,MAAM;SACxB,CAAC;IACN,CAAC,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB;IACnC,OAAO,CAAC,OAAe,EAAE,iBAAoC,EAAE,WAAgB,EAAQ,EAAE;QACrF,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,iBAAiB,CAAC;QAC3D,MAAM,EAAE,WAAW,EAAE,GAAG,aAAa,CAAC,WAAY,CAAC;QAEnD,IAAI,aAAa,CAAC,iBAAiB,IAAI,aAAa,YAAY,oCAAgB,IAAI,WAAW,EAAE,CAAC;YAC9F,WAAW,CAAC,SAAS,KAArB,WAAW,CAAC,SAAS,GAAK,WAAW,CAAC,SAAS,CAAC,SAAS,EAAC;YAC1D,WAAW,CAAC,QAAQ,KAApB,WAAW,CAAC,QAAQ,GAAK;gBACrB,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,KAAK;gBAC/B,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,MAAM;aACpC,EAAC;QACN,CAAC;IACL,CAAC,CAAC;AACN,CAAC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CAAC,mBAAwC;IAC7E,OAAO,KAAK,EAAE,IAAS,EAAE,iBAAoC,EAAiB,EAAE;QAC5E,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,iBAAiB,CAAC;QAC3D,MAAM,WAAW,GAAG,aAAa,CAAC,WAAY,CAAC;QAE/C,0GAA0G;QAC1G,mEAAmE;QACnE,IAAI,aAAa,YAAY,oCAAgB,EAAE,CAAC;YAC5C,MAAM,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,GAAG,aAAa,CAAC;YAEnE,IAAI,qBAAqB,EAAE,CAAC;gBACxB,iHAAiH;gBACjH,OAAO;YACX,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC/B,MAAM,mBAAmB,CAAC,6BAA6B,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAE9E,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACrB,4BAA4B;gBAC5B,sFAAsF;gBACtF,aAAa,CAAC,MAAM,CAAC,EAAE,qBAAqB,EAAE,IAAI,EAAE,CAAC,CAAC;YAC1D,CAAC;QACL,CAAC;aAAM,IAAI,aAAa,YAAY,kCAAe,EAAE,CAAC;YAClD,MAAM,mBAAmB,CAAC,4BAA4B,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAC9E,CAAC;IACL,CAAC,CAAC;AACN,CAAC"}