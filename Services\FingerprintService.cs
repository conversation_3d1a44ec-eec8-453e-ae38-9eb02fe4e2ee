using System.Collections.Concurrent;
using BatchBrowserEngine.Models;

namespace BatchBrowserEngine.Services
{
    /// <summary>
    /// 指纹服务 - 生成和管理浏览器指纹
    /// 功能说明：动态生成唯一指纹，防止检测，支持Canvas、WebGL、Audio指纹修改
    /// </summary>
    public class FingerprintService
    {
        private readonly ILogger<FingerprintService> _logger;
        private readonly ConcurrentQueue<FingerprintInfo> _fingerprintPool;
        private readonly Random _random;
        
        // 指纹配置池大小
        private const int FINGERPRINT_POOL_SIZE = 100;

        public FingerprintService(ILogger<FingerprintService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _fingerprintPool = new ConcurrentQueue<FingerprintInfo>();
            _random = new Random();
        }

        /// <summary>
        /// 初始化指纹池
        /// </summary>
        public async Task InitializeAsync()
        {
            await Task.Run(() =>
            {
                _logger.LogInformation("🎭 开始生成指纹配置池...");
                
                for (int i = 0; i < FINGERPRINT_POOL_SIZE; i++)
                {
                    var fingerprint = GenerateRandomFingerprint();
                    _fingerprintPool.Enqueue(fingerprint);
                }
                
                _logger.LogInformation($"✅ 指纹池生成完成，共 {FINGERPRINT_POOL_SIZE} 个指纹配置");
            });
        }

        /// <summary>
        /// 获取随机指纹
        /// </summary>
        public async Task<FingerprintInfo> GetRandomFingerprintAsync()
        {
            return await Task.Run(() =>
            {
                if (_fingerprintPool.TryDequeue(out var fingerprint))
                {
                    // 重新生成一个新的指纹补充池子
                    var newFingerprint = GenerateRandomFingerprint();
                    _fingerprintPool.Enqueue(newFingerprint);
                    
                    return fingerprint;
                }
                
                // 如果池子空了，直接生成一个新的
                return GenerateRandomFingerprint();
            });
        }

        /// <summary>
        /// 生成随机指纹配置
        /// </summary>
        private FingerprintInfo GenerateRandomFingerprint()
        {
            var userAgents = new[]
            {
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
            };

            var resolutions = new[]
            {
                (1920, 1080), (1366, 768), (1536, 864), (1440, 900), (1280, 720),
                (1600, 900), (1024, 768), (1280, 1024), (1680, 1050), (1920, 1200)
            };

            var languages = new[] { "zh-CN", "en-US", "en-GB", "ja-JP", "ko-KR" };
            var timezones = new[] { "Asia/Shanghai", "America/New_York", "Europe/London", "Asia/Tokyo", "Asia/Seoul" };
            
            var resolution = resolutions[_random.Next(resolutions.Length)];
            
            return new FingerprintInfo
            {
                Id = Guid.NewGuid().ToString(),
                UserAgent = userAgents[_random.Next(userAgents.Length)],
                ViewportWidth = resolution.Item1,
                ViewportHeight = resolution.Item2,
                ScreenWidth = resolution.Item1,
                ScreenHeight = resolution.Item2,
                Language = languages[_random.Next(languages.Length)],
                Timezone = timezones[_random.Next(timezones.Length)],
                Platform = "Win32",
                HardwareConcurrency = _random.Next(4, 17), // 4-16核心
                DeviceMemory = new[] { 4, 8, 16, 32 }[_random.Next(4)], // GB
                ColorDepth = 24,
                PixelDepth = 24,
                WebGLVendor = "Google Inc. (Intel)",
                WebGLRenderer = "ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)",
                CanvasFingerprint = GenerateCanvasFingerprint(),
                AudioFingerprint = GenerateAudioFingerprint(),
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// 生成Canvas指纹
        /// </summary>
        private string GenerateCanvasFingerprint()
        {
            // 生成随机的Canvas指纹哈希
            var bytes = new byte[16];
            _random.NextBytes(bytes);
            return Convert.ToHexString(bytes).ToLower();
        }

        /// <summary>
        /// 生成Audio指纹
        /// </summary>
        private string GenerateAudioFingerprint()
        {
            // 生成随机的Audio指纹哈希
            var bytes = new byte[16];
            _random.NextBytes(bytes);
            return Convert.ToHexString(bytes).ToLower();
        }

        /// <summary>
        /// 生成指纹注入脚本
        /// </summary>
        public string GenerateFingerprintScript(FingerprintInfo fingerprint)
        {
            return $@"
// 指纹注入脚本 - 防检测
(() => {{
    // 覆盖navigator属性
    Object.defineProperty(navigator, 'language', {{
        get: () => '{fingerprint.Language}'
    }});
    
    Object.defineProperty(navigator, 'languages', {{
        get: () => ['{fingerprint.Language}']
    }});
    
    Object.defineProperty(navigator, 'platform', {{
        get: () => '{fingerprint.Platform}'
    }});
    
    Object.defineProperty(navigator, 'hardwareConcurrency', {{
        get: () => {fingerprint.HardwareConcurrency}
    }});
    
    Object.defineProperty(navigator, 'deviceMemory', {{
        get: () => {fingerprint.DeviceMemory}
    }});

    // 覆盖screen属性
    Object.defineProperty(screen, 'width', {{
        get: () => {fingerprint.ScreenWidth}
    }});
    
    Object.defineProperty(screen, 'height', {{
        get: () => {fingerprint.ScreenHeight}
    }});
    
    Object.defineProperty(screen, 'colorDepth', {{
        get: () => {fingerprint.ColorDepth}
    }});
    
    Object.defineProperty(screen, 'pixelDepth', {{
        get: () => {fingerprint.PixelDepth}
    }});

    // 时区设置
    const originalDateTimeFormat = Intl.DateTimeFormat;
    Intl.DateTimeFormat = function(...args) {{
        if (args.length === 0) {{
            args = ['{fingerprint.Language}'];
        }}
        return new originalDateTimeFormat(...args);
    }};

    // WebGL指纹修改
    const getParameter = WebGLRenderingContext.prototype.getParameter;
    WebGLRenderingContext.prototype.getParameter = function(parameter) {{
        if (parameter === 37445) {{ // UNMASKED_VENDOR_WEBGL
            return '{fingerprint.WebGLVendor}';
        }}
        if (parameter === 37446) {{ // UNMASKED_RENDERER_WEBGL
            return '{fingerprint.WebGLRenderer}';
        }}
        return getParameter.call(this, parameter);
    }};

    // Canvas指纹修改
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function(...args) {{
        const result = originalToDataURL.apply(this, args);
        // 添加轻微的随机噪声
        return result.replace(/data:image\/png;base64,/, 'data:image/png;base64,{fingerprint.CanvasFingerprint}');
    }};

    // Audio指纹修改
    const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
    AudioContext.prototype.createAnalyser = function() {{
        const analyser = originalCreateAnalyser.call(this);
        const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
        analyser.getFloatFrequencyData = function(array) {{
            originalGetFloatFrequencyData.call(this, array);
            // 添加轻微的随机噪声
            for (let i = 0; i < array.length; i++) {{
                array[i] += (Math.random() - 0.5) * 0.0001;
            }}
        }};
        return analyser;
    }};

    // 移除webdriver标识
    Object.defineProperty(navigator, 'webdriver', {{
        get: () => undefined
    }});

    // 移除自动化检测
    delete window.chrome.runtime.onConnect;
    delete window.chrome.runtime.onMessage;
    
    console.log('🎭 指纹注入完成:', {{
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        hardwareConcurrency: navigator.hardwareConcurrency,
        deviceMemory: navigator.deviceMemory,
        screen: {{
            width: screen.width,
            height: screen.height,
            colorDepth: screen.colorDepth
        }}
    }});
}})();";
        }
    }

    /// <summary>
    /// 指纹信息模型
    /// </summary>
    public class FingerprintInfo
    {
        public string Id { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public int ViewportWidth { get; set; }
        public int ViewportHeight { get; set; }
        public int ScreenWidth { get; set; }
        public int ScreenHeight { get; set; }
        public string Language { get; set; } = string.Empty;
        public string Timezone { get; set; } = string.Empty;
        public string Platform { get; set; } = string.Empty;
        public int HardwareConcurrency { get; set; }
        public int DeviceMemory { get; set; }
        public int ColorDepth { get; set; }
        public int PixelDepth { get; set; }
        public string WebGLVendor { get; set; } = string.Empty;
        public string WebGLRenderer { get; set; } = string.Empty;
        public string CanvasFingerprint { get; set; } = string.Empty;
        public string AudioFingerprint { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }
}
