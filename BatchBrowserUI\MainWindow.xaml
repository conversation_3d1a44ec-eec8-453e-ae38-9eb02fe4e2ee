﻿<!-- filepath: d:\IIIIII\BatchBrowserUI\MainWindow.xaml -->
<Window x:Class="BatchBrowserUI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"        xmlns:local="clr-namespace:BatchBrowserUI"
        mc:Ignorable="d"
        Title="批量浏览器管理系统 - Chrome集成版" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize">
    
    <!-- 窗口资源定义 - 样式和模板 -->
    <Window.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF0D47A1"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#FFBDBDBD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 成功按钮样式 -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF4CAF50"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF388E3C"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#FF2E7D32"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 警告按钮样式 -->
        <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FFFF9800"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FFF57C00"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#FFE65100"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 危险按钮样式 -->
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FFF44336"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FFD32F2F"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#FFC62828"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 状态指示器样式 -->
        <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
            <Setter Property="Width" Value="12"/>
            <Setter Property="Height" Value="12"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>
    </Window.Resources>

    <!-- 主窗口布局 - 使用Grid进行区域划分 -->
    <Grid Background="#FFF5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- 标题栏 -->
            <RowDefinition Height="Auto"/> <!-- 控制面板 -->
            <RowDefinition Height="*"/>    <!-- 主内容区域 -->
            <RowDefinition Height="Auto"/> <!-- 状态栏 -->
        </Grid.RowDefinitions>

        <!-- 标题栏区域 -->
        <Border Grid.Row="0" Background="#FF1976D2" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- 系统图标和标题 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🚀" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <StackPanel>
                        <TextBlock Text="批量浏览器管理系统" 
                                 FontSize="18" FontWeight="Bold" 
                                 Foreground="White"/>
                        <TextBlock Text="Chrome集成增强版 - 支持50个并发实例" 
                                 FontSize="11" 
                                 Foreground="#FFBBDEFB"/>
                    </StackPanel>
                </StackPanel>

                <!-- 系统状态指示 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <StackPanel Orientation="Horizontal" Margin="0,0,15,0">
                        <Ellipse x:Name="EngineStatusIndicator" 
                               Style="{StaticResource StatusIndicatorStyle}"
                               Fill="#FFF44336"/> <!-- 红色表示未启动 -->
                        <TextBlock Text="引擎状态" Foreground="White" VerticalAlignment="Center"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal">
                        <Ellipse x:Name="ChromeStatusIndicator" 
                               Style="{StaticResource StatusIndicatorStyle}"
                               Fill="#FFFF9800"/> <!-- 橙色表示检测中 -->
                        <TextBlock Text="Chrome集成" Foreground="White" VerticalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 控制面板区域 -->
        <Border Grid.Row="1" Background="White" Padding="20,15" 
                BorderThickness="0,0,0,1" BorderBrush="#FFE0E0E0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 主要操作按钮 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button x:Name="StartEngineButton" 
                          Content="🚀 启动引擎" 
                          Style="{StaticResource SuccessButtonStyle}"
                          Click="StartEngineButton_Click"
                          ToolTip="启动Node.js批量浏览器引擎"/>
                    
                    <Button x:Name="StopEngineButton" 
                          Content="⏹️ 停止引擎" 
                          Style="{StaticResource DangerButtonStyle}"
                          Click="StopEngineButton_Click"
                          IsEnabled="False"
                          ToolTip="安全停止Node.js引擎"/>
                    
                    <Button x:Name="CreateInstancesButton" 
                          Content="🌐 创建实例" 
                          Style="{StaticResource ModernButtonStyle}"
                          Click="CreateInstancesButton_Click"
                          IsEnabled="False"
                          ToolTip="批量创建浏览器实例"/>
                    
                    <Button x:Name="ManageProxiesButton" 
                          Content="🔄 代理管理" 
                          Style="{StaticResource ModernButtonStyle}"
                          Click="ManageProxiesButton_Click"
                          ToolTip="管理代理服务器池"/>
                </StackPanel>

                <!-- 快速设置 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="并发数:" Margin="0,0,5,0" VerticalAlignment="Center"/>
                    <ComboBox x:Name="ConcurrencyComboBox" Width="60" SelectedIndex="2"
                            ToolTip="设置同时运行的浏览器实例数量">
                        <ComboBoxItem Content="1"/>
                        <ComboBoxItem Content="5"/>
                        <ComboBoxItem Content="10"/>
                        <ComboBoxItem Content="20"/>
                        <ComboBoxItem Content="50"/>
                    </ComboBox>
                    
                    <CheckBox x:Name="AntiDetectionCheckBox" 
                            Content="防检测模式" 
                            IsChecked="True" 
                            Margin="20,0,0,0"
                            ToolTip="启用高级防检测和指纹伪装"/>
                </StackPanel>
            </Grid>
        </Border>        <!-- 主内容区域 - 使用TabControl进行功能分组 -->
        <TabControl x:Name="MainTabControl" Grid.Row="2" Margin="20" Background="White">
            
            <!-- 实例管理选项卡 -->
            <TabItem Header="🌐 实例管理" FontSize="13">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 实例列表工具栏 -->
                    <Border Grid.Row="0" Background="#FFF8F9FA" Padding="10" 
                          CornerRadius="4" Margin="0,0,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                <TextBlock Text="活跃实例:" FontWeight="SemiBold" Margin="0,0,5,0"/>
                                <TextBlock x:Name="ActiveInstancesCountLabel" Text="{Binding SystemMonitor.ActiveBrowsers}" FontWeight="Bold" Foreground="#FF2196F3"/>
                                <TextBlock Text="/" Margin="5,0"/>
                                <TextBlock x:Name="TotalInstancesCountLabel" Text="{Binding SystemMonitor.TotalBrowsers}"/>
                                <TextBlock Text="个实例运行中" Margin="5,0,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button Content="🔄 刷新列表" 
                                      Style="{StaticResource ModernButtonStyle}"
                                      Click="RefreshInstancesButton_Click"/>
                                <Button Content="❌ 关闭所有" 
                                      Style="{StaticResource DangerButtonStyle}"
                                      Click="CloseAllInstancesButton_Click"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- 实例列表 -->
                    <DataGrid x:Name="BrowserInstancesDataGrid"
                            Grid.Row="1"
                            ItemsSource="{Binding BrowserInstances}"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            IsReadOnly="True"
                            GridLinesVisibility="Horizontal"
                            AlternatingRowBackground="#FFF8F9FA"
                            HeadersVisibility="Column">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="实例ID" Binding="{Binding Id}" Width="120"/>
                            <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
                            <DataGridTextColumn Header="代理地址" Binding="{Binding Proxy}" Width="150"/>
                            <DataGridTextColumn Header="当前URL" Binding="{Binding CurrentUrl}" Width="*"/>
                            <DataGridTextColumn Header="创建时间" Binding="{Binding CreatedAt}" Width="140"/>
                            <DataGridTemplateColumn Header="操作" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="📋" Width="25" Height="25" Margin="2"
                                                  ToolTip="查看详情" Click="ViewInstanceButton_Click"/>
                                            <Button Content="❌" Width="25" Height="25" Margin="2"
                                                  ToolTip="关闭实例" Click="CloseInstanceButton_Click"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- 批量操作面板 -->
                    <Border Grid.Row="2" Background="#FFF8F9FA" Padding="10" 
                          CornerRadius="4" Margin="0,10,0,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="批量操作:" VerticalAlignment="Center" 
                                     FontWeight="SemiBold" Margin="0,0,10,0"/>
                            <Button Content="批量导航" Style="{StaticResource ModernButtonStyle}"
                                  Click="BatchNavigateButton_Click"/>
                            <Button Content="批量执行脚本" Style="{StaticResource ModernButtonStyle}"
                                  Click="BatchExecuteButton_Click"/>
                            <Button Content="批量截图" Style="{StaticResource ModernButtonStyle}"
                                  Click="BatchScreenshotButton_Click"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </TabItem>

            <!-- 代理管理选项卡 -->
            <TabItem Header="🔄 代理管理" FontSize="13">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 代理统计面板 -->
                    <Border Grid.Row="0" Background="#FFF8F9FA" Padding="15" 
                          CornerRadius="4" Margin="0,0,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Text="总代理数" FontSize="11" Foreground="#FF666666" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalProxiesLabel" Text="0" FontSize="20" 
                                         FontWeight="Bold" Foreground="#FF2196F3" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Text="健康代理" FontSize="11" Foreground="#FF666666" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="HealthyProxiesLabel" Text="0" FontSize="20" 
                                         FontWeight="Bold" Foreground="#FF4CAF50" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Text="故障代理" FontSize="11" Foreground="#FF666666" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="FailedProxiesLabel" Text="0" FontSize="20" 
                                         FontWeight="Bold" Foreground="#FFF44336" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Grid.Column="3" Orientation="Horizontal">
                                <Button Content="🔄 健康检查" Style="{StaticResource ModernButtonStyle}"
                                      Click="ProxyHealthCheckButton_Click"/>
                                <Button Content="➕ 添加代理" Style="{StaticResource SuccessButtonStyle}"
                                      Click="AddProxyButton_Click"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- 代理列表 -->
                    <DataGrid x:Name="ProxiesDataGrid"
                            Grid.Row="1"
                            ItemsSource="{Binding ProxyList}"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            IsReadOnly="True"
                            GridLinesVisibility="Horizontal"
                            AlternatingRowBackground="#FFF8F9FA">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="代理地址" Binding="{Binding Address}" Width="200"/>
                            <DataGridTextColumn Header="端口" Binding="{Binding Port}" Width="80"/>
                            <DataGridTextColumn Header="类型" Binding="{Binding Type}" Width="80"/>
                            <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
                            <DataGridTextColumn Header="响应时间" Binding="{Binding ResponseTime}" Width="100"/>
                            <DataGridTextColumn Header="使用次数" Binding="{Binding UsageCount}" Width="80"/>
                            <DataGridTextColumn Header="最后检查" Binding="{Binding LastChecked}" Width="140"/>
                            <DataGridTemplateColumn Header="操作" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="🧪" Width="25" Height="25" Margin="2"
                                                  ToolTip="测试代理" Click="TestProxyButton_Click"/>
                                            <Button Content="❌" Width="25" Height="25" Margin="2"
                                                  ToolTip="删除代理" Click="RemoveProxyButton_Click"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- 系统监控选项卡 -->
            <TabItem Header="📊 系统监控" FontSize="13">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 性能指标面板 -->
                    <Grid Grid.Row="0" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- CPU使用率 -->
                        <Border Grid.Column="0" Background="White" CornerRadius="8" 
                              Padding="15" Margin="0,0,10,0" BorderThickness="1" BorderBrush="#FFE0E0E0">
                            <StackPanel>
                                <TextBlock Text="CPU使用率" FontSize="12" Foreground="#FF666666"/>
                                <TextBlock x:Name="CpuUsageLabel" Text="{Binding SystemMonitor.CpuUsageString}" FontSize="24"
                                         FontWeight="Bold" Foreground="#FF2196F3"/>
                                <ProgressBar x:Name="CpuUsageProgressBar" Height="6"
                                           Value="{Binding SystemMonitor.CpuUsage, Mode=OneWay}" Maximum="100"
                                           Background="#FFE0E0E0" Foreground="#FF2196F3" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>

                        <!-- 内存使用 -->
                        <Border Grid.Column="1" Background="White" CornerRadius="8" 
                              Padding="15" Margin="0,0,10,0" BorderThickness="1" BorderBrush="#FFE0E0E0">
                            <StackPanel>
                                <TextBlock Text="内存使用" FontSize="12" Foreground="#FF666666"/>
                                <TextBlock x:Name="MemoryUsageLabel" Text="{Binding SystemMonitor.MemoryUsageString}" FontSize="24"
                                         FontWeight="Bold" Foreground="#FF4CAF50"/>
                                <ProgressBar x:Name="MemoryUsageProgressBar" Height="6"
                                           Value="{Binding SystemMonitor.MemoryPercentage, Mode=OneWay}" Maximum="100"
                                           Background="#FFE0E0E0" Foreground="#FF4CAF50" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>

                        <!-- 运行时间 -->
                        <Border Grid.Column="2" Background="White" CornerRadius="8" 
                              Padding="15" Margin="0,0,10,0" BorderThickness="1" BorderBrush="#FFE0E0E0">
                            <StackPanel>
                                <TextBlock Text="运行时间" FontSize="12" Foreground="#FF666666"/>
                                <TextBlock x:Name="UptimeLabel" Text="{Binding SystemMonitor.UptimeString}" FontSize="24"
                                         FontWeight="Bold" Foreground="#FFFF9800"/>
                            </StackPanel>
                        </Border>

                        <!-- 成功率 -->
                        <Border Grid.Column="3" Background="White" CornerRadius="8" 
                              Padding="15" BorderThickness="1" BorderBrush="#FFE0E0E0">
                            <StackPanel>
                                <TextBlock Text="任务成功率" FontSize="12" Foreground="#FF666666"/>
                                <TextBlock x:Name="SuccessRateLabel" Text="{Binding SystemMonitor.SuccessRateString}" FontSize="24"
                                         FontWeight="Bold" Foreground="#FF9C27B0"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- 日志输出区域 -->
                    <Border Grid.Row="1" Background="White" CornerRadius="8" 
                          BorderThickness="1" BorderBrush="#FFE0E0E0" Padding="10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 日志标题栏 -->
                            <Grid Grid.Row="0" Margin="0,0,0,10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text="📋 系统日志" FontSize="14" FontWeight="SemiBold"/>
                                
                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <CheckBox x:Name="AutoScrollCheckBox" Content="自动滚动" IsChecked="True" 
                                            Margin="0,0,10,0"/>
                                    <Button Content="🗑️ 清空日志" Style="{StaticResource WarningButtonStyle}"
                                          Click="ClearLogButton_Click"/>
                                </StackPanel>
                            </Grid>

                            <!-- 日志内容 -->
                            <ScrollViewer Grid.Row="1" x:Name="LogScrollViewer"
                                        VerticalScrollBarVisibility="Auto"
                                        HorizontalScrollBarVisibility="Auto">
                                <ListBox x:Name="LogListBox"
                                       ItemsSource="{Binding SystemLogs}"
                                       Background="Transparent"
                                       BorderThickness="0"
                                       FontFamily="Consolas"
                                       FontSize="11"
                                       ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                       ScrollViewer.VerticalScrollBarVisibility="Auto"/>
                            </ScrollViewer>
                        </Grid>
                    </Border>
                </Grid>
            </TabItem>

            <!-- 设置选项卡 -->
            <TabItem Header="⚙️ 系统设置" FontSize="13">
                <ScrollViewer Margin="15">
                    <StackPanel>
                        <!-- Chrome设置组 -->
                        <GroupBox Header="Chrome浏览器设置" Margin="0,0,0,15" Padding="10">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                                    <TextBlock Text="Chrome路径:" VerticalAlignment="Center" Width="100"/>
                                    <TextBox x:Name="ChromePathTextBox" Width="400" IsReadOnly="True" 
                                           ToolTip="自动检测到的Chrome浏览器路径"/>
                                    <Button Content="🔍 重新检测" Style="{StaticResource ModernButtonStyle}"
                                          Click="DetectChromeButton_Click" Margin="10,0,0,0"/>
                                </StackPanel>

                                <CheckBox Grid.Row="1" x:Name="UseLocalChromeCheckBox" 
                                        Content="使用本地Chrome浏览器（推荐）" 
                                        IsChecked="True" Margin="0,0,0,10"
                                        ToolTip="使用本地安装的正式版Chrome，避免自动测试软件控制提示"/>

                                <CheckBox Grid.Row="2" x:Name="HeadlessCheckBox" 
                                        Content="无头模式运行" 
                                        IsChecked="False"
                                        ToolTip="在后台运行浏览器，不显示界面窗口"/>
                            </Grid>
                        </GroupBox>

                        <!-- 防检测设置组 -->
                        <GroupBox Header="防检测和指纹设置" Margin="0,0,0,15" Padding="10">
                            <StackPanel>
                                <CheckBox x:Name="EnableAntiDetectionCheckBox" 
                                        Content="启用高级防检测机制" 
                                        IsChecked="True" Margin="0,0,0,10"
                                        ToolTip="启用26个防检测参数和14步指纹伪装"/>

                                <CheckBox x:Name="RandomFingerprintCheckBox" 
                                        Content="随机生成浏览器指纹" 
                                        IsChecked="True" Margin="0,0,0,10"
                                        ToolTip="为每个实例生成独特的浏览器指纹"/>

                                <CheckBox x:Name="SpoofUserAgentCheckBox" 
                                        Content="伪装User-Agent" 
                                        IsChecked="True" Margin="0,0,0,10"
                                        ToolTip="使用随机的User-Agent字符串"/>

                                <CheckBox x:Name="SpoofViewportCheckBox" 
                                        Content="随机化视口尺寸" 
                                        IsChecked="True"
                                        ToolTip="随机设置浏览器窗口尺寸"/>
                            </StackPanel>
                        </GroupBox>

                        <!-- 性能设置组 -->
                        <GroupBox Header="性能和资源设置" Margin="0,0,0,15" Padding="10">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                                    <TextBlock Text="最大并发实例:" VerticalAlignment="Center" Width="120"/>
                                    <Slider x:Name="MaxConcurrencySlider" Width="200" 
                                          Minimum="1" Maximum="50" Value="10" 
                                          TickFrequency="5" IsSnapToTickEnabled="True"/>
                                    <TextBlock x:Name="MaxConcurrencyLabel" Text="10" 
                                             VerticalAlignment="Center" Margin="10,0,0,0" FontWeight="Bold"/>
                                </StackPanel>

                                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
                                    <TextBlock Text="页面超时时间:" VerticalAlignment="Center" Width="120"/>
                                    <Slider x:Name="PageTimeoutSlider" Width="200" 
                                          Minimum="10" Maximum="120" Value="30" 
                                          TickFrequency="10" IsSnapToTickEnabled="True"/>
                                    <TextBlock x:Name="PageTimeoutLabel" Text="30秒" 
                                             VerticalAlignment="Center" Margin="10,0,0,0" FontWeight="Bold"/>
                                </StackPanel>

                                <CheckBox Grid.Row="2" x:Name="EnableImagesCheckBox" 
                                        Content="加载图片（影响性能）" 
                                        IsChecked="False"
                                        ToolTip="禁用图片加载可以提高性能和降低带宽使用"/>
                            </Grid>
                        </GroupBox>

                        <!-- 保存设置按钮 -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button Content="💾 保存设置" Style="{StaticResource SuccessButtonStyle}"
                                  Click="SaveSettingsButton_Click"/>
                            <Button Content="🔄 重置默认" Style="{StaticResource WarningButtonStyle}"
                                  Click="ResetSettingsButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- 状态栏 -->
        <Border Grid.Row="3" Background="#FF263238" Padding="15,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- 状态信息 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock x:Name="StatusLabel" Text="{Binding EngineStatus}"
                             Foreground="White" VerticalAlignment="Center"/>
                    <TextBlock Text=" | " Foreground="#FF90A4AE" Margin="10,0"/>
                    <TextBlock x:Name="LastUpdateLabel" Text="{Binding SystemMonitor.LastUpdated, StringFormat='最后更新: {0:HH:mm:ss}'}"
                             Foreground="#FF90A4AE" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- 版本信息 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="版本 v2.0" Foreground="#FF90A4AE" VerticalAlignment="Center"/>
                    <TextBlock Text=" | " Foreground="#FF90A4AE" Margin="10,0"/>
                    <TextBlock Text="Chrome集成增强版" Foreground="#FF90A4AE" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
