using BatchBrowserEngine.Services;
using BatchBrowserEngine.Hubs;
using Microsoft.OpenApi.Models;

namespace BatchBrowserEngine
{
    /// <summary>
    /// 批量浏览器引擎主程序
    /// 功能说明：ASP.NET Core Web API + SignalR + Playwright的批量浏览器管理系统
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // 显示启动信息
            ShowStartupBanner();
            
            var builder = WebApplication.CreateBuilder(args);

            // 配置服务
            ConfigureServices(builder.Services, builder.Configuration);
            
            // 配置日志
            ConfigureLogging(builder.Logging);

            var app = builder.Build();

            // 配置中间件管道
            ConfigureMiddleware(app);
            
            // 初始化服务
            await InitializeServicesAsync(app);

            // 显示启动完成信息
            ShowStartupComplete(app);

            // 启动应用
            await app.RunAsync();
        }

        /// <summary>
        /// 配置服务
        /// </summary>
        private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            // 添加控制器
            services.AddControllers()
                .AddJsonOptions(options =>
                {
                    options.JsonSerializerOptions.PropertyNamingPolicy = null; // 保持原始属性名
                    options.JsonSerializerOptions.WriteIndented = true; // 格式化JSON输出
                });

            // 添加API文档
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "批量浏览器引擎 API",
                    Version = "v2.0",
                    Description = "基于C# + Playwright的批量浏览器管理系统",
                    Contact = new OpenApiContact
                    {
                        Name = "批量浏览器引擎",
                        Email = "<EMAIL>"
                    }
                });
                
                // 包含XML注释
                var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                if (File.Exists(xmlPath))
                {
                    c.IncludeXmlComments(xmlPath);
                }
            });

            // 添加SignalR
            services.AddSignalR(options =>
            {
                options.EnableDetailedErrors = true;
                options.KeepAliveInterval = TimeSpan.FromSeconds(15);
                options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
            });

            // 添加CORS
            services.AddCors(options =>
            {
                options.AddDefaultPolicy(policy =>
                {
                    policy.AllowAnyOrigin()
                          .AllowAnyMethod()
                          .AllowAnyHeader();
                });
                
                options.AddPolicy("SignalRPolicy", policy =>
                {
                    policy.AllowAnyMethod()
                          .AllowAnyHeader()
                          .AllowCredentials()
                          .SetIsOriginAllowed(origin => true);
                });
            });

            // 注册业务服务
            services.AddSingleton<FingerprintService>();
            services.AddSingleton<ProxyService>();
            services.AddSingleton<BrowserEngineService>();

            // 添加HTTP客户端
            services.AddHttpClient();

            // 添加内存缓存
            services.AddMemoryCache();

            // 添加健康检查
            services.AddHealthChecks();
        }

        /// <summary>
        /// 配置日志
        /// </summary>
        private static void ConfigureLogging(ILoggingBuilder logging)
        {
            logging.ClearProviders();
            logging.AddConsole(options =>
            {
                options.IncludeScopes = true;
                options.TimestampFormat = "[yyyy-MM-dd HH:mm:ss] ";
            });
            
            // 设置日志级别
            logging.SetMinimumLevel(LogLevel.Information);
            logging.AddFilter("Microsoft.AspNetCore", LogLevel.Warning);
            logging.AddFilter("Microsoft.AspNetCore.SignalR", LogLevel.Information);
        }

        /// <summary>
        /// 配置中间件管道
        /// </summary>
        private static void ConfigureMiddleware(WebApplication app)
        {
            // 开发环境配置
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "批量浏览器引擎 API v2.0");
                    c.RoutePrefix = ""; // 设置Swagger UI为根路径
                });
            }

            // 启用CORS
            app.UseCors();

            // 路由
            app.UseRouting();

            // 健康检查
            app.MapHealthChecks("/health");

            // API控制器
            app.MapControllers();

            // SignalR Hub
            app.MapHub<BrowserHub>("/browserhub").RequireCors("SignalRPolicy");

            // 默认路由 - 重定向到Swagger
            app.MapGet("/", () => Results.Redirect("/swagger"));
        }

        /// <summary>
        /// 初始化服务
        /// </summary>
        private static async Task InitializeServicesAsync(WebApplication app)
        {
            using var scope = app.Services.CreateScope();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
            
            try
            {
                logger.LogInformation("🔧 开始初始化服务...");

                // 初始化指纹服务
                var fingerprintService = scope.ServiceProvider.GetRequiredService<FingerprintService>();
                await fingerprintService.InitializeAsync();

                // 初始化代理服务
                var proxyService = scope.ServiceProvider.GetRequiredService<ProxyService>();
                await proxyService.InitializeAsync();

                // 初始化浏览器引擎
                var browserEngine = scope.ServiceProvider.GetRequiredService<BrowserEngineService>();
                var initSuccess = await browserEngine.InitializeAsync();
                
                if (!initSuccess)
                {
                    logger.LogError("❌ 浏览器引擎初始化失败");
                    throw new InvalidOperationException("浏览器引擎初始化失败");
                }

                logger.LogInformation("✅ 所有服务初始化完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "❌ 服务初始化失败");
                throw;
            }
        }

        /// <summary>
        /// 显示启动横幅
        /// </summary>
        private static void ShowStartupBanner()
        {
            Console.WriteLine();
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                    🚀 批量浏览器引擎 v2.0                    ║");
            Console.WriteLine("║                     C# + Playwright 版本                     ║");
            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
            Console.WriteLine("║  ✨ 特性:                                                    ║");
            Console.WriteLine("║    • 支持50+并发浏览器实例                                   ║");
            Console.WriteLine("║    • 智能代理轮换和健康检查                                   ║");
            Console.WriteLine("║    • 动态指纹生成和防检测                                     ║");
            Console.WriteLine("║    • RESTful API + SignalR实时通信                           ║");
            Console.WriteLine("║    • 隔离数据目录和完整生命周期管理                           ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.WriteLine();
        }

        /// <summary>
        /// 显示启动完成信息
        /// </summary>
        private static void ShowStartupComplete(WebApplication app)
        {
            var urls = app.Urls.Any() ? string.Join(", ", app.Urls) : "http://localhost:5000";
            
            Console.WriteLine();
            Console.WriteLine("🎉 批量浏览器引擎启动成功！");
            Console.WriteLine($"📡 HTTP API: {urls}");
            Console.WriteLine($"🔗 WebSocket: {urls.Replace("http", "ws")}/browserhub");
            Console.WriteLine($"📚 API文档: {urls}/swagger");
            Console.WriteLine($"❤️ 健康检查: {urls}/health");
            Console.WriteLine();
            Console.WriteLine("⚡ 系统已准备就绪，等待API调用...");
            Console.WriteLine("🔧 按 Ctrl+C 停止服务");
            Console.WriteLine();
        }
    }
}
