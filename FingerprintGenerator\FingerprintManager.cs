using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Security.Cryptography;
using System.Text;

namespace FingerprintGenerator
{
    /// <summary>
    /// 指纹管理器 - 独立DLL模块
    /// 功能说明：生成和管理浏览器指纹，防止检测，支持Canvas、WebGL、Audio指纹修改
    /// </summary>
    public class FingerprintManager : IDisposable
    {
        private readonly ILogger<FingerprintManager>? _logger;
        private readonly ConcurrentQueue<FingerprintInfo> _fingerprintPool;
        private readonly Random _random;
        
        // 指纹配置池大小
        private const int DEFAULT_POOL_SIZE = 100;

        public FingerprintManager(ILogger<FingerprintManager>? logger = null)
        {
            _logger = logger;
            _fingerprintPool = new ConcurrentQueue<FingerprintInfo>();
            _random = new Random();
        }

        /// <summary>
        /// 初始化指纹池
        /// </summary>
        /// <param name="poolSize">池大小</param>
        public async Task<bool> InitializeAsync(int poolSize = DEFAULT_POOL_SIZE)
        {
            try
            {
                _logger?.LogInformation("🎭 开始生成指纹配置池...");
                
                await Task.Run(() =>
                {
                    for (int i = 0; i < poolSize; i++)
                    {
                        var fingerprint = GenerateRandomFingerprint();
                        _fingerprintPool.Enqueue(fingerprint);
                    }
                });
                
                _logger?.LogInformation($"✅ 指纹池生成完成，共 {poolSize} 个指纹配置");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 指纹池初始化失败");
                return false;
            }
        }

        /// <summary>
        /// 获取随机指纹
        /// </summary>
        /// <returns>指纹信息</returns>
        public FingerprintInfo GetRandomFingerprint()
        {
            if (_fingerprintPool.TryDequeue(out var fingerprint))
            {
                // 重新生成一个新的指纹补充池子
                var newFingerprint = GenerateRandomFingerprint();
                _fingerprintPool.Enqueue(newFingerprint);
                
                _logger?.LogDebug($"🎭 分配指纹: {fingerprint.Id}");
                return fingerprint;
            }
            
            // 如果池子空了，直接生成一个新的
            _logger?.LogWarning("⚠️ 指纹池为空，生成临时指纹");
            return GenerateRandomFingerprint();
        }

        /// <summary>
        /// 生成指定数量的指纹
        /// </summary>
        /// <param name="count">数量</param>
        /// <returns>指纹列表</returns>
        public List<FingerprintInfo> GenerateFingerprints(int count)
        {
            var fingerprints = new List<FingerprintInfo>();
            
            for (int i = 0; i < count; i++)
            {
                fingerprints.Add(GetRandomFingerprint());
            }
            
            _logger?.LogInformation($"🎭 生成 {count} 个指纹");
            return fingerprints;
        }

        /// <summary>
        /// 生成自定义指纹
        /// </summary>
        /// <param name="config">指纹配置</param>
        /// <returns>指纹信息</returns>
        public FingerprintInfo GenerateCustomFingerprint(FingerprintConfig config)
        {
            var fingerprint = new FingerprintInfo
            {
                Id = Guid.NewGuid().ToString(),
                UserAgent = config.UserAgent ?? GetRandomUserAgent(),
                ViewportWidth = config.ViewportWidth ?? GetRandomViewportWidth(),
                ViewportHeight = config.ViewportHeight ?? GetRandomViewportHeight(),
                ScreenWidth = config.ScreenWidth ?? config.ViewportWidth ?? GetRandomViewportWidth(),
                ScreenHeight = config.ScreenHeight ?? config.ViewportHeight ?? GetRandomViewportHeight(),
                Language = config.Language ?? GetRandomLanguage(),
                Timezone = config.Timezone ?? GetRandomTimezone(),
                Platform = config.Platform ?? "Win32",
                HardwareConcurrency = config.HardwareConcurrency ?? _random.Next(4, 17),
                DeviceMemory = config.DeviceMemory ?? GetRandomDeviceMemory(),
                ColorDepth = config.ColorDepth ?? 24,
                PixelDepth = config.PixelDepth ?? 24,
                WebGLVendor = config.WebGLVendor ?? GetRandomWebGLVendor(),
                WebGLRenderer = config.WebGLRenderer ?? GetRandomWebGLRenderer(),
                CanvasFingerprint = GenerateCanvasFingerprint(),
                AudioFingerprint = GenerateAudioFingerprint(),
                CreatedAt = DateTime.Now
            };
            
            _logger?.LogInformation($"🎭 生成自定义指纹: {fingerprint.Id}");
            return fingerprint;
        }

        /// <summary>
        /// 生成指纹注入脚本
        /// </summary>
        /// <param name="fingerprint">指纹信息</param>
        /// <returns>JavaScript脚本</returns>
        public string GenerateFingerprintScript(FingerprintInfo fingerprint)
        {
            return $@"
// 指纹注入脚本 - 防检测 v2.0
(() => {{
    'use strict';
    
    console.log('🎭 开始注入指纹:', '{fingerprint.Id}');
    
    // 覆盖navigator属性
    const navigatorProps = {{
        language: '{fingerprint.Language}',
        languages: ['{fingerprint.Language}'],
        platform: '{fingerprint.Platform}',
        hardwareConcurrency: {fingerprint.HardwareConcurrency},
        deviceMemory: {fingerprint.DeviceMemory},
        userAgent: '{fingerprint.UserAgent}'
    }};
    
    Object.keys(navigatorProps).forEach(prop => {{
        Object.defineProperty(navigator, prop, {{
            get: () => navigatorProps[prop],
            configurable: true
        }});
    }});

    // 覆盖screen属性
    const screenProps = {{
        width: {fingerprint.ScreenWidth},
        height: {fingerprint.ScreenHeight},
        availWidth: {fingerprint.ScreenWidth},
        availHeight: {fingerprint.ScreenHeight - 40},
        colorDepth: {fingerprint.ColorDepth},
        pixelDepth: {fingerprint.PixelDepth}
    }};
    
    Object.keys(screenProps).forEach(prop => {{
        Object.defineProperty(screen, prop, {{
            get: () => screenProps[prop],
            configurable: true
        }});
    }});

    // WebGL指纹修改
    const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
    WebGLRenderingContext.prototype.getParameter = function(parameter) {{
        if (parameter === 37445) return '{fingerprint.WebGLVendor}';
        if (parameter === 37446) return '{fingerprint.WebGLRenderer}';
        return originalGetParameter.call(this, parameter);
    }};

    // Canvas指纹修改
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function(...args) {{
        const result = originalToDataURL.apply(this, args);
        // 添加轻微的随机噪声
        const noise = '{fingerprint.CanvasFingerprint}';
        return result.replace(/data:image\/png;base64,/, `data:image/png;base64,${{noise}}`);
    }};

    // Audio指纹修改
    if (window.AudioContext || window.webkitAudioContext) {{
        const AudioContextClass = window.AudioContext || window.webkitAudioContext;
        const originalCreateAnalyser = AudioContextClass.prototype.createAnalyser;
        AudioContextClass.prototype.createAnalyser = function() {{
            const analyser = originalCreateAnalyser.call(this);
            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
            analyser.getFloatFrequencyData = function(array) {{
                originalGetFloatFrequencyData.call(this, array);
                // 添加轻微的随机噪声
                for (let i = 0; i < array.length; i++) {{
                    array[i] += (Math.random() - 0.5) * 0.0001;
                }}
            }};
            return analyser;
        }};
    }}

    // 移除webdriver标识
    Object.defineProperty(navigator, 'webdriver', {{
        get: () => undefined,
        configurable: true
    }});

    // 移除自动化检测
    if (window.chrome && window.chrome.runtime) {{
        delete window.chrome.runtime.onConnect;
        delete window.chrome.runtime.onMessage;
    }}

    // 时区设置
    const originalDateTimeFormat = Intl.DateTimeFormat;
    Intl.DateTimeFormat = function(...args) {{
        if (args.length === 0) args = ['{fingerprint.Language}'];
        return new originalDateTimeFormat(...args);
    }};

    // 权限API修改
    if (navigator.permissions && navigator.permissions.query) {{
        const originalQuery = navigator.permissions.query;
        navigator.permissions.query = function(permissionDesc) {{
            if (permissionDesc.name === 'notifications') {{
                return Promise.resolve({{ state: 'default' }});
            }}
            return originalQuery.call(this, permissionDesc);
        }};
    }}

    console.log('✅ 指纹注入完成:', {{
        id: '{fingerprint.Id}',
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        hardwareConcurrency: navigator.hardwareConcurrency,
        deviceMemory: navigator.deviceMemory,
        screen: {{
            width: screen.width,
            height: screen.height,
            colorDepth: screen.colorDepth
        }}
    }});
}})();";
        }

        /// <summary>
        /// 验证指纹有效性
        /// </summary>
        /// <param name="fingerprint">指纹信息</param>
        /// <returns>验证结果</returns>
        public FingerprintValidationResult ValidateFingerprint(FingerprintInfo fingerprint)
        {
            var result = new FingerprintValidationResult { IsValid = true };
            var errors = new List<string>();

            // 检查必要字段
            if (string.IsNullOrEmpty(fingerprint.UserAgent))
                errors.Add("UserAgent不能为空");
            
            if (fingerprint.ViewportWidth <= 0 || fingerprint.ViewportHeight <= 0)
                errors.Add("视口尺寸必须大于0");
            
            if (fingerprint.HardwareConcurrency <= 0 || fingerprint.HardwareConcurrency > 32)
                errors.Add("硬件并发数必须在1-32之间");
            
            if (fingerprint.DeviceMemory <= 0)
                errors.Add("设备内存必须大于0");

            if (errors.Any())
            {
                result.IsValid = false;
                result.Errors = errors;
            }

            return result;
        }

        /// <summary>
        /// 获取指纹池统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public FingerprintPoolStats GetStats()
        {
            return new FingerprintPoolStats
            {
                PoolSize = _fingerprintPool.Count,
                TotalGenerated = 0, // 可以添加计数器跟踪
                LastGenerated = DateTime.Now
            };
        }

        #region 私有方法

        /// <summary>
        /// 生成随机指纹配置
        /// </summary>
        private FingerprintInfo GenerateRandomFingerprint()
        {
            var resolution = GetRandomResolution();
            
            return new FingerprintInfo
            {
                Id = Guid.NewGuid().ToString(),
                UserAgent = GetRandomUserAgent(),
                ViewportWidth = resolution.Width,
                ViewportHeight = resolution.Height,
                ScreenWidth = resolution.Width,
                ScreenHeight = resolution.Height,
                Language = GetRandomLanguage(),
                Timezone = GetRandomTimezone(),
                Platform = "Win32",
                HardwareConcurrency = _random.Next(4, 17),
                DeviceMemory = GetRandomDeviceMemory(),
                ColorDepth = 24,
                PixelDepth = 24,
                WebGLVendor = GetRandomWebGLVendor(),
                WebGLRenderer = GetRandomWebGLRenderer(),
                CanvasFingerprint = GenerateCanvasFingerprint(),
                AudioFingerprint = GenerateAudioFingerprint(),
                CreatedAt = DateTime.Now
            };
        }

        private string GetRandomUserAgent()
        {
            // 超大平台级别的Windows UserAgent库 - 100+种组合
            var userAgents = new[]
            {
                // Chrome 最新版本 (Windows 10/11)
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",

                // Chrome with different Windows versions
                "Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win32; x32) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",

                // Edge 浏览器 (基于Chromium)
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 Edg/121.0.0.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36 Edg/118.0.0.0",
                "Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 Edg/121.0.0.0",

                // Firefox 浏览器
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:118.0) Gecko/20100101 Firefox/118.0",
                "Mozilla/5.0 (Windows NT 11.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0",
                "Mozilla/5.0 (Windows NT 10.0; WOW64; rv:121.0) Gecko/20100101 Firefox/121.0",

                // Chrome with different patch versions
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.109 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.71 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.199 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.5993.117 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.5993.88 Safari/537.36",

                // Chrome Beta 版本
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.85 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.57 Safari/537.36",

                // Opera 浏览器 (基于Chromium)
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/106.0.0.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 OPR/105.0.0.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36 OPR/104.0.0.0",
                "Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/106.0.0.0",

                // Brave 浏览器
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Brave/120",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Brave/119",
                "Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Brave/120",

                // Vivaldi 浏览器
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Vivaldi/6.5.3206.63",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Vivaldi/6.4.3160.47",
                "Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Vivaldi/6.5.3206.63",

                // Chrome 企业版
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Enterprise",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Enterprise",

                // Chrome 开发者版本
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Dev",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 Dev",

                // Chrome Canary 版本
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36 Canary",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Canary",

                // 不同架构的Windows
                "Mozilla/5.0 (Windows NT 10.0; ARM64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; ARM64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 11.0; ARM64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",

                // Chrome 移动模拟
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Mobile Safari/537.36",

                // Firefox ESR 版本
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:115.0) Gecko/20100101 Firefox/115.0 ESR",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:102.0) Gecko/20100101 Firefox/102.0 ESR",
                "Mozilla/5.0 (Windows NT 11.0; Win64; x64; rv:115.0) Gecko/20100101 Firefox/115.0 ESR",

                // Firefox Beta 版本
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0 Beta",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0 Beta",

                // Firefox Nightly 版本
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:124.0) Gecko/20100101 Firefox/124.0 Nightly",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0 Nightly",

                // Edge Beta 版本
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 Edg/121.0.0.0 Beta",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0 Beta",

                // Edge Dev 版本
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Edg/122.0.0.0 Dev",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 Edg/121.0.0.0 Dev",

                // 旧版本 Chrome (仍在使用)
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",

                // 旧版本 Firefox (仍在使用)
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:117.0) Gecko/20100101 Firefox/117.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:116.0) Gecko/20100101 Firefox/116.0",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:115.0) Gecko/20100101 Firefox/115.0",

                // Chrome 特殊构建版本
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.129 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.130 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.200 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.201 Safari/537.36",

                // 不同语言版本的浏览器
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 (zh-CN)",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 (en-US)",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 (ja-JP)",

                // 企业环境特殊版本
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Corporate",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Corporate",

                // 360安全浏览器 (基于Chromium)
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 360SE",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 360SE",

                // QQ浏览器 (基于Chromium)
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 QQBrowser",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 QQBrowser",

                // 搜狗浏览器 (基于Chromium)
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 SogouExplorer",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 SogouExplorer"
            };
            return userAgents[_random.Next(userAgents.Length)];
        }

        private (int Width, int Height) GetRandomResolution()
        {
            var resolutions = new[]
            {
                (1920, 1080), (1366, 768), (1536, 864), (1440, 900), (1280, 720),
                (1600, 900), (1024, 768), (1280, 1024), (1680, 1050), (1920, 1200)
            };
            return resolutions[_random.Next(resolutions.Length)];
        }

        private int GetRandomViewportWidth() => GetRandomResolution().Width;
        private int GetRandomViewportHeight() => GetRandomResolution().Height;

        private string GetRandomLanguage()
        {
            var languages = new[] { "zh-CN", "en-US", "en-GB", "ja-JP", "ko-KR" };
            return languages[_random.Next(languages.Length)];
        }

        private string GetRandomTimezone()
        {
            var timezones = new[] { "Asia/Shanghai", "America/New_York", "Europe/London", "Asia/Tokyo", "Asia/Seoul" };
            return timezones[_random.Next(timezones.Length)];
        }

        private int GetRandomDeviceMemory()
        {
            var memories = new[] { 4, 8, 16, 32 };
            return memories[_random.Next(memories.Length)];
        }

        private string GetRandomWebGLVendor()
        {
            var vendors = new[] { "Google Inc. (Intel)", "Google Inc. (NVIDIA)", "Google Inc. (AMD)" };
            return vendors[_random.Next(vendors.Length)];
        }

        private string GetRandomWebGLRenderer()
        {
            var renderers = new[]
            {
                "ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)",
                "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0, D3D11)",
                "ANGLE (AMD, AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0, D3D11)"
            };
            return renderers[_random.Next(renderers.Length)];
        }

        private string GenerateCanvasFingerprint()
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[16];
            rng.GetBytes(bytes);
            return Convert.ToHexString(bytes).ToLower();
        }

        private string GenerateAudioFingerprint()
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[16];
            rng.GetBytes(bytes);
            return Convert.ToHexString(bytes).ToLower();
        }

        #endregion

        public void Dispose()
        {
            _logger?.LogInformation("🔄 指纹管理器已释放资源");
        }
    }

    #region 数据模型

    /// <summary>
    /// 指纹信息
    /// </summary>
    public class FingerprintInfo
    {
        public string Id { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public int ViewportWidth { get; set; }
        public int ViewportHeight { get; set; }
        public int ScreenWidth { get; set; }
        public int ScreenHeight { get; set; }
        public string Language { get; set; } = string.Empty;
        public string Timezone { get; set; } = string.Empty;
        public string Platform { get; set; } = string.Empty;
        public int HardwareConcurrency { get; set; }
        public int DeviceMemory { get; set; }
        public int ColorDepth { get; set; }
        public int PixelDepth { get; set; }
        public string WebGLVendor { get; set; } = string.Empty;
        public string WebGLRenderer { get; set; } = string.Empty;
        public string CanvasFingerprint { get; set; } = string.Empty;
        public string AudioFingerprint { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// 指纹配置
    /// </summary>
    public class FingerprintConfig
    {
        public string? UserAgent { get; set; }
        public int? ViewportWidth { get; set; }
        public int? ViewportHeight { get; set; }
        public int? ScreenWidth { get; set; }
        public int? ScreenHeight { get; set; }
        public string? Language { get; set; }
        public string? Timezone { get; set; }
        public string? Platform { get; set; }
        public int? HardwareConcurrency { get; set; }
        public int? DeviceMemory { get; set; }
        public int? ColorDepth { get; set; }
        public int? PixelDepth { get; set; }
        public string? WebGLVendor { get; set; }
        public string? WebGLRenderer { get; set; }
    }

    /// <summary>
    /// 指纹验证结果
    /// </summary>
    public class FingerprintValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    /// <summary>
    /// 指纹池统计信息
    /// </summary>
    public class FingerprintPoolStats
    {
        public int PoolSize { get; set; }
        public int TotalGenerated { get; set; }
        public DateTime LastGenerated { get; set; }
    }

    #endregion
}
