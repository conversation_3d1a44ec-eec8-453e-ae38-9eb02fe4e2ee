// 代理信息UI数据模型
// 功能说明：用于UI数据绑定的代理服务器信息模型

using System;
using System.ComponentModel;

namespace BatchBrowserUI.Models
{
    /// <summary>
    /// 代理信息UI模型 - 用于DataGrid数据绑定
    /// 功能：展示代理服务器的状态和配置信息
    /// </summary>
    public class ProxyModel : INotifyPropertyChanged
    {
        private string _address;                // 代理地址
        private int _port;                      // 代理端口
        private string _type;                   // 代理类型 (HTTP/SOCKS5)
        private string _status;                 // 代理状态 (活跃/停用/错误)
        private string _username;               // 用户名
        private string _password;               // 密码
        private int _responseTime;              // 响应时间 (ms)
        private DateTime _lastCheck;            // 最后检查时间
        private int _usageCount;                // 使用次数
        private bool _isActive;                 // 是否活跃

        /// <summary>
        /// 代理服务器地址
        /// </summary>
        public string Address
        {
            get => _address;
            set
            {
                _address = value;
                OnPropertyChanged(nameof(Address));
            }
        }

        /// <summary>
        /// 代理服务器端口
        /// </summary>
        public int Port
        {
            get => _port;
            set
            {
                _port = value;
                OnPropertyChanged(nameof(Port));
            }
        }

        /// <summary>
        /// 代理类型 (HTTP/SOCKS5)
        /// </summary>
        public string Type
        {
            get => _type;
            set
            {
                _type = value;
                OnPropertyChanged(nameof(Type));
            }
        }

        /// <summary>
        /// 代理服务器状态
        /// </summary>
        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        /// <summary>
        /// 认证用户名
        /// </summary>
        public string Username
        {
            get => _username;
            set
            {
                _username = value;
                OnPropertyChanged(nameof(Username));
            }
        }

        /// <summary>
        /// 认证密码 (用于显示，实际存储时应加密)
        /// </summary>
        public string Password
        {
            get => _password;
            set
            {
                _password = value;
                OnPropertyChanged(nameof(Password));
            }
        }

        /// <summary>
        /// 响应时间 (毫秒)
        /// </summary>
        public int ResponseTime
        {
            get => _responseTime;
            set
            {
                _responseTime = value;
                OnPropertyChanged(nameof(ResponseTime));
                OnPropertyChanged(nameof(ResponseTimeString));
            }
        }

        /// <summary>
        /// 响应时间的字符串表示 - 用于UI显示
        /// </summary>
        public string ResponseTimeString => $"{ResponseTime} ms";

        /// <summary>
        /// 最后健康检查时间
        /// </summary>
        public DateTime LastCheck
        {
            get => _lastCheck;
            set
            {
                _lastCheck = value;
                OnPropertyChanged(nameof(LastCheck));
                OnPropertyChanged(nameof(LastCheckString));
            }
        }

        /// <summary>
        /// 最后检查时间的字符串表示 - 用于UI显示
        /// </summary>
        public string LastCheckString => LastCheck.ToString("HH:mm:ss");

        /// <summary>
        /// 使用次数统计
        /// </summary>
        public int UsageCount
        {
            get => _usageCount;
            set
            {
                _usageCount = value;
                OnPropertyChanged(nameof(UsageCount));
            }
        }

        /// <summary>
        /// 是否处于活跃状态
        /// </summary>
        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged(nameof(IsActive));
            }
        }

        /// <summary>
        /// 代理完整地址 - 用于UI显示
        /// </summary>
        public string FullAddress => $"{Address}:{Port}";

        /// <summary>
        /// 属性变更通知事件 - 支持数据绑定
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 触发属性变更通知
        /// </summary>
        /// <param name="propertyName">变更的属性名称</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
