﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "85CC112FB0B0A2DB17EC35244B366FD69F5D0FAD"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using BatchBrowserUI;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace BatchBrowserUI {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 132 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse EngineStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse ChromeStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartEngineButton;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopEngineButton;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateInstancesButton;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ManageProxiesButton;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ConcurrencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AntiDetectionCheckBox;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl MainTabControl;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveInstancesCountLabel;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalInstancesCountLabel;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid BrowserInstancesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 316 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalProxiesLabel;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HealthyProxiesLabel;
        
        #line default
        #line hidden
        
        
        #line 328 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FailedProxiesLabel;
        
        #line default
        #line hidden
        
        
        #line 342 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ProxiesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 398 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CpuUsageLabel;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar CpuUsageProgressBar;
        
        #line default
        #line hidden
        
        
        #line 411 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MemoryUsageLabel;
        
        #line default
        #line hidden
        
        
        #line 413 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar MemoryUsageProgressBar;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UptimeLabel;
        
        #line default
        #line hidden
        
        
        #line 434 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SuccessRateLabel;
        
        #line default
        #line hidden
        
        
        #line 460 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoScrollCheckBox;
        
        #line default
        #line hidden
        
        
        #line 468 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer LogScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 471 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox LogListBox;
        
        #line default
        #line hidden
        
        
        #line 500 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ChromePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 506 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox UseLocalChromeCheckBox;
        
        #line default
        #line hidden
        
        
        #line 511 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox HeadlessCheckBox;
        
        #line default
        #line hidden
        
        
        #line 521 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableAntiDetectionCheckBox;
        
        #line default
        #line hidden
        
        
        #line 526 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RandomFingerprintCheckBox;
        
        #line default
        #line hidden
        
        
        #line 531 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SpoofUserAgentCheckBox;
        
        #line default
        #line hidden
        
        
        #line 536 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SpoofViewportCheckBox;
        
        #line default
        #line hidden
        
        
        #line 554 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider MaxConcurrencySlider;
        
        #line default
        #line hidden
        
        
        #line 557 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MaxConcurrencyLabel;
        
        #line default
        #line hidden
        
        
        #line 563 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider PageTimeoutSlider;
        
        #line default
        #line hidden
        
        
        #line 566 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PageTimeoutLabel;
        
        #line default
        #line hidden
        
        
        #line 570 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableImagesCheckBox;
        
        #line default
        #line hidden
        
        
        #line 599 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusLabel;
        
        #line default
        #line hidden
        
        
        #line 602 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateLabel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/BatchBrowserUI;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.EngineStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 2:
            this.ChromeStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 3:
            this.StartEngineButton = ((System.Windows.Controls.Button)(target));
            
            #line 162 "..\..\..\MainWindow.xaml"
            this.StartEngineButton.Click += new System.Windows.RoutedEventHandler(this.StartEngineButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.StopEngineButton = ((System.Windows.Controls.Button)(target));
            
            #line 168 "..\..\..\MainWindow.xaml"
            this.StopEngineButton.Click += new System.Windows.RoutedEventHandler(this.StopEngineButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CreateInstancesButton = ((System.Windows.Controls.Button)(target));
            
            #line 175 "..\..\..\MainWindow.xaml"
            this.CreateInstancesButton.Click += new System.Windows.RoutedEventHandler(this.CreateInstancesButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ManageProxiesButton = ((System.Windows.Controls.Button)(target));
            
            #line 182 "..\..\..\MainWindow.xaml"
            this.ManageProxiesButton.Click += new System.Windows.RoutedEventHandler(this.ManageProxiesButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ConcurrencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.AntiDetectionCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.MainTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 10:
            this.ActiveInstancesCountLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TotalInstancesCountLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            
            #line 237 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshInstancesButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 240 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseAllInstancesButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.BrowserInstancesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 17:
            
            #line 284 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BatchNavigateButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 286 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BatchExecuteButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 288 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BatchScreenshotButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.TotalProxiesLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.HealthyProxiesLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.FailedProxiesLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            
            #line 334 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ProxyHealthCheckButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 336 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddProxyButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.ProxiesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 28:
            this.CpuUsageLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.CpuUsageProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 30:
            this.MemoryUsageLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.MemoryUsageProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 32:
            this.UptimeLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.SuccessRateLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.AutoScrollCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 35:
            
            #line 463 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearLogButton_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.LogScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 37:
            this.LogListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 38:
            this.ChromePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 39:
            
            #line 503 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DetectChromeButton_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.UseLocalChromeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 41:
            this.HeadlessCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 42:
            this.EnableAntiDetectionCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 43:
            this.RandomFingerprintCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 44:
            this.SpoofUserAgentCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 45:
            this.SpoofViewportCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 46:
            this.MaxConcurrencySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 47:
            this.MaxConcurrencyLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 48:
            this.PageTimeoutSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 49:
            this.PageTimeoutLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.EnableImagesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 51:
            
            #line 580 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 52:
            
            #line 582 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 53:
            this.StatusLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 54:
            this.LastUpdateLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 15:
            
            #line 267 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewInstanceButton_Click);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 269 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseInstanceButton_Click);
            
            #line default
            #line hidden
            break;
            case 26:
            
            #line 364 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestProxyButton_Click);
            
            #line default
            #line hidden
            break;
            case 27:
            
            #line 366 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveProxyButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

