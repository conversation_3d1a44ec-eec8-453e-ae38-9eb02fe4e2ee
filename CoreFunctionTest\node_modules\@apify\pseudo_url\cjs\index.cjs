"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  PseudoUrl: () => PseudoUrl,
  purlToRegExp: () => purlToRegExp
});
module.exports = __toCommonJS(index_exports);

// src/pseudo_url.ts
var import_node_util = require("util");
var import_log = __toESM(require("@apify/log"));
var _PseudoUrl = class _PseudoUrl {
  /**
   * @param purl
   *   A pseudo-URL string or a regular expression object.
   *   Using a `RegExp` instance enables more granular control,
   *   such as making the matching case-sensitive.
   */
  constructor(purl) {
    __publicField(this, "regex");
    if (purl instanceof RegExp) {
      this.regex = purl;
    } else if (typeof purl === "string") {
      this.regex = purlToRegExp(purl);
      import_log.default.debug("PURL parsed", { purl, regex: this.regex });
    } else {
      const type = Array.isArray(purl) ? "array" : typeof purl;
      throw new Error(`Invalid PseudoUrl format, 'string' or 'RegExp' required, got \`${(0, import_node_util.inspect)(purl)}\` of type '${type}' instead`);
    }
  }
  /**
   * Determines whether a URL matches this pseudo-URL pattern.
   */
  matches(url) {
    return typeof url === "string" && url.match(this.regex) !== null;
  }
};
__name(_PseudoUrl, "PseudoUrl");
var PseudoUrl = _PseudoUrl;
function purlToRegExp(purl) {
  const trimmedPurl = purl.trim();
  if (trimmedPurl.length === 0) throw new Error(`Cannot parse PURL '${trimmedPurl}': it must be an non-empty string`);
  let regex = "^";
  try {
    let openBrackets = 0;
    for (let i = 0; i < trimmedPurl.length; i++) {
      const ch = trimmedPurl.charAt(i);
      if (ch === "[" && ++openBrackets === 1) {
        regex += "(";
      } else if (ch === "]" && openBrackets > 0 && --openBrackets === 0) {
        regex += ")";
      } else if (openBrackets > 0) {
        regex += ch;
      } else {
        const code = ch.charCodeAt(0);
        if (code >= 48 && code <= 57 || code >= 65 && code <= 90 || code >= 97 && code <= 122) {
          regex += ch;
        } else {
          const hex = code < 16 ? `0${code.toString(16)}` : code.toString(16);
          regex += `\\x${hex}`;
        }
      }
    }
    regex += "$";
  } catch (err) {
    throw new Error(`Cannot parse PURL '${purl}': ${err}`);
  }
  return new RegExp(regex, "i");
}
__name(purlToRegExp, "purlToRegExp");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  PseudoUrl,
  purlToRegExp
});
//# sourceMappingURL=index.cjs.map