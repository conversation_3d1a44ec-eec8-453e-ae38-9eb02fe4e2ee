{"name": "@crawlee/cli", "version": "3.13.7", "description": "The scalable web crawling and scraping library for JavaScript/Node.js. Enables development of data extraction and web automation jobs (not only) with headless Chrome and Puppeteer.", "engines": {"node": ">=16.0.0"}, "bin": {"crawlee": "./index.js"}, "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": "./index.mjs", "require": "./index.js", "types": "./index.d.ts"}, "./package.json": "./package.json"}, "keywords": ["apify", "headless", "chrome", "puppeteer", "crawler", "scraper"], "author": {"name": "Apify", "email": "<EMAIL>", "url": "https://apify.com"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/apify/crawlee"}, "bugs": {"url": "https://github.com/apify/crawlee/issues"}, "homepage": "https://crawlee.dev", "scripts": {"build": "yarn clean && yarn compile && yarn copy", "clean": "rimraf ./dist", "compile": "tsc -p tsconfig.build.json", "copy": "tsx ../../scripts/copy.ts"}, "publishConfig": {"access": "public"}, "dependencies": {"@crawlee/templates": "3.13.7", "ansi-colors": "^4.1.3", "fs-extra": "^11.0.0", "inquirer": "^8.2.4", "tslib": "^2.4.0", "yargonaut": "^1.1.4", "yargs": "^17.5.1"}, "lerna": {"command": {"publish": {"assets": []}}}, "gitHead": "f357979b66b4348730ce587edec6dcbb4e1ff26d"}