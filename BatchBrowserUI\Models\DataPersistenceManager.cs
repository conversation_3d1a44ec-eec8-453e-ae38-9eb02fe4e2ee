// 数据持久化管理器 - 负责浏览器实例数据的保存和恢复
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Linq;

namespace BatchBrowserUI.Models
{
    /// <summary>
    /// 数据持久化管理器
    /// 负责浏览器实例数据的保存、加载和管理
    /// </summary>
    public class DataPersistenceManager
    {
        /// <summary>
        /// 数据文件路径
        /// </summary>
        private readonly string _dataFilePath;
        
        /// <summary>
        /// 配置文件路径
        /// </summary>
        private readonly string _configFilePath;

        /// <summary>
        /// 初始化数据持久化管理器
        /// </summary>
        public DataPersistenceManager()
        {
            var dataDirectory = Path.Combine(Environment.CurrentDirectory, "data");

            // 确保数据目录存在
            if (!Directory.Exists(dataDirectory))
            {
                Directory.CreateDirectory(dataDirectory);
                Console.WriteLine($"📁 创建数据目录: {dataDirectory}");
            }

            _dataFilePath = Path.Combine(dataDirectory, "browser_instances.json");
            _configFilePath = Path.Combine(dataDirectory, "app_config.json");

            Console.WriteLine($"📁 数据持久化管理器初始化完成");
            Console.WriteLine($"📁 实例数据文件: {_dataFilePath}");
            Console.WriteLine($"📁 配置文件: {_configFilePath}");
            Console.WriteLine($"📁 数据文件存在: {File.Exists(_dataFilePath)}");
        }

        /// <summary>
        /// 保存浏览器实例数据
        /// </summary>
        /// <param name="instances">浏览器实例列表</param>
        /// <returns>保存是否成功</returns>
        public bool SaveBrowserInstances(List<BrowserInstanceModel> instances)
        {
            try
            {
                Console.WriteLine($"💾 正在保存 {instances.Count} 个浏览器实例...");

                // 创建持久化数据结构
                var persistenceData = new BrowserInstancesPersistenceData
                {
                    SaveTime = DateTime.Now,
                    Version = "1.0",
                    Instances = instances.Select(instance => new PersistentBrowserInstance
                    {
                        Id = instance.Id,
                        Status = instance.Status,
                        ProxyServer = instance.ProxyServer,
                        Url = instance.Url,
                        CreatedTime = instance.CreatedTime,
                        LastActiveTime = instance.LastActiveTime,
                        MemoryUsage = instance.MemoryUsage,
                        Fingerprint = instance.Fingerprint,
                        IsActive = instance.IsActive,
                        RequestCount = instance.RequestCount
                    }).ToList()
                };

                // 序列化为JSON
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var jsonString = JsonSerializer.Serialize(persistenceData, jsonOptions);
                
                // 写入文件
                File.WriteAllText(_dataFilePath, jsonString);
                
                Console.WriteLine($"✅ 浏览器实例数据保存成功: {_dataFilePath}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 保存浏览器实例数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 加载浏览器实例数据
        /// </summary>
        /// <returns>浏览器实例列表</returns>
        public List<BrowserInstanceModel> LoadBrowserInstances()
        {
            try
            {
                if (!File.Exists(_dataFilePath))
                {
                    Console.WriteLine($"📋 数据文件不存在，返回空列表: {_dataFilePath}");
                    return new List<BrowserInstanceModel>();
                }

                Console.WriteLine($"📂 正在加载浏览器实例数据: {_dataFilePath}");

                // 读取文件内容
                var jsonString = File.ReadAllText(_dataFilePath);
                
                // 反序列化 - 使用CamelCase策略来匹配JSON文件格式
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    PropertyNameCaseInsensitive = true // 添加大小写不敏感选项
                };

                Console.WriteLine($"📋 JSON内容预览: {jsonString.Substring(0, Math.Min(200, jsonString.Length))}...");
                var persistenceData = JsonSerializer.Deserialize<BrowserInstancesPersistenceData>(jsonString, jsonOptions);
                Console.WriteLine($"📋 反序列化结果: persistenceData={persistenceData != null}, Instances={persistenceData?.Instances?.Count ?? -1}");
                
                if (persistenceData?.Instances == null)
                {
                    Console.WriteLine($"⚠️ 数据文件格式无效，返回空列表");
                    return new List<BrowserInstanceModel>();
                }

                // 转换为UI模型
                var instances = persistenceData.Instances.Select(persistent =>
                {
                    var instance = new BrowserInstanceModel
                    {
                        Id = persistent.Id ?? "",
                        Status = persistent.Status ?? "已停止",
                        ProxyServer = persistent.ProxyServer,
                        Proxy = persistent.ProxyServer ?? "无代理",
                        Url = persistent.Url ?? "about:blank",
                        CreatedTime = persistent.CreatedTime,
                        Created = persistent.CreatedTime,
                        LastActiveTime = persistent.LastActiveTime,
                        MemoryUsage = persistent.MemoryUsage,
                        Memory = (int)persistent.MemoryUsage,
                        Fingerprint = persistent.Fingerprint ?? "默认指纹",
                        IsActive = false, // 重启后所有实例都设为非活跃状态
                        RequestCount = persistent.RequestCount
                    };
                    return instance;
                }).ToList();

                Console.WriteLine($"✅ 成功加载 {instances.Count} 个浏览器实例");
                Console.WriteLine($"📋 数据保存时间: {persistenceData.SaveTime:yyyy-MM-dd HH:mm:ss}");

                return instances;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 加载浏览器实例数据失败: {ex.Message}");
                return new List<BrowserInstanceModel>();
            }
        }

        /// <summary>
        /// 保存应用程序配置
        /// </summary>
        /// <param name="config">Chrome配置</param>
        /// <returns>保存是否成功</returns>
        public bool SaveAppConfig(ChromeConfig config)
        {
            try
            {
                Console.WriteLine($"💾 正在保存应用程序配置...");

                var configData = new AppConfigData
                {
                    SaveTime = DateTime.Now,
                    Version = "1.0",
                    ChromeConfig = config
                };

                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var jsonString = JsonSerializer.Serialize(configData, jsonOptions);
                File.WriteAllText(_configFilePath, jsonString);
                
                Console.WriteLine($"✅ 应用程序配置保存成功: {_configFilePath}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 保存应用程序配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 加载应用程序配置
        /// </summary>
        /// <returns>Chrome配置</returns>
        public ChromeConfig LoadAppConfig()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    Console.WriteLine($"📋 配置文件不存在，返回默认配置: {_configFilePath}");
                    return new ChromeConfig();
                }

                Console.WriteLine($"📂 正在加载应用程序配置: {_configFilePath}");

                var jsonString = File.ReadAllText(_configFilePath);
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var configData = JsonSerializer.Deserialize<AppConfigData>(jsonString, jsonOptions);
                
                if (configData?.ChromeConfig == null)
                {
                    Console.WriteLine($"⚠️ 配置文件格式无效，返回默认配置");
                    return new ChromeConfig();
                }

                Console.WriteLine($"✅ 应用程序配置加载成功");
                Console.WriteLine($"📋 配置保存时间: {configData.SaveTime:yyyy-MM-dd HH:mm:ss}");

                return configData.ChromeConfig;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 加载应用程序配置失败: {ex.Message}");
                return new ChromeConfig();
            }
        }

        /// <summary>
        /// 清理过期数据
        /// </summary>
        /// <param name="daysToKeep">保留天数</param>
        public void CleanupOldData(int daysToKeep = 30)
        {
            try
            {
                Console.WriteLine($"🧹 开始清理 {daysToKeep} 天前的数据...");

                var dataDirectory = Path.GetDirectoryName(_dataFilePath);
                if (string.IsNullOrEmpty(dataDirectory) || !Directory.Exists(dataDirectory))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var files = Directory.GetFiles(dataDirectory, "*.json");

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.LastWriteTime < cutoffDate)
                    {
                        File.Delete(file);
                        Console.WriteLine($"🗑️ 删除过期文件: {Path.GetFileName(file)}");
                    }
                }

                Console.WriteLine($"✅ 数据清理完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 数据清理失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 浏览器实例持久化数据结构
    /// </summary>
    public class BrowserInstancesPersistenceData
    {
        public DateTime SaveTime { get; set; }
        public string Version { get; set; } = "1.0";
        public List<PersistentBrowserInstance> Instances { get; set; } = new();
    }

    /// <summary>
    /// 持久化的浏览器实例数据
    /// </summary>
    public class PersistentBrowserInstance
    {
        public string? Id { get; set; }
        public string? Status { get; set; }
        public string? ProxyServer { get; set; }
        public string? Url { get; set; }
        public DateTime CreatedTime { get; set; }
        public DateTime LastActiveTime { get; set; }
        public long MemoryUsage { get; set; }
        public string? Fingerprint { get; set; }
        public bool IsActive { get; set; }
        public int RequestCount { get; set; }
    }

    /// <summary>
    /// 应用程序配置数据结构
    /// </summary>
    public class AppConfigData
    {
        public DateTime SaveTime { get; set; }
        public string Version { get; set; } = "1.0";
        public ChromeConfig? ChromeConfig { get; set; }
    }
}
