<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <!-- 引用所有DLL模块 -->
    <ProjectReference Include="..\BrowserCore\BrowserCore.csproj" />
    <ProjectReference Include="..\ProxyManager\ProxyManager.csproj" />
    <ProjectReference Include="..\FingerprintGenerator\FingerprintGenerator.csproj" />
    <ProjectReference Include="..\InstanceManager\InstanceManager.csproj" />
    <ProjectReference Include="..\PerformanceMonitor\PerformanceMonitor.csproj" />
    <ProjectReference Include="..\DataPersistence\DataPersistence.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- 日志 -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    
    <!-- 依赖注入 -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
  </ItemGroup>

</Project>
