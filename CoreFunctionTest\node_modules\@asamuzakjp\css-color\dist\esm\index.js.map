{"version": 3, "file": "index.js", "sources": ["../../src/index.ts"], "sourcesContent": ["/*!\n * CSS color - Resolve, parse, convert CSS color.\n * @license MIT\n * @copyright asamuzaK (Kazz)\n * @see {@link https://github.com/asamuzaK/cssColor/blob/main/LICENSE}\n */\n\nimport { cssCalc as csscalc } from './js/css-calc';\nimport { isGradient } from './js/css-gradient';\nimport { cssVar } from './js/css-var';\nimport { extractDashedIdent, isColor as iscolor, splitValue } from './js/util';\n\nexport { convert } from './js/convert';\nexport { resolve } from './js/resolve';\n/* utils */\nexport const utils = {\n  cssCalc: csscalc,\n  cssVar,\n  extractDashedIdent,\n  isColor: iscolor,\n  isGradient,\n  splitValue\n};\n/* TODO: remove later */\n/* alias */\nexport const isColor = utils.isColor;\nexport const cssCalc = utils.cssCalc;\n"], "names": ["csscalc", "iscolor"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeO,MAAM,QAAQ;AAAA,EACnB,SAASA;AAAAA,EACT;AAAA,EACA;AAAA,EACA,SAASC;AAAAA,EACT;AAAA,EACA;AACF;AAGO,MAAM,UAAU,MAAM;AACtB,MAAM,UAAU,MAAM;"}