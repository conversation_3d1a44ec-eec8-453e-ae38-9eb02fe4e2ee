{"version": 3, "file": "error_tracker.js", "sourceRoot": "", "sources": ["../../src/crawlers/error_tracker.ts"], "names": [], "mappings": ";;;;;AAAA,yCAAoC;AAGpC,2DAAuD;AAuBvD,MAAM,6BAA6B,GAAG,CAAC,IAAY,EAAE,EAAE;IACnD,MAAM,6BAA6B,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAE5D,IAAI,6BAA6B,KAAK,CAAC,CAAC,EAAE,CAAC;QACvC,MAAM,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,6BAA6B,CAAC,CAAC;QAElF,IAAI,wBAAwB,KAAK,CAAC,CAAC,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC,KAAK,CAAC,6BAA6B,GAAG,CAAC,EAAE,wBAAwB,CAAC,CAAC;QACnF,CAAC;IACL,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,qEAAqE;AACrE,MAAM,qBAAqB,GAAG,CAAC,KAAe,EAAE,EAAE;IAC9C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACvB,MAAM,IAAI,GAAG,6BAA6B,CAAC,IAAI,CAAC,CAAC;QAEjD,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACnG,SAAS;QACb,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,OAAO,6BAA6B,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC;AAEF,MAAM,kBAAkB,GAAG,CAAC,KAAqB,EAAE,OAAgC,EAAE,aAAsB,EAAE,EAAE;IAC3G,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IAElE,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC;IAEjB,IAAI,KAAK,EAAE,CAAC;QACR,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAChE,OAAO,GAAG,CAAC,CAAC;gBACZ,MAAM;YACV,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAI,oBAAoB,GAAG,IAAI,CAAC;IAChC,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC;QACjB,oBAAoB,GAAG,aAAa;YAChC,CAAC,CAAC,KAAM;iBACD,KAAK,CAAC,OAAO,CAAC;iBACd,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;iBACpB,IAAI,CAAC,IAAI,CAAC;YACjB,CAAC,CAAC,qBAAqB,CAAC,KAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACxB,oBAAoB,GAAG,qBAAqB,CAAC;IACjD,CAAC;IAED,IAAI,CAAC,CAAC,oBAAoB,IAAI,OAAO,CAAC,EAAE,CAAC;QACrC,OAAO,CAAC,oBAAoB,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAED,OAAO,OAAO,CAAC,oBAAoB,CAA4B,CAAC;AACpE,CAAC,CAAC;AAEF,MAAM,iBAAiB,GAAG,CAAC,KAAqB,EAAE,OAAgC,EAAE,EAAE;IAClF,IAAI,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;IAErB,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACrB,IAAI,GAAG,oBAAoB,CAAC;IAChC,CAAC;IAED,IAAI,CAAC,CAAC,IAAI,IAAI,OAAO,CAAC,EAAE,CAAC;QACrB,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAA4B,CAAC;AAC5D,CAAC,CAAC;AAEF,MAAM,iBAAiB,GAAG,CAAC,KAAqB,EAAE,OAAgC,EAAE,EAAE;IAClF,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;IAEvB,IAAI,CAAC,CAAC,IAAI,IAAI,OAAO,CAAC,EAAE,CAAC;QACrB,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED,OAAO,OAAO,CAAC,IAAI,CAA4B,CAAC;AACpD,CAAC,CAAC;AAEF,MAAM,2BAA2B,GAAG,CAAC,CAAW,EAAE,CAAW,EAAE,EAAE;IAC7D,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC;IACtB,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC;IACtB,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC;QAC/C,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;QAEhB,GAAG,CAAC;YACA,IAAI,UAAU,GAAG,MAAM,CAAC;YAExB,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;YAE1C,IAAI,UAAU,GAAG,MAAM,CAAC;YAExB,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,OAAO,UAAU,GAAG,CAAC,CAAC,MAAM,IAAI,UAAU,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC;gBAC3F,MAAM,EAAE,CAAC;YACb,CAAC;YAED,IAAI,MAAM,GAAG,SAAS,EAAE,CAAC;gBACrB,SAAS,GAAG,MAAM,CAAC;gBACnB,YAAY,GAAG,MAAM,CAAC;gBACtB,YAAY,GAAG,MAAM,CAAC;YAC1B,CAAC;QACL,CAAC,QAAQ,MAAM,KAAK,CAAC,CAAC,EAAE;IAC5B,CAAC;IAED,OAAO;QACH,SAAS;QACT,YAAY;QACZ,YAAY;KACf,CAAC;AACN,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,KAAgB,EAAE,MAAe,EAAE,EAAE;IACrD,IAAI,MAAM,GAAG,CAAC,CAAC;IAEf,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACvB,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;YAClB,MAAM,EAAE,CAAC;QACb,CAAC;IACL,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,MAAM,oBAAoB,GAAG,CAAC,CAAW,EAAE,CAAW,EAAE,EAAE;IACtD,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,2BAA2B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAEpF,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;QAClB,OAAO,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC;IAED,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;IACvC,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;IACvC,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,YAAY,GAAG,SAAS,CAAC,CAAC;IACjD,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,YAAY,GAAG,SAAS,CAAC,CAAC;IAEjD,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC3C,MAAM,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,YAAY,EAAE,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC;IAEhE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7C,MAAM,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,MAAM,8BAA8B,GAAG,CAAC,CAAW,EAAE,CAAW,EAAE,EAAE;IAChE,MAAM,MAAM,GAAG,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE1C,0BAA0B;IAC1B,IAAI,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,CAAC;IACjB,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,wFAAwF;AACxF,MAAM,aAAa,GAAG,CAAC,CAAS,EAAE,CAAS,EAAE,OAAgC,EAAE,EAAE;IAC7E,MAAM,WAAW,GAAG,8BAA8B,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEzF,IAAI,WAAW,KAAK,GAAG,EAAE,CAAC;QACtB,OAAO,SAAS,CAAC;IACrB,CAAC;IAMD,MAAM,KAAK,GAAI,OAAO,CAAC,CAAC,CAAc,CAAC,KAAK,GAAI,OAAO,CAAC,CAAC,CAAc,CAAC,KAAK,CAAC;IAE9E,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;IAClB,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;IAElB,OAAO,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QACtD,KAAK;KACR,CAAC,CAAC;IAEH,OAAO,WAAW,CAAC;AACvB,CAAC,CAAC;AAEF,MAAM,oBAAoB,GAAG,CAAC,KAAqB,EAAE,OAAgC,EAAE,eAAwB,EAAE,EAAE;IAC/G,IAAI,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;IAExB,IAAI,CAAC,OAAO,EAAE,CAAC;QACX,IAAI,CAAC;YACD,OAAO;gBACH,OAAO,KAAK,KAAK,QAAQ;oBACrB,CAAC,CAAC,KAAK;oBACP,CAAC,CAAC,qDAAqD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;QAC3F,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,GAAG,iFAAiF,IAAA,mBAAO,EAAC,KAAK,EAAE;gBACtG,KAAK,EAAE,CAAC;aACX,CAAC,EAAE,CAAC;QACT,CAAC;IACL,CAAC;IAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACnB,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3C,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;IAC/E,CAAC;IAED,IAAI,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC;QACxB,OAAO,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAClD,KAAK,EAAE,CAAC;SACX,CAAC,CAAC;QAEH,yFAAyF;QACzF,8DAA8D;QAC9D,KAAK,MAAM,eAAe,IAAI,OAAO,EAAE,CAAC;YACpC,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;YACpE,IAAI,UAAU,EAAE,CAAC;gBACb,OAAO,GAAG,UAAU,CAAC;gBACrB,MAAM;YACV,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO,OAAO,CAAC,OAAO,CAA4B,CAAC;AACvD,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,CAAC,KAAyB,EAAE,EAAE;IAChD,IAAI,CAAC,CAAC,OAAO,IAAI,KAAK,CAAC,EAAE,CAAC;QACtB,oDAAoD;QACpD,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,KAAM,EAAE,CAAC;AACnB,CAAC,CAAC;AAEF;;;;;;;;;;;;;GAaG;AACH,MAAa,YAAY;IASrB,YAAY,UAAwC,EAAE;QARtD,wCAA8B;QAE9B;;;;;WAAgC;QAEhC;;;;;WAAc;QAEd;;;;;WAAoC;QAGhC,+BAAA,IAAI,yBAAY;YACZ,aAAa,EAAE,IAAI;YACnB,aAAa,EAAE,IAAI;YACnB,cAAc,EAAE,IAAI;YACpB,aAAa,EAAE,KAAK;YACpB,gBAAgB,EAAE,IAAI;YACtB,eAAe,EAAE,KAAK;YACtB,kBAAkB,EAAE,KAAK;YACzB,GAAG,OAAO;SACb,MAAA,CAAC;QAEF,IAAI,+BAAA,IAAI,6BAAS,CAAC,kBAAkB,EAAE,CAAC;YACnC,IAAI,CAAC,gBAAgB,GAAG,IAAI,oCAAgB,EAAE,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACnB,CAAC;IAEO,WAAW,CAAC,KAAqB;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAExB,IAAI,+BAAA,IAAI,6BAAS,CAAC,cAAc,EAAE,CAAC;YAC/B,KAAK,GAAG,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,+BAAA,IAAI,6BAAS,CAAC,aAAa,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,+BAAA,IAAI,6BAAS,CAAC,aAAa,EAAE,CAAC;YAC9B,KAAK,GAAG,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,+BAAA,IAAI,6BAAS,CAAC,aAAa,EAAE,CAAC;YAC9B,KAAK,GAAG,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,+BAAA,IAAI,6BAAS,CAAC,gBAAgB,EAAE,CAAC;YACjC,KAAK,GAAG,oBAAoB,CAAC,KAAK,EAAE,KAAK,EAAE,+BAAA,IAAI,6BAAS,CAAC,eAAe,CAAC,CAAC;QAC9E,CAAC;QAED,aAAa,CAAC,KAA0B,CAAC,CAAC;QAE1C,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,GAAG,CAAC,KAAqB;QACrB,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAExB,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1D,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,QAAQ,CAAC,KAAqB,EAAE,OAAyB;QAC3D,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEtC,+EAA+E;QAC/E,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI,OAAO,EAAE,CAAC;YAC/B,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1D,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;IAED,mBAAmB;QACf,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,MAAM,QAAQ,GAAG,CAAC,KAA8B,EAAQ,EAAE;YACtD,IAAI,OAAO,IAAI,KAAK,EAAE,CAAC;gBACnB,KAAK,EAAE,CAAC;gBACR,OAAO;YACX,CAAC;YAED,8DAA8D;YAC9D,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;gBACtB,QAAQ,CAAC,KAAK,CAAC,GAAG,CAA4B,CAAC,CAAC;YACpD,CAAC;QACL,CAAC,CAAC;QAEF,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEtB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,oBAAoB,CAAC,KAAa;QAC9B,MAAM,MAAM,GAAyB,EAAE,CAAC;QAExC,MAAM,QAAQ,GAAG,CAAC,KAA8B,EAAE,IAAc,EAAQ,EAAE;YACtE,IAAI,OAAO,IAAI,KAAK,EAAE,CAAC;gBACnB,MAAM,CAAC,IAAI,CAAC,CAAE,KAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;gBAC1C,OAAO;YACX,CAAC;YAED,8DAA8D;YAC9D,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;gBACtB,QAAQ,CAAC,KAAK,CAAC,GAAG,CAA4B,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;YACpE,CAAC;QACL,CAAC,CAAC;QAEF,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAE1B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAAgC,EAAE,KAAqB,EAAE,OAAwB;QACnG,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAEvG,OAAO,CAAC,uBAAuB,GAAG,iBAAiB,CAAC;QACpD,OAAO,CAAC,iBAAiB,GAAG,WAAW,CAAC;IAC5C,CAAC;IAED,KAAK;QACD,yFAAyF;QACzF,8DAA8D;QAC9D,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC;IACL,CAAC;CACJ;AA5ID,oCA4IC"}