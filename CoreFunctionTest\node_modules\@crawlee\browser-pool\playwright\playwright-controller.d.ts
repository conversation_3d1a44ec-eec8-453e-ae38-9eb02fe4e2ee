import type { <PERSON><PERSON> } from '@crawlee/types';
// @ts-ignore optional peer dependency or compatibility with es2022
import type { <PERSON><PERSON><PERSON>, BrowserType, Page } from 'playwright';
import { <PERSON><PERSON><PERSON><PERSON>ontroller } from '../abstract-classes/browser-controller';
import type { SafeParameters } from '../utils';
export declare class PlaywrightController extends Browser<PERSON><PERSON>roller<BrowserType, SafeParameters<BrowserType['launch']>[0], Browser> {
    normalizeProxyOptions(proxyUrl: string | undefined, pageOptions: any): Record<string, unknown>;
    protected _newPage(contextOptions?: SafeParameters<Browser['newPage']>[0]): Promise<Page>;
    protected _close(): Promise<void>;
    protected _kill(): Promise<void>;
    protected _getCookies(page: Page): Promise<Cookie[]>;
    protected _setCookies(page: Page, cookies: Cookie[]): Promise<void>;
}
//# sourceMappingURL=playwright-controller.d.ts.map