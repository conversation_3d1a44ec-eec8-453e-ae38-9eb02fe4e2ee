"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InstallPlaywrightBrowsersCommand = void 0;
const tslib_1 = require("tslib");
const node_child_process_1 = require("node:child_process");
const ansi_colors_1 = tslib_1.__importDefault(require("ansi-colors"));
const envVariable = 'CRAWLEE_SKIP_BROWSER_INSTALL';
class InstallPlaywrightBrowsersCommand {
    constructor() {
        Object.defineProperty(this, "command", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 'install-playwright-browsers'
        });
        Object.defineProperty(this, "describe", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 'Installs browsers needed by Playwright for local testing'
        });
        Object.defineProperty(this, "builder", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: async (args) => {
                args.options('force', {
                    alias: 'f',
                    default: false,
                    type: 'boolean',
                    describe: 'Use `--force` to force installation of browsers even if the environment is marked as having them.',
                });
                return args;
            }
        });
        Object.defineProperty(this, "handler", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: (args) => {
                if (process.env[envVariable]) {
                    if (!args.force) {
                        console.log(ansi_colors_1.default.green('Browsers are already installed!'));
                        return;
                    }
                    console.warn(ansi_colors_1.default.yellow('Installing Playwright browsers in an environment where browsers have already been installed...'));
                }
                else {
                    console.log(ansi_colors_1.default.green('Installing Playwright browsers...'));
                }
                // TODO: detect package manager
                (0, node_child_process_1.execSync)(`npx playwright install`, { stdio: 'inherit' });
            }
        });
    }
}
exports.InstallPlaywrightBrowsersCommand = InstallPlaywrightBrowsersCommand;
//# sourceMappingURL=InstallPlaywrightBrowsersCommand.js.map