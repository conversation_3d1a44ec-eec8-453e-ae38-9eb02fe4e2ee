# 🚀 批量浏览器管理后端系统

## 📋 项目简介

这是一个纯后端的批量浏览器管理系统，支持大规模并发浏览器实例管理、智能代理轮换和动态指纹生成。

## 🏗️ 系统架构

```
D:\IIIIII\
├── BatchBrowserController.cs          # 🎮 主控制器 - 统一管理所有功能
├── NodejsBatchEngineEnhanced.js       # 🚀 Node.js批量引擎 - 基于Playwright
├── NodejsEngineManager.cs             # 🔧 Node.js引擎管理器 - C#与Node.js通信桥梁
├── ProxyPoolManager.cs                # 🌐 代理池管理器 - 智能代理轮换
├── Models/                            # 📊 核心数据模型
│   ├── BrowserInstancePool.cs         #   - 浏览器实例池管理
│   ├── Fingerprint.cs                 #   - 指纹数据结构
│   ├── FingerprintGenerator.cs        #   - 动态指纹生成器
│   ├── PerformanceMonitor.cs          #   - 性能监控器
│   └── ProxyInfo.cs                   #   - 代理信息模型
├── package.json                       # 📦 Node.js依赖配置
├── proxies.txt                        # 🌐 代理服务器配置文件
└── node_modules/                      # 📚 Node.js依赖包
```

## ✨ 核心功能

### 🎯 批量浏览器管理
- **并发支持**: 最多50+浏览器实例同时运行
- **隔离环境**: 每个实例独立的数据目录和配置
- **生命周期管理**: 自动创建、监控、清理浏览器实例

### 🌐 智能代理轮换
- **代理池管理**: 支持数百个代理服务器
- **健康检查**: 自动检测代理可用性
- **故障转移**: 代理失效时自动切换备用代理

### 🎭 动态指纹生成
- **唯一指纹**: 每个浏览器实例生成独特指纹
- **防检测**: Canvas、WebGL、Audio指纹修改
- **随机化**: 用户代理、屏幕分辨率、时区等随机化

### 📊 性能监控
- **资源监控**: 实时监控CPU、内存使用情况
- **实例统计**: 活跃实例数量、成功率统计
- **性能优化**: 自动回收长时间未使用的实例

## 🚀 快速开始

### 1. 启动Node.js引擎
```bash
cd D:\IIIIII
node NodejsBatchEngineEnhanced.js
```

### 2. 配置代理服务器（可选）
编辑 `proxies.txt` 文件，格式：
```
服务器:端口:账号:密码
127.0.0.1:8080:username:password
```

### 3. 使用C#控制器
```csharp
using BatchBrowserManager;

// 创建控制器实例
var controller = new BatchBrowserController();

// 启动系统
await controller.StartSystemAsync();

// 创建浏览器实例
var result = await controller.CreateBrowserInstancesAsync(5);

// 执行批量操作
await controller.ExecuteBatchOperationAsync("https://example.com");

// 停止系统
await controller.StopSystemAsync();
```

## 🔧 API接口

### HTTP API (Node.js引擎)
- `POST /create-instances` - 创建浏览器实例
- `GET /instances` - 获取实例列表
- `POST /execute-batch` - 执行批量操作
- `DELETE /instances/:id` - 删除指定实例
- `GET /status` - 获取系统状态

### WebSocket API
- 实时状态更新
- 实例事件通知
- 性能监控数据推送

## 📋 系统要求

- **Node.js**: 16.0+ 
- **.NET**: 6.0+
- **Chrome浏览器**: 最新版本
- **内存**: 建议8GB+（大规模并发时）
- **操作系统**: Windows 10/11

## 🎯 使用场景

- **数据采集**: 大规模网站数据抓取
- **自动化测试**: 并发Web应用测试
- **SEO监控**: 搜索引擎排名监控
- **市场研究**: 竞品分析和价格监控
- **社交媒体**: 批量账号管理

## ⚠️ 注意事项

1. **合规使用**: 请遵守目标网站的robots.txt和使用条款
2. **资源控制**: 根据系统配置合理设置并发数量
3. **代理质量**: 使用高质量代理服务器以确保稳定性
4. **监控告警**: 建议配置资源使用监控和告警

## 🔗 技术栈

- **后端语言**: C# (.NET 6+)
- **自动化引擎**: Node.js + Playwright
- **Web框架**: Express.js
- **通信协议**: HTTP API + WebSocket
- **数据格式**: JSON
- **代理协议**: HTTP/HTTPS/SOCKS5

---

**版本**: v2.0  
**更新时间**: 2025-01-10  
**维护状态**: 活跃开发中
