var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);

// src/pseudo_url.ts
import { inspect } from "node:util";
import log from "@apify/log";
var _PseudoUrl = class _PseudoUrl {
  /**
   * @param purl
   *   A pseudo-URL string or a regular expression object.
   *   Using a `RegExp` instance enables more granular control,
   *   such as making the matching case-sensitive.
   */
  constructor(purl) {
    __publicField(this, "regex");
    if (purl instanceof RegExp) {
      this.regex = purl;
    } else if (typeof purl === "string") {
      this.regex = purlToRegExp(purl);
      log.debug("PURL parsed", { purl, regex: this.regex });
    } else {
      const type = Array.isArray(purl) ? "array" : typeof purl;
      throw new Error(`Invalid PseudoUrl format, 'string' or 'RegExp' required, got \`${inspect(purl)}\` of type '${type}' instead`);
    }
  }
  /**
   * Determines whether a URL matches this pseudo-URL pattern.
   */
  matches(url) {
    return typeof url === "string" && url.match(this.regex) !== null;
  }
};
__name(_PseudoUrl, "PseudoUrl");
var PseudoUrl = _PseudoUrl;
function purlToRegExp(purl) {
  const trimmedPurl = purl.trim();
  if (trimmedPurl.length === 0) throw new Error(`Cannot parse PURL '${trimmedPurl}': it must be an non-empty string`);
  let regex = "^";
  try {
    let openBrackets = 0;
    for (let i = 0; i < trimmedPurl.length; i++) {
      const ch = trimmedPurl.charAt(i);
      if (ch === "[" && ++openBrackets === 1) {
        regex += "(";
      } else if (ch === "]" && openBrackets > 0 && --openBrackets === 0) {
        regex += ")";
      } else if (openBrackets > 0) {
        regex += ch;
      } else {
        const code = ch.charCodeAt(0);
        if (code >= 48 && code <= 57 || code >= 65 && code <= 90 || code >= 97 && code <= 122) {
          regex += ch;
        } else {
          const hex = code < 16 ? `0${code.toString(16)}` : code.toString(16);
          regex += `\\x${hex}`;
        }
      }
    }
    regex += "$";
  } catch (err) {
    throw new Error(`Cannot parse PURL '${purl}': ${err}`);
  }
  return new RegExp(regex, "i");
}
__name(purlToRegExp, "purlToRegExp");
export {
  PseudoUrl,
  purlToRegExp
};
//# sourceMappingURL=index.mjs.map