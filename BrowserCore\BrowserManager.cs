using Microsoft.Playwright;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace BrowserCore
{
    /// <summary>
    /// 浏览器管理器 - 独立DLL模块
    /// 功能说明：提供浏览器实例的创建、管理、操作等核心功能
    /// </summary>
    public class BrowserManager : IDisposable
    {
        private readonly ILogger<BrowserManager>? _logger;
        private IPlaywright? _playwright;
        private readonly Dictionary<string, IBrowser> _browsers;
        private readonly Dictionary<string, BrowserInstanceInfo> _instances;
        private bool _isInitialized = false;

        public BrowserManager(ILogger<BrowserManager>? logger = null)
        {
            _logger = logger;
            _browsers = new Dictionary<string, IBrowser>();
            _instances = new Dictionary<string, BrowserInstanceInfo>();
        }

        /// <summary>
        /// 初始化浏览器管理器
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger?.LogInformation("🔧 正在初始化Playwright浏览器引擎...");
                
                _playwright = await Playwright.CreateAsync();
                _isInitialized = true;
                
                _logger?.LogInformation("✅ 浏览器管理器初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 浏览器管理器初始化失败");
                return false;
            }
        }

        /// <summary>
        /// 创建浏览器实例
        /// </summary>
        /// <param name="config">浏览器配置</param>
        /// <returns>实例ID</returns>
        public async Task<string?> CreateBrowserAsync(BrowserConfig config)
        {
            if (!_isInitialized || _playwright == null)
            {
                _logger?.LogError("❌ 浏览器管理器未初始化");
                return null;
            }

            try
            {
                var instanceId = GenerateInstanceId();
                
                // 配置启动选项
                var launchOptions = new BrowserTypeLaunchOptions
                {
                    Headless = config.Headless,
                    ExecutablePath = config.ChromePath,
                    Args = config.Args?.ToArray()
                };

                // 如果指定了用户数据目录，需要通过Args传递
                if (!string.IsNullOrEmpty(config.UserDataDir))
                {
                    var argsList = launchOptions.Args?.ToList() ?? new List<string>();
                    argsList.Add($"--user-data-dir={config.UserDataDir}");
                    launchOptions.Args = argsList.ToArray();
                }

                // 启动浏览器
                var browser = await _playwright.Chromium.LaunchAsync(launchOptions);
                
                // 创建实例信息
                var instanceInfo = new BrowserInstanceInfo
                {
                    Id = instanceId,
                    Status = "active",
                    CreatedAt = DateTime.Now,
                    Config = config
                };

                // 保存实例
                _browsers[instanceId] = browser;
                _instances[instanceId] = instanceInfo;
                
                _logger?.LogInformation($"✅ 浏览器实例创建成功: {instanceId}");
                return instanceId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 创建浏览器实例失败");
                return null;
            }
        }

        /// <summary>
        /// 获取浏览器实例
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <returns>浏览器实例</returns>
        public IBrowser? GetBrowser(string instanceId)
        {
            return _browsers.TryGetValue(instanceId, out var browser) ? browser : null;
        }

        /// <summary>
        /// 创建新页面
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <returns>页面对象</returns>
        public async Task<IPage?> CreatePageAsync(string instanceId)
        {
            var browser = GetBrowser(instanceId);
            if (browser == null)
            {
                _logger?.LogError($"❌ 浏览器实例不存在: {instanceId}");
                return null;
            }

            try
            {
                var page = await browser.NewPageAsync();
                _logger?.LogInformation($"📄 新页面创建成功: {instanceId}");
                return page;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 创建页面失败: {instanceId}");
                return null;
            }
        }

        /// <summary>
        /// 导航到指定URL
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <param name="url">目标URL</param>
        /// <returns>导航结果</returns>
        public async Task<NavigationResult> NavigateAsync(string instanceId, string url)
        {
            var page = await CreatePageAsync(instanceId);
            if (page == null)
            {
                return new NavigationResult { Success = false, Message = "无法创建页面" };
            }

            try
            {
                var response = await page.GotoAsync(url);
                var success = response?.Ok ?? false;
                
                if (_instances.TryGetValue(instanceId, out var instance))
                {
                    instance.CurrentUrl = url;
                    instance.LastActivity = DateTime.Now;
                }

                _logger?.LogInformation($"🌐 导航成功: {instanceId} -> {url}");
                
                return new NavigationResult 
                { 
                    Success = success, 
                    Message = success ? "导航成功" : "导航失败",
                    Url = url,
                    StatusCode = response?.Status
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 导航失败: {instanceId} -> {url}");
                return new NavigationResult { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// 执行JavaScript脚本
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <param name="script">JavaScript代码</param>
        /// <returns>执行结果</returns>
        public async Task<ScriptResult> ExecuteScriptAsync(string instanceId, string script)
        {
            var browser = GetBrowser(instanceId);
            if (browser == null)
            {
                return new ScriptResult { Success = false, Message = "浏览器实例不存在" };
            }

            try
            {
                var pages = browser.Contexts.SelectMany(c => c.Pages).ToList();
                if (!pages.Any())
                {
                    return new ScriptResult { Success = false, Message = "没有可用页面" };
                }

                var page = pages.First();
                var result = await page.EvaluateAsync(script);
                
                _logger?.LogInformation($"📜 脚本执行成功: {instanceId}");
                
                return new ScriptResult 
                { 
                    Success = true, 
                    Message = "脚本执行成功",
                    Result = result
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 脚本执行失败: {instanceId}");
                return new ScriptResult { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// 截图
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <param name="options">截图选项</param>
        /// <returns>截图数据</returns>
        public async Task<ScreenshotResult> TakeScreenshotAsync(string instanceId, ScreenshotOptions? options = null)
        {
            var browser = GetBrowser(instanceId);
            if (browser == null)
            {
                return new ScreenshotResult { Success = false, Message = "浏览器实例不存在" };
            }

            try
            {
                var pages = browser.Contexts.SelectMany(c => c.Pages).ToList();
                if (!pages.Any())
                {
                    return new ScreenshotResult { Success = false, Message = "没有可用页面" };
                }

                var page = pages.First();
                var screenshotOptions = new PageScreenshotOptions
                {
                    FullPage = options?.FullPage ?? false,
                    Type = options?.Format == "jpeg" ? ScreenshotType.Jpeg : ScreenshotType.Png
                };

                var screenshot = await page.ScreenshotAsync(screenshotOptions);
                
                _logger?.LogInformation($"📸 截图成功: {instanceId}");
                
                return new ScreenshotResult 
                { 
                    Success = true, 
                    Message = "截图成功",
                    Data = screenshot,
                    Format = options?.Format ?? "png"
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 截图失败: {instanceId}");
                return new ScreenshotResult { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// 关闭浏览器实例
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <returns>关闭结果</returns>
        public async Task<bool> CloseBrowserAsync(string instanceId)
        {
            try
            {
                if (_browsers.TryGetValue(instanceId, out var browser))
                {
                    await browser.CloseAsync();
                    await browser.DisposeAsync();
                    _browsers.Remove(instanceId);
                }

                if (_instances.ContainsKey(instanceId))
                {
                    _instances.Remove(instanceId);
                }

                _logger?.LogInformation($"🗑️ 浏览器实例已关闭: {instanceId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"❌ 关闭浏览器实例失败: {instanceId}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有实例信息
        /// </summary>
        /// <returns>实例列表</returns>
        public List<BrowserInstanceInfo> GetAllInstances()
        {
            return _instances.Values.ToList();
        }

        /// <summary>
        /// 获取实例统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public BrowserStats GetStats()
        {
            return new BrowserStats
            {
                TotalInstances = _instances.Count,
                ActiveInstances = _instances.Values.Count(i => i.Status == "active"),
                CreatedToday = _instances.Values.Count(i => i.CreatedAt.Date == DateTime.Today)
            };
        }

        /// <summary>
        /// 生成实例ID
        /// </summary>
        private string GenerateInstanceId()
        {
            return $"browser-{DateTime.Now.Ticks:X}-{Random.Shared.Next(1000, 9999):D3}";
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 关闭所有浏览器实例
                foreach (var browser in _browsers.Values)
                {
                    browser?.CloseAsync().Wait(5000);
                    browser?.DisposeAsync().AsTask().Wait(5000);
                }
                
                _browsers.Clear();
                _instances.Clear();
                
                // 释放Playwright
                _playwright?.Dispose();
                
                _logger?.LogInformation("🔄 浏览器管理器已释放资源");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "❌ 释放资源时发生错误");
            }
        }
    }

    #region 数据模型

    /// <summary>
    /// 浏览器配置
    /// </summary>
    public class BrowserConfig
    {
        public bool Headless { get; set; } = false;
        public string? ChromePath { get; set; }
        public List<string>? Args { get; set; }
        public string? UserDataDir { get; set; }
        public string? ProxyServer { get; set; }
    }

    /// <summary>
    /// 浏览器实例信息
    /// </summary>
    public class BrowserInstanceInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? LastActivity { get; set; }
        public string? CurrentUrl { get; set; }
        public BrowserConfig? Config { get; set; }
    }

    /// <summary>
    /// 导航结果
    /// </summary>
    public class NavigationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? Url { get; set; }
        public int? StatusCode { get; set; }
    }

    /// <summary>
    /// 脚本执行结果
    /// </summary>
    public class ScriptResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public object? Result { get; set; }
    }

    /// <summary>
    /// 截图选项
    /// </summary>
    public class ScreenshotOptions
    {
        public bool FullPage { get; set; } = false;
        public string Format { get; set; } = "png";
        public int? Quality { get; set; }
    }

    /// <summary>
    /// 截图结果
    /// </summary>
    public class ScreenshotResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public byte[]? Data { get; set; }
        public string Format { get; set; } = "png";
    }

    /// <summary>
    /// 浏览器统计信息
    /// </summary>
    public class BrowserStats
    {
        public int TotalInstances { get; set; }
        public int ActiveInstances { get; set; }
        public int CreatedToday { get; set; }
    }

    #endregion
}
