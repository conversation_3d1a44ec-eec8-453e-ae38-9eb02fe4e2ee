{"version": 3, "file": "browser-controller.d.ts", "sourceRoot": "", "sources": ["../../src/abstract-classes/browser-controller.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAEzD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAIlD,OAAO,EAAE,yBAAyB,EAAE,MAAM,WAAW,CAAC;AACtD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAEvD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAC9C,OAAO,KAAK,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAIpF,MAAM,WAAW,uBAAuB,CACpC,OAAO,SAAS,aAAa,EAC7B,cAAc,SAAS,UAAU,GAAG,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAChF,YAAY,SAAS,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EACjF,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EACvD,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;IAElE,CAAC,yBAAyB,CAAC,cAAc,CAAC,EAAE,CACxC,UAAU,EAAE,iBAAiB,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC,KAClG,IAAI,CAAC;CACb;AAED;;;;;;;GAOG;AACH,8BAAsB,iBAAiB,CACnC,OAAO,SAAS,aAAa,GAAG,aAAa,EAC7C,cAAc,SAAS,UAAU,GAAG,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAChF,YAAY,SAAS,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EACjF,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EACvD,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CACpE,SAAQ,YAAY,CAAC,uBAAuB,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;IACjH,EAAE,SAAY;IAEd;;OAEG;IACH,aAAa,EAAE,aAAa,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;IAEnG;;OAEG;IACH,OAAO,EAAE,YAAY,CAAc;IAEnC;;OAEG;IACH,aAAa,EAAE,aAAa,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC,CAAc;IAEhH;;;OAGG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB,QAAQ,UAAS;IAEjB,WAAW,SAAK;IAEhB,UAAU,SAAK;IAEf,gBAAgB,SAAc;IAE9B,OAAO,CAAC,SAAS,CAAc;IAE/B,OAAO,CAAC,eAAe,CAEpB;IAEH,OAAO,CAAC,aAAa,CAAc;IAEnC,OAAO,CAAC,iBAAiB,CAEtB;gBAES,aAAa,EAAE,aAAa,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC;IAK9G;;;;;OAKG;IACH,QAAQ,IAAI,IAAI;IAQhB;;OAEG;IACH,aAAa,CACT,OAAO,EAAE,YAAY,EACrB,aAAa,EAAE,aAAa,CAAC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC,GACnG,IAAI;IASP;;;;;OAKG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAoB5B;;;;OAIG;IACG,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAM3B;;;OAGG;IACG,OAAO,CAAC,WAAW,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC,aAAa,CAAC;IAW7D,UAAU,CAAC,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAIjE,UAAU,CAAC,IAAI,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAIxD;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAC1C;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IACzC;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,cAAc,GAAG,OAAO,CAAC,aAAa,CAAC;IAEjF;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAErF;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAEtE;;OAEG;IACH,QAAQ,CAAC,qBAAqB,CAAC,QAAQ,EAAE,MAAM,GAAG,SAAS,EAAE,WAAW,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;CAC1G"}