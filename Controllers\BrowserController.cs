using Microsoft.AspNetCore.Mvc;
using BatchBrowserEngine.Services;
using BatchBrowserEngine.Models;

namespace BatchBrowserEngine.Controllers
{
    /// <summary>
    /// 浏览器管理API控制器
    /// 功能说明：提供浏览器实例的创建、管理、操作等RESTful API
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class BrowserController : ControllerBase
    {
        private readonly BrowserEngineService _browserEngine;
        private readonly ProxyService _proxyService;
        private readonly ILogger<BrowserController> _logger;

        public BrowserController(
            BrowserEngineService browserEngine,
            ProxyService proxyService,
            ILogger<BrowserController> logger)
        {
            _browserEngine = browserEngine ?? throw new ArgumentNullException(nameof(browserEngine));
            _proxyService = proxyService ?? throw new ArgumentNullException(nameof(proxyService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 创建浏览器实例
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建结果</returns>
        [HttpPost("instances")]
        public async Task<ActionResult<ApiResponse<CreateInstancesResult>>> CreateInstances(
            [FromBody] CreateInstancesRequest request)
        {
            try
            {
                _logger.LogInformation($"📝 收到创建实例请求: {request.Count} 个实例");

                if (request.Count <= 0 || request.Count > 50)
                {
                    return BadRequest(ApiResponse<CreateInstancesResult>.ErrorResult(
                        "实例数量必须在1-50之间", "INVALID_COUNT"));
                }

                var result = await _browserEngine.CreateInstancesAsync(request.Count, request.TargetUrl);
                
                if (result.Success)
                {
                    return Ok(ApiResponse<CreateInstancesResult>.SuccessResult(result, 
                        $"成功创建 {result.CreatedCount} 个浏览器实例"));
                }
                else
                {
                    return BadRequest(ApiResponse<CreateInstancesResult>.ErrorResult(result.Message));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 创建浏览器实例失败");
                return StatusCode(500, ApiResponse<CreateInstancesResult>.ErrorResult(
                    "服务器内部错误", "INTERNAL_ERROR"));
            }
        }

        /// <summary>
        /// 获取所有浏览器实例
        /// </summary>
        /// <returns>实例列表</returns>
        [HttpGet("instances")]
        public async Task<ActionResult<ApiResponse<List<BrowserInstanceInfo>>>> GetInstances()
        {
            try
            {
                var instances = await _browserEngine.GetInstancesAsync();
                
                _logger.LogInformation($"📊 返回 {instances.Count} 个浏览器实例");
                
                return Ok(ApiResponse<List<BrowserInstanceInfo>>.SuccessResult(instances,
                    $"获取到 {instances.Count} 个实例"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 获取浏览器实例失败");
                return StatusCode(500, ApiResponse<List<BrowserInstanceInfo>>.ErrorResult(
                    "服务器内部错误", "INTERNAL_ERROR"));
            }
        }

        /// <summary>
        /// 获取单个浏览器实例
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <returns>实例信息</returns>
        [HttpGet("instances/{instanceId}")]
        public async Task<ActionResult<ApiResponse<BrowserInstanceInfo>>> GetInstance(string instanceId)
        {
            try
            {
                var instances = await _browserEngine.GetInstancesAsync();
                var instance = instances.FirstOrDefault(i => i.Id == instanceId);
                
                if (instance == null)
                {
                    return NotFound(ApiResponse<BrowserInstanceInfo>.ErrorResult(
                        $"实例 {instanceId} 不存在", "INSTANCE_NOT_FOUND"));
                }
                
                return Ok(ApiResponse<BrowserInstanceInfo>.SuccessResult(instance));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ 获取实例 {instanceId} 失败");
                return StatusCode(500, ApiResponse<BrowserInstanceInfo>.ErrorResult(
                    "服务器内部错误", "INTERNAL_ERROR"));
            }
        }

        /// <summary>
        /// 删除浏览器实例
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("instances/{instanceId}")]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteInstance(string instanceId)
        {
            try
            {
                _logger.LogInformation($"🗑️ 删除实例请求: {instanceId}");
                
                var success = await _browserEngine.DeleteInstanceAsync(instanceId);
                
                if (success)
                {
                    return Ok(ApiResponse<bool>.SuccessResult(true, $"实例 {instanceId} 已删除"));
                }
                else
                {
                    return NotFound(ApiResponse<bool>.ErrorResult(
                        $"实例 {instanceId} 不存在", "INSTANCE_NOT_FOUND"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ 删除实例 {instanceId} 失败");
                return StatusCode(500, ApiResponse<bool>.ErrorResult(
                    "服务器内部错误", "INTERNAL_ERROR"));
            }
        }

        /// <summary>
        /// 批量删除浏览器实例
        /// </summary>
        /// <param name="instanceIds">实例ID列表</param>
        /// <returns>删除结果</returns>
        [HttpDelete("instances")]
        public async Task<ActionResult<ApiResponse<BatchOperationResult>>> DeleteInstances(
            [FromBody] List<string> instanceIds)
        {
            try
            {
                _logger.LogInformation($"🗑️ 批量删除实例请求: {instanceIds.Count} 个实例");
                
                var result = new BatchOperationResult { Success = true };
                
                foreach (var instanceId in instanceIds)
                {
                    var success = await _browserEngine.DeleteInstanceAsync(instanceId);
                    result.Results.Add(new InstanceOperationResult
                    {
                        InstanceId = instanceId,
                        Success = success,
                        Message = success ? "删除成功" : "实例不存在"
                    });
                }
                
                result.Message = $"删除完成，成功: {result.SuccessCount}, 失败: {result.FailureCount}";
                
                return Ok(ApiResponse<BatchOperationResult>.SuccessResult(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 批量删除实例失败");
                return StatusCode(500, ApiResponse<BatchOperationResult>.ErrorResult(
                    "服务器内部错误", "INTERNAL_ERROR"));
            }
        }

        /// <summary>
        /// 获取系统状态
        /// </summary>
        /// <returns>系统状态</returns>
        [HttpGet("status")]
        public async Task<ActionResult<ApiResponse<SystemStatus>>> GetStatus()
        {
            try
            {
                var instances = await _browserEngine.GetInstancesAsync();
                var proxyStats = _proxyService.GetStats();
                
                var status = new SystemStatus
                {
                    IsRunning = true,
                    ActiveInstances = instances.Count,
                    MaxConcurrency = 50,
                    ProxyStats = proxyStats,
                    StartTime = DateTime.Now.AddHours(-1), // 临时值，应该从服务启动时间获取
                    Resources = GetSystemResources()
                };
                
                return Ok(ApiResponse<SystemStatus>.SuccessResult(status));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 获取系统状态失败");
                return StatusCode(500, ApiResponse<SystemStatus>.ErrorResult(
                    "服务器内部错误", "INTERNAL_ERROR"));
            }
        }

        /// <summary>
        /// 获取代理池统计
        /// </summary>
        /// <returns>代理池统计</returns>
        [HttpGet("proxy-stats")]
        public ActionResult<ApiResponse<ProxyPoolStats>> GetProxyStats()
        {
            try
            {
                var stats = _proxyService.GetStats();
                return Ok(ApiResponse<ProxyPoolStats>.SuccessResult(stats));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ 获取代理统计失败");
                return StatusCode(500, ApiResponse<ProxyPoolStats>.ErrorResult(
                    "服务器内部错误", "INTERNAL_ERROR"));
            }
        }

        /// <summary>
        /// 健康检查端点
        /// </summary>
        /// <returns>健康状态</returns>
        [HttpGet("health")]
        public ActionResult<ApiResponse<object>> HealthCheck()
        {
            return Ok(ApiResponse<object>.SuccessResult(new 
            { 
                status = "healthy", 
                timestamp = DateTime.Now,
                version = "2.0.0"
            }));
        }

        /// <summary>
        /// 获取系统资源使用情况
        /// </summary>
        private SystemResources GetSystemResources()
        {
            try
            {
                var process = System.Diagnostics.Process.GetCurrentProcess();
                
                return new SystemResources
                {
                    MemoryUsage = process.WorkingSet64,
                    MemoryTotal = GC.GetTotalMemory(false),
                    ThreadCount = process.Threads.Count,
                    HandleCount = process.HandleCount
                };
            }
            catch
            {
                return new SystemResources();
            }
        }
    }
}
