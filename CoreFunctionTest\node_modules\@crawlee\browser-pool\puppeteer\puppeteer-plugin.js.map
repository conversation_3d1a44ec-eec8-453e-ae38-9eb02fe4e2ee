{"version": 3, "file": "puppeteer-plugin.js", "sourceRoot": "", "sources": ["../../src/puppeteer/puppeteer-plugin.ts"], "names": [], "mappings": ";;;AAAA,+CAA4C;AAO5C,uEAAmE;AACnE,wDAAyD;AAEzD,sCAAgC;AAChC,oCAAgC;AAEhC,iEAA6D;AAE7D,MAAM,gBAAgB,GAAG,iBAAiB,CAAC;AAE3C,MAAa,eAAgB,SAAQ,8BAKpC;IACa,KAAK,CAAC,OAAO,CACnB,aAKC;QAED,IAAI,mBAAmB,GAAG,KAAK,CAAC;QAEhC,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,IAAA,mBAAQ,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;YAC7D,MAAM,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,mBAAmB,GAAG,OAAO,GAAG,EAAE,CAAC;QACvC,CAAC;QAAC,MAAM,CAAC;YACL,SAAS;QACb,CAAC;QACD,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,iBAAiB,EAAE,sBAAsB,EAAE,QAAQ,EAAE,GAAG,aAAa,CAAC;QAE1G,IAAI,sBAAsB,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;QAClF,CAAC;QAED,aAAc,CAAC,WAAW,GAAG,aAAc,CAAC,WAAW,IAAI,WAAW,CAAC;QAEvE,IAAI,aAAc,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;YACpC,IAAI,KAAK,CAAC,OAAO,CAAC,aAAc,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,aAAc,CAAC,IAAI,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACJ,aAAc,CAAC,IAAI,GAAG,CAAC,iCAAiC,CAAC,CAAC;YAC9D,CAAC;QACL,CAAC;QAED,IAAI,aAAc,CAAC,QAAQ,KAAK,IAAI,IAAI,mBAAmB,EAAE,CAAC;YAC1D,aAAc,CAAC,QAAQ,GAAG,KAAY,CAAC;QAC3C,CAAC;QAED,IAAI,OAA+B,CAAC;QAEpC,CAAC;YACG,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC,GAAG,MAAM,IAAA,qCAAmB,EAAC,QAAQ,CAAC,CAAC;YAExE,IAAI,QAAQ,EAAE,CAAC;gBACX,MAAM,QAAQ,GAAG,GAAG,gBAAgB,GAAG,kBAAkB,IAAI,QAAQ,EAAE,CAAC;gBAExE,IAAI,KAAK,CAAC,OAAO,CAAC,aAAc,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrC,aAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACvC,CAAC;qBAAM,CAAC;oBACJ,aAAc,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACrC,CAAC;YACL,CAAC;YAED,IAAI,CAAC;gBACD,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;gBAEnD,IAAI,kBAAkB,EAAE,CAAC;oBACrB,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;wBAClC,MAAM,KAAK,EAAE,CAAC;oBAClB,CAAC,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBAClB,MAAM,KAAK,EAAE,CAAC;gBAEd,IAAI,CAAC,0BAA0B,CAC3B,KAAK,EACL,aAAa,CAAC,aAAa,EAAE,cAAc,EAC3C,qCAAqC,EACrC,2MAA2M,CAC9M,CAAC;YACN,CAAC;QACL,CAAC;QAED,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,KAAK,EAAE,MAA6B,EAAE,EAAE;YAChE,IAAI,CAAC;gBACD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBAEjC,IAAI,IAAI,EAAE,CAAC;oBACP,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;wBACvB,YAAG,CAAC,SAAS,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;wBACtC,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,YAAI,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBAClB,YAAG,CAAC,SAAS,CAAC,KAAK,EAAE,sCAAsC,CAAC,CAAC;YACjE,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GACd;YACI,SAAS;YACT,OAAO;YACP,WAAW;YACX,+BAA+B;YAC/B,sBAAsB;YACtB,SAAS;YACT,IAAI;YACJ,SAAS;SAEhB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACrB,GAAG,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAiB,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACxD,OAAO,GAAG,CAAC;QACf,CAAC,EAAE,EAAgB,CAAC,CAAC;QACrB,MAAM,MAAM,GAAG,mBAAmB,CAAC,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAAC,sBAAsB,CAAC;QAE9F,OAAO,GAAG,IAAI,KAAK,CAAC,OAAO,EAAE;YACzB,GAAG,EAAE,CAAC,MAAM,EAAE,QAA8B,EAAE,QAAQ,EAAE,EAAE;gBACtD,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBACzB,OAAO,KAAK,EAAE,GAAG,IAA0D,EAAE,EAAE;wBAC3E,IAAI,IAAyB,CAAC;wBAE9B,IAAI,iBAAiB,EAAE,CAAC;4BACpB,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC,GAAG,MAAM,IAAA,qCAAmB,EAAC,QAAQ,CAAC,CAAC;4BAExE,IAAI,CAAC;gCACD,MAAM,OAAO,GAAG,CAAC,MAAO,OAAe,CAAC,MAAM,CAAC,CAAC;oCAC5C,WAAW,EAAE,kBAAkB,IAAI,QAAQ;iCAC9C,CAAC,CAAkC,CAAC;gCAErC,IAAI,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;gCAEtC,IAAI,kBAAkB,EAAE,CAAC;oCACrB,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;wCACxB,MAAM,KAAK,EAAE,CAAC;oCAClB,CAAC,CAAC,CAAC;gCACP,CAAC;4BACL,CAAC;4BAAC,OAAO,KAAK,EAAE,CAAC;gCACb,MAAM,KAAK,EAAE,CAAC;gCAEd,MAAM,KAAK,CAAC;4BAChB,CAAC;wBACL,CAAC;6BAAM,CAAC;4BACJ,IAAI,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;wBAC/C,CAAC;wBAED;;;;;;;;;;;;;;;0BAeE;wBAEF,OAAO,IAAI,CAAC;oBAChB,CAAC,CAAC;gBACN,CAAC;gBAED,IAAI,QAAQ,IAAI,YAAY,EAAE,CAAC;oBAC3B,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAClC,CAAC;gBAED,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACnD,CAAC;SACJ,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACnB,CAAC;IAES,iBAAiB;QAMvB,OAAO,IAAI,0CAAmB,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAES,KAAK,CAAC,wBAAwB,CACpC,cAKC;QAED;;;;;;;;;;;;;;;;;;;;;;;;UAwBE;IACN,CAAC;IAES,uBAAuB,CAC7B,cAKC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAnOD,0CAmOC"}