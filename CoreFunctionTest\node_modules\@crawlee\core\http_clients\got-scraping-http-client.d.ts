import type { BaseHttpClient, HttpRequest, HttpResponse, RedirectHandler, ResponseTypes, StreamingHttpResponse } from './base-http-client';
/**
 * A HTTP client implementation based on the `got-scraping` library.
 */
export declare class GotScrapingHttpClient implements BaseHttpClient {
    /**
     * @inheritDoc
     */
    sendRequest<TResponseType extends keyof ResponseTypes>(request: HttpRequest<TResponseType>): Promise<HttpResponse<TResponseType>>;
    /**
     * @inheritDoc
     */
    stream(request: HttpRequest, handleRedirect?: RedirectHandler): Promise<StreamingHttpResponse>;
}
//# sourceMappingURL=got-scraping-http-client.d.ts.map