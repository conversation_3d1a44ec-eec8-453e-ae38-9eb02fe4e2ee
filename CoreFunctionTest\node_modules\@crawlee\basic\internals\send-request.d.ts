import { type BaseHttpClient, type HttpRequestOptions, type Request, type Session } from '@crawlee/core';
// @ts-ignore optional peer dependency or compatibility with es2022
import type { GotResponse } from 'got-scraping';
/**
 * Prepares a function to be used as the `sendRequest` context helper.
 *
 * @internal
 * @param httpClient The HTTP client that will perform the requests.
 * @param originRequest The crawling request being processed.
 * @param session The user session associated with the current request.
 * @param getProxyUrl A function that will return the proxy URL that should be used for handling the request.
 */
export declare function createSendRequest(httpClient: BaseHttpClient, originRequest: Request, session: Session | undefined, getProxyUrl: () => string | undefined): <Response = string>(overrideOptions?: Partial<HttpRequestOptions>) => Promise<GotResponse<Response>>;
//# sourceMappingURL=send-request.d.ts.map