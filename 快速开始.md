# 🚀 快速开始指南

## 📋 系统要求

- ✅ **.NET 8.0 SDK** 或更高版本
- ✅ **Windows 10/11** 操作系统  
- ✅ **Chrome浏览器** (用于BrowserCore模块)
- ✅ **8GB内存** 推荐 (大规模使用时)

## 🔧 第一步：构建所有DLL模块

### 方法1：使用构建脚本（推荐）
```bash
# 双击运行或在命令行执行
build-all-dlls.bat
```

### 方法2：手动构建
```bash
# 分别进入每个模块目录构建
cd BrowserCore
dotnet build --configuration Release

cd ..\ProxyManager  
dotnet build --configuration Release

cd ..\FingerprintGenerator
dotnet build --configuration Release

cd ..\InstanceManager
dotnet build --configuration Release

cd ..\PerformanceMonitor
dotnet build --configuration Release

cd ..\DataPersistence
dotnet build --configuration Release
```

## 🧪 第二步：运行测试程序

```bash
cd DllTestConsole
dotnet run
```

测试程序将依次测试所有6个DLL模块的功能。

## 💡 第三步：在您的项目中使用

### 创建新项目
```bash
dotnet new console -n MyBrowserProject
cd MyBrowserProject
```

### 添加DLL引用
```xml
<ItemGroup>
  <Reference Include="BrowserCore">
    <HintPath>D:\IIIIII\dist\dlls\BrowserCore.dll</HintPath>
  </Reference>
  <Reference Include="ProxyManager">
    <HintPath>D:\IIIIII\dist\dlls\ProxyManager.dll</HintPath>
  </Reference>
  <!-- 添加其他需要的DLL引用 -->
</ItemGroup>
```

### 基础使用示例
```csharp
using BrowserCore;
using ProxyManager;
using FingerprintGenerator;
using Microsoft.Extensions.Logging;

// 配置日志
using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
var logger = loggerFactory.CreateLogger<Program>();

// 1. 初始化各个模块
using var browserManager = new BrowserManager(logger);
using var proxyManager = new ProxyPoolManager(logger);
using var fingerprintManager = new FingerprintManager(logger);

// 2. 初始化模块
await browserManager.InitializeAsync();
await proxyManager.InitializeAsync("proxies.txt");
await fingerprintManager.InitializeAsync(50);

// 3. 获取代理和指纹
var proxy = proxyManager.GetNextProxy();
var fingerprint = fingerprintManager.GetRandomFingerprint();

// 4. 创建浏览器实例
var config = new BrowserConfig
{
    Headless = false,
    ProxyServer = proxy?.Address,
    Args = new List<string> { "--no-first-run" }
};

var instanceId = await browserManager.CreateBrowserAsync(config);

// 5. 执行操作
if (!string.IsNullOrEmpty(instanceId))
{
    var result = await browserManager.NavigateAsync(instanceId, "https://example.com");
    Console.WriteLine($"导航结果: {result.Success} - {result.Message}");
    
    // 关闭浏览器
    await browserManager.CloseBrowserAsync(instanceId);
}
```

## 🎯 常用场景示例

### 场景1：批量数据采集
```csharp
// 创建多个浏览器实例进行并发采集
var tasks = new List<Task>();
for (int i = 0; i < 5; i++)
{
    tasks.Add(CreateAndNavigateAsync(i));
}
await Task.WhenAll(tasks);

async Task CreateAndNavigateAsync(int index)
{
    var proxy = proxyManager.GetNextProxy();
    var fingerprint = fingerprintManager.GetRandomFingerprint();
    
    var config = new BrowserConfig
    {
        Headless = true,
        ProxyServer = proxy?.Address
    };
    
    var instanceId = await browserManager.CreateBrowserAsync(config);
    if (!string.IsNullOrEmpty(instanceId))
    {
        await browserManager.NavigateAsync(instanceId, $"https://example.com/page{index}");
        // 执行数据采集逻辑
        await browserManager.CloseBrowserAsync(instanceId);
    }
}
```

### 场景2：性能监控
```csharp
using var monitor = new SystemMonitor(logger: logger);

// 定期检查性能
while (true)
{
    var metrics = monitor.GetCurrentMetrics();
    var alerts = monitor.CheckAlerts();
    
    Console.WriteLine($"CPU: {metrics.CpuUsage:F1}%, 内存: {metrics.MemoryUsage:F1}%");
    
    if (alerts.Any())
    {
        Console.WriteLine($"⚠️ 发现 {alerts.Count} 个性能告警");
        foreach (var alert in alerts)
        {
            Console.WriteLine($"  - {alert.Message}");
        }
    }
    
    await Task.Delay(5000); // 每5秒检查一次
}
```

### 场景3：数据持久化
```csharp
using var dataManager = new DataManager(logger: logger);
await dataManager.InitializeAsync();

// 保存配置
var config = new { MaxInstances = 10, Timeout = 30 };
await dataManager.SaveConfigAsync("app_config", config);

// 保存采集结果
var results = new List<object> { /* 采集到的数据 */ };
await dataManager.SaveToJsonAsync(results, "results.json");

// 加载配置
var loadedConfig = await dataManager.LoadConfigAsync<object>("app_config");
```

## 📚 更多资源

- 📖 **详细文档**: [README_DLL_MODULES.md](README_DLL_MODULES.md)
- 🏗️ **项目结构**: [项目结构说明.md](项目结构说明.md)
- 🧪 **测试示例**: [DllTestConsole/Program.cs](DllTestConsole/Program.cs)

## ❓ 常见问题

### Q: 构建失败怎么办？
A: 确保已安装.NET 8.0 SDK，并检查网络连接以下载NuGet包。

### Q: 浏览器启动失败？
A: 确保系统已安装Chrome浏览器，或在BrowserConfig中指定Chrome路径。

### Q: 代理连接失败？
A: 检查proxies.txt文件格式，确保代理服务器可用。

### Q: 如何调试模块？
A: 每个模块都支持ILogger接口，配置适当的日志级别即可看到详细信息。

## 🎉 开始您的批量浏览器管理之旅！

现在您已经拥有了一套完整的、模块化的批量浏览器管理系统。每个模块都可以独立使用，也可以组合使用来构建强大的自动化解决方案。

祝您使用愉快！🚀
