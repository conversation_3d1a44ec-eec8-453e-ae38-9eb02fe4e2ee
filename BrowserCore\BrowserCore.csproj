<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>BatchBrowser.BrowserCore</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>BatchBrowser Team</Authors>
    <Description>批量浏览器核心管理模块 - 独立DLL</Description>
  </PropertyGroup>

  <ItemGroup>
    <!-- Playwright 浏览器自动化 -->
    <PackageReference Include="Microsoft.Playwright" Version="1.40.0" />
    
    <!-- JSON处理 -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    
    <!-- 日志 -->
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
  </ItemGroup>

</Project>
