{"name": "@apify/pseudo_url", "version": "2.0.59", "description": "Tool to find the matching URLs on a page (or html document) by providing a URL pattern.", "main": "./cjs/index.cjs", "module": "./esm/index.mjs", "typings": "./cjs/index.d.ts", "exports": {".": {"import": {"types": "./esm/index.d.mts", "default": "./esm/index.mjs"}, "require": {"types": "./cjs/index.d.ts", "default": "./cjs/index.cjs"}}}, "keywords": ["apify"], "author": {"name": "Apify", "email": "<EMAIL>", "url": "https://apify.com"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/apify/apify-shared-js"}, "bugs": {"url": "https://github.com/apify/apify-shared-js/issues"}, "homepage": "https://apify.com", "scripts": {"build": "npm run clean && npm run compile && npm run copy", "clean": "rimraf ./dist", "compile": "tsup", "copy": "ts-node -T ../../scripts/copy.ts"}, "publishConfig": {"access": "public"}, "dependencies": {"@apify/log": "^2.5.18"}, "gitHead": "e98bc7ac677b4f748930ffb38c587e2e59781a1b"}