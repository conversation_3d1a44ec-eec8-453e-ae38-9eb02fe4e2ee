{"name": "@crawlee/browser", "version": "3.13.7", "description": "The scalable web crawling and scraping library for JavaScript/Node.js. Enables development of data extraction and web automation jobs (not only) with headless Chrome and Puppeteer.", "engines": {"node": ">=16.0.0"}, "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": "./index.mjs", "require": "./index.js", "types": "./index.d.ts"}, "./package.json": "./package.json"}, "keywords": ["apify", "headless", "chrome", "puppeteer", "crawler", "scraper"], "author": {"name": "Apify", "email": "<EMAIL>", "url": "https://apify.com"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/apify/crawlee"}, "bugs": {"url": "https://github.com/apify/crawlee/issues"}, "homepage": "https://crawlee.dev", "scripts": {"build": "yarn clean && yarn compile && yarn copy", "clean": "rimraf ./dist", "compile": "tsc -p tsconfig.build.json && gen-esm-wrapper ./index.js ./index.mjs", "copy": "tsx ../../scripts/copy.ts"}, "publishConfig": {"access": "public"}, "dependencies": {"@apify/timeout": "^0.3.0", "@crawlee/basic": "3.13.7", "@crawlee/browser-pool": "3.13.7", "@crawlee/types": "3.13.7", "@crawlee/utils": "3.13.7", "ow": "^0.28.1", "tslib": "^2.4.0", "type-fest": "^4.0.0"}, "peerDependencies": {"playwright": "*", "puppeteer": "*"}, "peerDependenciesMeta": {"playwright": {"optional": true}, "puppeteer": {"optional": true}}, "lerna": {"command": {"publish": {"assets": []}}}, "gitHead": "f357979b66b4348730ce587edec6dcbb4e1ff26d"}